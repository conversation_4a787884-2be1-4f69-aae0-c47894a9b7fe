package com.facishare.marketing.audit.log;

import com.facishare.marketing.audit.log.model.MarketingAuditLogArg;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.MarketingAuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class MarketingAuditLogServiceImpl implements MarketingAuditLogService {
    @Override
    public void sendAuditLog(MarketingAuditLogArg arg, String topic) {
        MarketingAuditLogDTO dto = MarketingAuditLogDTO.builder()
                .createTime(arg.getCreateTime() == null ? System.currentTimeMillis() : arg.getCreateTime())
                .appName(ConfigHelper.getProcessInfo().getAppName())
                .bizName(arg.getBizName())
                .profile(ConfigHelper.getProcessInfo().getProfile())
                .podIp(ConfigHelper.getProcessInfo().getIp())
                .clientIp(arg.getClientIp())
                .traceId(arg.getTraceId() == null ? TraceContext.get().getTraceId() : arg.getTraceId())
                .ei(StringUtils.isEmpty(arg.getEi()) ? TraceContext.get().getEi() :  arg.getEi())
                .ea(StringUtils.isEmpty(arg.getEa()) ? TraceContext.get().getEa() :  arg.getEa())
                .userId(arg.getUserId())
                .status(arg.getStatus())
                .objectApiName(StringUtils.isEmpty(arg.getObjectApiName()) ? "marketing" : arg.getObjectApiName())
                .objectIds(arg.getObjectIds())
                .message(arg.getMessage())
                .messageId(arg.getMessageId())
                .extra(arg.getExtra())
                .extra1(arg.getExtra1())
                .extra2(arg.getExtra2())
                .extra3(arg.getExtra3())
                .extra4(arg.getExtra4())
                .extra5(arg.getExtra5())
                .extra6(arg.getExtra6())
                .extra7(arg.getExtra7())
                .extra8(arg.getExtra8())
                .extra9(arg.getExtra9())
                .cost(arg.getCost() == null ? 0L : arg.getCost())
                .cost1(arg.getCost1() == null ? 0L : arg.getCost1())
                .cost2(arg.getCost2() == null ? 0L : arg.getCost2())
                .cost3(arg.getCost3() == null ? 0L : arg.getCost3())
                .cost4(arg.getCost4() == null ? 0L : arg.getCost4())
                .cost5(arg.getCost5() == null ? 0L : arg.getCost5())
                .cost6(arg.getCost6() == null ? 0L : arg.getCost6())
                .cost7(arg.getCost7() == null ? 0L : arg.getCost7())
                .cost8(arg.getCost8() == null ? 0L : arg.getCost8())
                .cost9(arg.getCost9() == null ? 0L : arg.getCost9())
                .actionName(arg.getActionName())
                .build();

        BizLogClient.send(topic, Pojo2Protobuf.toMessage(dto, com.fxiaoke.log.MarketingAuditLog.class).toByteArray());
    }
}
