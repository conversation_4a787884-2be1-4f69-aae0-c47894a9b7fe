package com.facishare.marketing.audit.log;

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface MarketingAuditLog {
    String bizName() default "";

    String topic() default "marketing-audit-log";

    Class<?> entityClass() default Object.class;

    Class<? extends EntityConverter> convertClass() default EntityConverter.class;

    Class<?> resultType() default Object.class;

    /**
     * @return
     */
    String status() default "true";

    String messageId() default "";

    String message() default "";

    String ea() default "";

    String ei() default "";

    String userId() default "";

    String objectApiName() default "";

    String objectIds() default "";

    /**
     * 条件表达式为 true 才会记录日志
     */
    String condition() default "'true'";

    /**
     * 计算耗时 如果为true 则会计算 cost1,cost2,cost3,cost4 的耗时
     * cost 字段不受影响 为总耗时，只需要在上下文任意位置中记录当前系统时间即可 比如：
     * SFALogContext.putVariable("cost1", System.currentTimeMillis());
     * SFALogContext.putVariable("cost2", System.currentTimeMillis());
     * SFALogContext.putVariable("cost3", System.currentTimeMillis());
     * SFALogContext.putVariable("cost4", System.currentTimeMillis());
     * SFALogContext.putVariable("cost5", System.currentTimeMillis());
     * 计算方法：
     * cost1 = cost1 - startTime
     * cost2 = cost2 - cost1
     * cost3 = cost3 - cost2
     * cost4 = cost4 - cost3
     * cost5 = cost5 - cost4
     */
    boolean calCost() default false;

    String cost() default "";

    String cost1() default "";

    String cost2() default "";

    String cost3() default "";

    String cost4() default "";

    String cost5() default "";

    String cost6() default "";

    String cost7() default "";

    String cost8() default "";

    String cost9() default "";

    String extra() default "";

    String extra1() default "";

    String extra2() default "";

    String extra3() default "";

    String extra4() default "";

    String extra5() default "";

    String extra6() default "";

    String extra7() default "";

    String extra8() default "";

    String extra9() default "";
}
