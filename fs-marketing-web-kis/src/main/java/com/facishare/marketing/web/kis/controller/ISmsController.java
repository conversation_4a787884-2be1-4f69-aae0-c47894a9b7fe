package com.facishare.marketing.web.kis.controller;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.marketing.api.result.sms.CalcForSpendingQuotaResult;
import com.facishare.marketing.api.result.sms.GroupSendResult;
import com.facishare.marketing.api.result.sms.QuerySignatureResult;
import com.facishare.marketing.api.result.sms.QueryTemplateResult;
import com.facishare.marketing.api.result.sms.QuotaStatisticsResult;
import com.facishare.marketing.api.result.sms.SMSGrayResult;
import com.facishare.marketing.api.result.sms.ShortUrlResult;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.web.kis.arg.sms.ShortUrlArg;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Created by ranluch on 2019/3/5.
 */
@FcpService("sms")
@RequestMapping("/web/sms")
public interface ISmsController {

    @FcpMethod("gray")
    @ResponseBody
    @RequestMapping(value = "/gray", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<SMSGrayResult> gray();

    @FcpMethod("querySignature")
    @ResponseBody
    @RequestMapping(value = "/querySignature", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<QuerySignatureResult> querySignature(@RequestBody com.facishare.marketing.web.kis.arg.sms.QuerySignatureArg arg);

    @FcpMethod("queryTemplate")
    @ResponseBody
    @RequestMapping(value = "/queryTemplate", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<PageResult<QueryTemplateResult>> queryTemplate(@RequestBody com.facishare.marketing.web.kis.arg.sms.QueryTemplateArg arg);

    @FcpMethod("getShortUrl")
    @ResponseBody
    @RequestMapping(value = "/getShortUrl", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<ShortUrlResult> getShortUrl(@RequestBody ShortUrlArg arg);

    @FcpMethod("sendGroupSMS")
    @ResponseBody
    @RequestMapping(value = "/sendGroupSMS", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<GroupSendResult> sendGroupSMS(@RequestBody com.facishare.marketing.web.kis.arg.sms.GroupSenderArg arg);

    @FcpMethod("statistics")
    @ResponseBody
    @RequestMapping(value = "/statistics", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<QuotaStatisticsResult> queryStatistics(@RequestBody com.facishare.marketing.web.kis.arg.sms.QuotaStatisticsArg arg);

    @FcpMethod("calcForSpendingQuota")
    @ResponseBody
    @RequestMapping(value = "/calcForSpendingQuota", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<CalcForSpendingQuotaResult> calcForSpendingQuota(@RequestBody com.facishare.marketing.web.kis.arg.sms.CalcQuotaForGroupSMSArg arg);
}
