package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.arg.counselor.GetPageAccountArg;
import com.facishare.marketing.api.result.counselor.CounselorQRResult;
import com.facishare.marketing.api.result.counselor.WeChatAccountInfoResult;
import com.facishare.marketing.api.service.counselor.CounselorService;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.EmptyUtil;
import com.facishare.marketing.common.util.EnvironmentUtil;
import com.facishare.marketing.web.kis.arg.counselor.GetWechatAccountArg;
import com.facishare.marketing.web.kis.controller.ICounselorController;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 * @Date 2019/12/23 11:29
 * @Version 1.0
 */

@Controller
@Slf4j
public class CounselorController implements ICounselorController {
    @Value("${home.domain}")
    private String homeDomain;

    @Autowired
    private CounselorService counselorService;

    @Override
    public Result<CounselorQRResult> checkAndGetQR( ) {
        if (!EnvironmentUtil.isOuterCloud(homeDomain)) {
            return counselorService.checkAndGetQR(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
        }else {
            CounselorQRResult qrResult = new CounselorQRResult();
            qrResult.setConsultant(false);
            return Result.newSuccess(qrResult);
        }
    }

    @Override

    public Result<PageResult<WeChatAccountInfoResult>> getPageWeChatAccount(GetWechatAccountArg arg) {
        if(EmptyUtil.isNullForList(arg.getPageNum(),arg.getPageSize())){
            log.warn("CounselorController webkis getPageWechatAccount params error,{}",arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if(arg.getEa()==null||arg.getUserId()==null){
            arg.setEa(UserInfoKeeper.getEa());
            arg.setUserId(UserInfoKeeper.getFsUserId());
        }
        GetPageAccountArg getPageAccountArg= BeanUtil.copy(arg,GetPageAccountArg.class);
        return counselorService.getPageWeChatAccount(getPageAccountArg);
    }
}
