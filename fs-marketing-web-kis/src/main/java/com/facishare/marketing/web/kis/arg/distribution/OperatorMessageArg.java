package com.facishare.marketing.web.kis.arg.distribution;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
public class OperatorMessageArg implements Serializable {
	@ApiModelProperty(value = "分销申请id")
	private String distributorApplicationId;
}
