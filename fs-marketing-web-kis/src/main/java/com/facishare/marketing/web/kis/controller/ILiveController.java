package com.facishare.marketing.web.kis.controller;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.marketing.api.result.CheckMemberSubmitResult;
import com.facishare.marketing.api.result.live.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.api.arg.CheckMemberSubmitArg;
import com.facishare.marketing.web.kis.arg.LiveArg;
import com.facishare.marketing.web.kis.arg.LiveMaterialsArg;
import com.facishare.marketing.web.kis.arg.live.*;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Created by zhengh on 2020/4/2.
 */
@FcpService("live")
@RequestMapping("/web/live")
public interface ILiveController {
    @FcpMethod("getViewUrl")
    @ResponseBody
    @RequestMapping(value = "/getViewUrl", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<GetViewUrlResult> getViewUrl(@RequestBody GetLiveViewUrlArg arg);

    @FcpMethod("getLectureUrl")
    @ResponseBody
    @RequestMapping(value = "/getLectureUrl", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<LiveLectureUrlResult> getLectureUrl(@RequestBody GetLectureViewUrlArg arg);

    @FcpMethod("getDetail")
    @ResponseBody
    @RequestMapping(value = "/getDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<GetLiveDetailResult> getDetail(@RequestBody GetLiveDetailArg arg);

    @FcpMethod("getParentDetail")
    @ResponseBody
    @RequestMapping(value = "/getParentDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<GetLiveDetailResult> getParentDetail(@RequestBody GetLiveDetailArg arg);

    @FcpMethod("getViewCheckInfo")
    @ResponseBody
    @RequestMapping(value = "/getViewCheckInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<GetLiveViewCheckInfoResult> getViewCheckInfo(@RequestBody GetLiveViewCheckInfoArg arg);

    @FcpMethod("getLiveStatus")
    @ResponseBody
    @RequestMapping(value = "/getLiveStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<Integer> getLiveStatus(@RequestBody GetLiveStatusArg arg);

    @FcpMethod("checkXiaoetongSubmit")
    @ResponseBody
    @RequestMapping(value = "/checkXiaoetongSubmit",method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<CheckUserHaveSubmitResult> checkXiaoetongSubmit(@RequestBody CheckXiaoetongSubmitArg arg);

    @FcpMethod("checkAndSyncUserToXiaoetong")
    @ResponseBody
    @RequestMapping(value = "/checkAndSyncUserToXiaoetong",method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<CheckAndSyncUserToXiaoetongResult> checkAndSyncUserToXiaoetong(@RequestBody SyncSubmitUserToXiaoetongArg arg);

    @FcpMethod("xiaoetongCommonLogin")
    @ResponseBody
    @RequestMapping(value = "/xiaoetongCommonLogin",method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<XiaoetongCommonLoginResult> xiaoetongCommonLogin(@RequestBody XiaoetongCommonLoginArg arg);

    @FcpMethod("getMarketingLiveByXiaoetongId")
    @ResponseBody
    @RequestMapping(value = "/getMarketingLiveByXiaoetongId",method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<GetMarketingLiveByXiaoetongIdResult> getMarketingLiveByXiaoetongId(@RequestBody GetMarketingLiveByXiaoetongIdArg arg);

    @FcpMethod("sendXiaoetongLoginSms")
    @ResponseBody
    @RequestMapping(value = "/sendXiaoetongLoginSms",method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result sendXiaoetongLoginSms(@RequestBody SendXiaoetongLoginSmsArg arg);


    @FcpMethod("checkPolyvSubmit")
    @ResponseBody
    @RequestMapping(value = "/checkPolyvSubmit",method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<CheckAndSyncUserToXiaoetongResult> checkPolyvSubmit(@RequestBody SyncSubmitUserToXiaoetongArg arg);

    @FcpMethod("externalAuth")
    @ResponseBody
    @GetMapping(value = "/externalAuth")
    Map<String, String> externalAuth(String channelId, String userid, Long ts, String token);

    @FcpMethod("getAccountByMaterials")
    @ResponseBody
    @RequestMapping(value = "getAccountByMaterials", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<ChannelsAccountResult> getAccountByMaterials(@RequestBody LiveMaterialsArg arg);

    @FcpMethod("list")
    @ResponseBody
    @RequestMapping(value = "list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<PageResult<ListResult>> list(@RequestBody LiveArg arg);

    @FcpMethod("appList")
    @ResponseBody
    @RequestMapping(value = "appList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<PageResult<ListResult>> appList(@RequestBody LiveArg arg);

    @FcpMethod("checkMemberSubmit")
    @ResponseBody
    @RequestMapping(value = "checkMemberSubmit", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<CheckMemberSubmitResult> checkMemberSubmit(@RequestBody CheckMemberSubmitArg arg);


}
