package com.facishare.marketing.web.kis.controller;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.marketing.api.arg.BasePageArg;
import com.facishare.marketing.api.arg.momentPoster.AddPosterArg;
import com.facishare.marketing.api.result.moment.AddPosterResult;
import com.facishare.marketing.api.result.moment.GetDailyPosterInfoResult;
import com.facishare.marketing.api.result.moment.QueryEnterpriseMaterialListUnitResult;
import com.facishare.marketing.api.result.moment.QueryMostPopularMaterialListUnitResult;
import com.facishare.marketing.api.result.qr.QueryQRPosterByEaListUnitResult;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.web.kis.annotation.GetOuterIdTrigger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@FcpService("momentPoster")
@RequestMapping("/web/momentPoster")
@Api(value = "朋友圈海报")
public interface IMomentPosterController {

    @FcpMethod("getDailyPosterInfo")
    @ResponseBody
    @GetOuterIdTrigger
    @RequestMapping(value = "/getDailyPosterInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<GetDailyPosterInfoResult> getDailyPosterInfo();

    @FcpMethod("queryMostPopularMaterialList")
    @ResponseBody
    @RequestMapping(value = "/queryMostPopularMaterialList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "热门海报列表", tags = "2.3.1")
    @GetOuterIdTrigger
    Result<PageResult<QueryMostPopularMaterialListUnitResult>> queryMostPopularMaterialList(@RequestBody BasePageArg arg);

    @FcpMethod("addPoster")
    @ResponseBody
    @RequestMapping(value = "/addPoster", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<AddPosterResult> addPoster(@RequestBody AddPosterArg arg);

    @FcpMethod("queryEnterpriseMaterialList")
    @ResponseBody
    @RequestMapping(value = "/queryEnterpriseMaterialList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<PageResult<QueryEnterpriseMaterialListUnitResult>> queryEnterpriseMaterialList(@RequestBody BasePageArg arg);

    @FcpMethod("queryListByEa")
    @ResponseBody
    @RequestMapping(value = "/queryListByEa", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<PageResult<QueryQRPosterByEaListUnitResult>> queryListByEa(@RequestBody BasePageArg arg);

}
