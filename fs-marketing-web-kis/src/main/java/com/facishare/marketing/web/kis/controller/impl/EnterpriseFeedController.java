package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.result.EnterpriseFeedResult;
import com.facishare.marketing.api.service.enterpriseFeed.EnterpriseFeedService;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.QueryEnterpriseFeedArg;
import com.facishare.marketing.web.kis.controller.IEnterpriseFeedController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

@Slf4j
@Controller
public class EnterpriseFeedController implements IEnterpriseFeedController {
    @Autowired
    private EnterpriseFeedService enterpriseFeedService;

    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @Override
    public Result<PageResult<EnterpriseFeedResult>> queryEnterpriseFeed(@RequestBody QueryEnterpriseFeedArg arg) {
        com.facishare.marketing.api.arg.QueryEnterpriseFeedArg vo = BeanUtil.copy(arg, com.facishare.marketing.api.arg.QueryEnterpriseFeedArg.class);
        vo.setEa(arg.getEa());
        if (vo.getUserId() == null){
            vo.setUserId(-10000);
        }
        return enterpriseFeedService.queryEnterpriseFeedResult(vo);
    }
}
