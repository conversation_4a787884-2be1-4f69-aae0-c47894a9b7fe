package com.facishare.marketing.web.kis.arg.distribution;

import com.facishare.mankeep.common.enums.ClueQueryResultStatusEnum;
import com.facishare.marketing.api.arg.BasePageArg;
import java.util.Arrays;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by ranluch on 2018/11/26.
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class QueryDistributeClueArg extends BasePageArg {
    private String distributorId;   //分销员id
    private Integer type;           //过滤类型:0待确认,1跟进中,2无效,3赢单,4输单 5全部
    private String keyWord;         //搜索姓名关键词

    public boolean isWrongParam() {
        return StringUtils.isBlank(distributorId) || (type != null && Arrays.stream(ClueQueryResultStatusEnum.values()).noneMatch(data -> data.getType() == type));
    }
}
