package com.facishare.marketing.web.kis.arg.conference;

import com.facishare.marketing.common.typehandlers.value.TagName;
import io.netty.util.internal.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2019/07/19
 **/
@Data
public class SignInArg implements Serializable {

    @ApiModelProperty("活动id")
    private String id;

    @ApiModelProperty("手机号(通过手机号签到)")
    private String phone;

    @ApiModelProperty("邮箱(通过邮箱签到)")
    private String email;

    @ApiModelProperty("标签id")
    private String tagId;

    @ApiModelProperty("是否延迟签到,对于报名后自动签到时要为true,否则为false")
    private Boolean delaySingIn;

    public boolean isWrongParam() {
        return StringUtils.isBlank(id);
    }

}
