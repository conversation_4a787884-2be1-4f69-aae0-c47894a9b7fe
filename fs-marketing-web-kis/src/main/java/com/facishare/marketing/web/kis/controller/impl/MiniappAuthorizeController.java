package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.arg.QueryMiniAppForwardUrlArg;
import com.facishare.marketing.api.service.CustomizeMiniAuthorizeService;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.controller.IMiniappAuthorizeController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;

@Controller
@Slf4j
public class MiniappAuthorizeController implements IMiniappAuthorizeController{
    @Autowired
    private CustomizeMiniAuthorizeService customizeMiniAuthorizeService;

    @Override
    @CrossOrigin
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<String> queryMiniAppForwardUrl(@RequestBody QueryMiniAppForwardUrlArg arg) {
        if (arg == null || (!arg.isValid() && StringUtils.isEmpty(arg.getCorpId()) && StringUtils.isEmpty(arg.getEa()))){
            log.info("MiniappAuthorizeController.queryMiniAppForwardUrl arg:{}",arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (arg.getQuery() == null){
            arg.setQuery("");
        }
        return customizeMiniAuthorizeService.queryMiniAppForwardUrl(arg);
    }
}
