package com.facishare.marketing.web.kis.interceptor;

import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.web.kis.annotation.ParamCheckTrigger;
import com.google.common.collect.ImmutableSet;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;

/**
 * 参数注解若返回值非Result，则不能使用（错误返回值不同）
 * Created  By zhoux 2019/02/23
 **/
@Slf4j
public class ParamCheckInterceptor implements MethodInterceptor {

    private static final Set<String> ERROR_ARG = ImmutableSet.of("undefined");

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        Method method = invocation.getMethod();
        if (method == null) {
            log.warn("IpInterceptor.argInterceptor method is null");
        } else {
            Object[] arguments = invocation.getArguments();
            if (arguments != null && arguments.length > 0) {
                for (Object object : arguments) {
                    Field[] fields = object.getClass().getDeclaredFields();
                    boolean checkResult = checkArgMethod(fields, object);
                    if(!checkResult) {
                        log.warn("ParamCheckInterceptor.argInterceptor arg error");
                        return Result.newError(SHErrorCode.PARAMS_ERROR);
                    }
                }
            }
        }
        return invocation.proceed();
    }

    private boolean checkArgMethod(Field[] fields, Object arg) {
        for (Field field : fields) {
            try {
                boolean result = true;
                ParamCheckTrigger paramCheckTrigger =  field.getAnnotation(ParamCheckTrigger.class);
                if (paramCheckTrigger != null) {
                    boolean access = field.isAccessible();
                    if (!access) {
                        field.setAccessible(true);
                    }
                    Object object = field.get(arg);
                    // 若参数必填
                    if (paramCheckTrigger.required()) {
                        result = (object != null && ERROR_ARG.stream().noneMatch(data -> data.equals(object.toString())));
                    }
                    if (!result) {
                        if (!access) {
                            field.setAccessible(false);
                        }
                        return false;
                    }
                    if (!access) {
                        field.setAccessible(false);
                    }
                }
            } catch (Exception e) {
                log.warn("ArgInterceptor.checkArgMethod error", e);
                return false;
            }
        }
        return true;
    }
}
