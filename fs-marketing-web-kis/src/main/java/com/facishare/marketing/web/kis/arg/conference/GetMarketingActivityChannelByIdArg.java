package com.facishare.marketing.web.kis.arg.conference;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2021/07/09
 **/
@Data
public class GetMarketingActivityChannelByIdArg implements Serializable {

    @ApiModelProperty("营销活动id")
    private String marketingActivityId;

    public boolean isWrongParam() {
        return StringUtils.isBlank(marketingActivityId);
    }

}
