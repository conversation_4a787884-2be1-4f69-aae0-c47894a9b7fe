package com.facishare.marketing.web.kis.utils;

import java.lang.reflect.Method;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;

/**
 * Created  By zhoux 2019/02/23
 */
@Slf4j
public class JoinPointUtil {


    public static Method ProceedingJoinPoint2Method(ProceedingJoinPoint joinPoint) {
        Method objMethod = null;
        try {
            String methodName = joinPoint.getSignature().getName();
            Class<?> classTarget = joinPoint.getTarget().getClass();
            Class<?>[] par = ((MethodSignature) joinPoint.getSignature()).getParameterTypes();
            objMethod = classTarget.getMethod(methodName, par);
        } catch (Exception e) {
            log.warn("JoinPointUtil.ProceedingJoinPoint2Method error", e);
        }
        return objMethod;
    }

}
