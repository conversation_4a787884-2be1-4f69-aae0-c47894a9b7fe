package com.facishare.marketing.web.kis.controller.outer;

import com.facishare.marketing.api.service.AccountService;
import com.facishare.marketing.api.service.sms.SendService;
import com.facishare.marketing.common.annoation.Authentication;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.filters.IPLimiterFilter;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.PhoneNumberCheck;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.CheckSMCodeArg;
import com.facishare.marketing.web.kis.arg.SendSMCodeArg;
import com.facishare.uc.api.service.CaptchaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * Created  By zhoux 2019/06/28
 **/
@RestController
@RequestMapping("/web/account")
@Slf4j
public class OuterAccountController {

    @Autowired
    private AccountService accountService;
    @Autowired
    private SendService sendService;
    @Autowired
    private CaptchaService captchaService;
    @Autowired
    private IPLimiterFilter ipLimiterFilter;

    /**
     * 发送手机验证码
     * @param arg
     * @return
     */
    @RequestMapping(value = "sendSMCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @Authentication
    public Result sendSMCode(HttpServletRequest httpServletRequest, @RequestBody SendSMCodeArg arg) {
        if (arg.isWrongParam()) {
            log.warn("OuterArticleController.queryArticleDetail param error arg:{}", arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        // 配合 IpLimiterFilter 配置使用:
        // 1. 调用获取短信验证码接口是否异常告警
        // 2. 超出限制,返回图形验证码
        // 3. 校验图形验证码
        try {
            if (ipLimiterFilter.alarm(httpServletRequest)) {
                if (StringUtils.isEmpty(arg.getEpxId()) || StringUtils.isEmpty(arg.getCode())) {
                    return Result.newError(SHErrorCode.PHONE_NUMBER_CAPTCHA);
                }
                if (!captchaService.verify(arg.getEpxId(), arg.getCode())) {
                    return Result.newError(SHErrorCode.PHONE_NUMBER_CAPTCHA_ERROR);
                }
            }
        } catch (Exception e) {
            log.warn("captcha fail e:", e);
        }
        //验证手机格式
        if (!PhoneNumberCheck.isPhoneLegal(arg.getPhone())) {
            log.info("OuterAccountController.sendSMCode failed phone number is illegal phone:{}", arg.getPhone());
            return new Result(SHErrorCode.PHONE_NUMBER_ILLEGAL);
        }

        return accountService.sendSMCode(arg.getPhone(), arg.getObjectType(), arg.getObjectId());
    }

    /**
     * 校验手机验证码
     * @param arg
     * @return
     */
    @RequestMapping(value = "checkSMCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result checkSMCode(@RequestBody CheckSMCodeArg arg) {
        if (arg.isWrongParam()) {
            log.warn("OuterAccountController.checkSMCode param error arg:{}", arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        return accountService.checkSMCode(arg.getPhone(), arg.getVerifyCode());
    }





}
