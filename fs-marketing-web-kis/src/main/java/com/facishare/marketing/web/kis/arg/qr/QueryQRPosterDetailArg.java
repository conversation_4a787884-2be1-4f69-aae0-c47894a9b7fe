package com.facishare.marketing.web.kis.arg.qr;

import com.facishare.marketing.web.kis.annotation.ParamCheckTrigger;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryQRPosterDetailArg implements Serializable {
    @ApiModelProperty("二维码海报ID")
    @ParamCheckTrigger(required = true)
    private String qrPosterId;

    @ApiModelProperty("邀约海报 需传邀约ID")
    private String inviteId;

    @ApiModelProperty("企业ea")
    private String ea;

    @ApiModelProperty("员工id")
    private Integer userId;

    private boolean partner = false;
}
