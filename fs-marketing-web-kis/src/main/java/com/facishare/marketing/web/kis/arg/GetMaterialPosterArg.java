package com.facishare.marketing.web.kis.arg;

import com.facishare.marketing.web.kis.annotation.ParamCheckTrigger;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName GetMaterialPosterArg
 * @Description
 * <AUTHOR>
 * @Date 2019/2/26 3:49 PM
 */
@Data
public class GetMaterialPosterArg implements Serializable {

    @ParamCheckTrigger(required = true)
    private Integer objectType;

    @ParamCheckTrigger(required = true)
    private String objectId;

    @ParamCheckTrigger(required = true)
    private String feedKey;

    private String spreadTaskId;

    private String marketingActivityId;

    private boolean partner;

}
