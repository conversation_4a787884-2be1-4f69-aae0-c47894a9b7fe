package com.facishare.marketing.web.kis.controller;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.marketing.api.result.bd.GetBdSiteListResult;
import com.facishare.marketing.api.result.counselor.CounselorQRResult;
import com.facishare.marketing.api.result.counselor.WeChatAccountInfoResult;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.web.kis.arg.bd.GetBdSiteListArg;
import com.facishare.marketing.web.kis.arg.counselor.CheckAndQRArg;
import com.facishare.marketing.web.kis.arg.counselor.GetWechatAccountArg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @Date 2019/12/23 11:00
 * @Version 1.0
 */
@FcpService("counselor")
@RequestMapping("/web/counselor")
@Api(value = "counselor", tags = "counselor_3.4.1")
public interface ICounselorController {
    @FcpMethod("checkAndGetQR")
    @ResponseBody
    @ApiOperation("检查是否有专属权限并返回二维码")
    @RequestMapping(value = "/checkAndGetQR", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<CounselorQRResult> checkAndGetQR();

    @FcpMethod("getPageWeChatAccount")
    @ResponseBody
    @ApiOperation("查找关联的微信客户信息列表")
    @RequestMapping(value = "/getPageWeChatAccount", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<PageResult<WeChatAccountInfoResult>> getPageWeChatAccount(@RequestBody GetWechatAccountArg arg);

}
