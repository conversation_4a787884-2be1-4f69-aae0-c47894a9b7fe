package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.arg.ListMarketingUserGroupArg;
import com.facishare.marketing.api.result.MarketingUserGroupResult;
import com.facishare.marketing.api.service.MarketingUserGroupService;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.kis.controller.IMarketingUserGroupController;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Created by ran<PERSON>ch on 2019/3/6.
 */
@Controller
@Slf4j
public class MarketingUserGroupController implements IMarketingUserGroupController {
    @Autowired
    private MarketingUserGroupService marketingUserGroupService;

    @Override
    public Result<PageResult<MarketingUserGroupResult>> listMarketingUserGroup(@RequestBody com.facishare.marketing.web.kis.arg.ListMarketingUserGroupArg arg) {
        Preconditions.checkNotNull(arg.getPageNum(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_ACTIVITYCONTROLLER_60));
        Preconditions.checkNotNull(arg.getPageSize(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_ACTIVITYCONTROLLER_61));
        ListMarketingUserGroupArg listMarketingUserGroupArg = BeanUtil.copy(arg, ListMarketingUserGroupArg.class);
        return marketingUserGroupService.listMarketingUserGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), listMarketingUserGroupArg);
    }
}