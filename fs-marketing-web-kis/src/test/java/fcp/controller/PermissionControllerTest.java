package fcp.controller;

import com.facishare.fcp.protocol.FcpRequest;
import com.facishare.fcp.protocol.FcpResponse;
import com.facishare.marketing.api.result.kis.GetEnterpriseStatisticSumUpResult;
import com.facishare.marketing.api.result.kis.GetUserStatusResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.web.kis.arg.BaseArg;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 * Created  By zhoux 2019/02/27
 **/
@Slf4j
public class PermissionControllerTest extends BaseFcpTest{

    @Test
    public void getCurrentUserStatus() {
        BaseArg arg = new BaseArg();

        FcpRequest request = createFcpRequest("TEST.kisPermission.getCurrentUserStatus", arg);

        FcpResponse fcpResponse = client.sendFcpRequest(request);

        Result<GetUserStatusResult> result = getResult(fcpResponse);
        log.info("getCurrentUserStatus result = {}", result);

    }

}
