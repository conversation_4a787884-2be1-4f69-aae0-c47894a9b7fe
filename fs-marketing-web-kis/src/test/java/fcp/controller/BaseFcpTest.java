package fcp.controller;

import com.facishare.fcp.protocol.FcpHeader.FcpHeaderType;
import com.facishare.fcp.protocol.FcpMessageBuilder;
import com.facishare.fcp.protocol.FcpMessageType;
import com.facishare.fcp.protocol.FcpRequest;
import com.facishare.fcp.protocol.FcpResponse;
import com.facishare.fcp.serialization.SerializerType;
import com.facishare.fcp.service.FcpApplication;
import com.facishare.marketing.common.result.Result;
import fcp.SimpleFcpClient;
import org.junit.Before;

import java.util.Locale;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by yuanj on 2017/3/25.
 */
public abstract class BaseFcpTest {

    static final int ERROR_CODE_OK = 0;

    SimpleFcpClient client;

    private AtomicInteger seqId = new AtomicInteger();

    @Before
    public void setUp() throws Exception {
        // 本地 FCP Server地址
//       client = new SimpleFcpClient();

        // 113环境FCP Server地址
        client = new SimpleFcpClient("172.31.101.246", 10634);
        client.connect();
    }

    FcpRequest createFcpRequest(String queryName, Object object) {
        byte[] bytes = FcpApplication.getInstance()
                                     .getSerializerManager()
                                     .getSerializer(SerializerType.PROTO_BUF.value())
                                     .encode(object);

        return FcpMessageBuilder.createFcpRequestBuilder(FcpMessageType.QUERY)
                                .addFcpHeader(FcpHeaderType.CALL_ID, UUID.randomUUID().toString())
                                .addFcpHeader(FcpHeaderType.SEQUENCE_ID, seqId.incrementAndGet())
                                .addFcpHeader(FcpHeaderType.QUERY_NAME, queryName)
                                .addFcpHeader(FcpHeaderType.USER_INFO, "E.2.1000")
                                .addFcpHeader(FcpHeaderType.CLIENT_INFO, "iOS.100550008")
                                .addFcpHeader(FcpHeaderType.ENTERPRISE_ID, 58433L)
                                .addFcpHeader(FcpHeaderType.CONTENT_TYPE, SerializerType.PROTO_BUF.value())
                                .addFcpHeader(FcpHeaderType.LANGUAGE_LOCALE, Locale.ENGLISH.toLanguageTag())
                                .addFcpBody(bytes)
                                .build();
    }

    <T> Result<T> getResult(FcpResponse fcpResponse) {
        Result<T> result = FcpApplication.getInstance()
                                         .getSerializerManager()
                                         .getSerializer(SerializerType.PROTO_BUF.value())
                                         .decode(Result.class, fcpResponse.getMergedBinaryBody());
        return result;
    }

}
