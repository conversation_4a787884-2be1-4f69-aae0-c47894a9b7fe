package com.facishare.marketing.outapi.arg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Created on 2021-03-31.
 */
@Data
public class RecordEmployeeActionArg implements Serializable {
	private String ea;
	private Integer fsUserId;
	@ApiModelProperty("可选 web app mini_app")
	private String channel;
	@ApiModelProperty("固定为visit_page")
	private String action;
	@ApiModelProperty("固定为页面ID, 前端自己给每个页面定义一下即可")
	private String targetObjectId;
	
	public Map<String, Object> toMap(){
		Map<String, Object> map = new HashMap<>();
		map.put("ea", ea);
		map.put("fsUserId", fsUserId);
		map.put("channel", channel);
		map.put("action", action);
		map.put("targetObjectId", targetObjectId);
		return map;
	}
}
