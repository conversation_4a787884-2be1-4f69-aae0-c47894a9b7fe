package com.facishare.marketing.outapi.arg;

import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.typehandlers.value.TagName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BatchAddOrDeleteTagNamesToCrmDataArg implements Serializable {
    /**
     * CRM 对象描述ApiName
     */
    private String crmObjectDescribeApiName;

    /**
     * CRM对象ID
     */
    private List<String> crmObjectIds;

    private List<TagName> tagNames;
    /**
     * We just support customer object for now.
     */
    public boolean valid(){
        return CrmObjectApiNameEnum.CUSTOMER.getName().equals(crmObjectDescribeApiName) && crmObjectIds != null && !crmObjectIds.isEmpty() && tagNames != null && !tagNames.isEmpty();
    }
}
