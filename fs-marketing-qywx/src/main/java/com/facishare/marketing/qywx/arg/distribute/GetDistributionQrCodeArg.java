package com.facishare.marketing.qywx.arg.distribute;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang.StringUtils;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GetDistributionQrCodeArg extends QYWXBaseArg{

    @ApiModelProperty(value = "分销员id")
    private String distributorId;

    @ApiModelProperty(value = "运营员id")
    private String operatorId;

    public boolean isWrongParam() {
        return (StringUtils.isBlank(distributorId) && StringUtils.isBlank(operatorId));
    }

}
