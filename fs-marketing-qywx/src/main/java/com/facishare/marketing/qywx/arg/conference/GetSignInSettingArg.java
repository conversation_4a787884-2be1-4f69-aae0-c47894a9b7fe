package com.facishare.marketing.qywx.arg.conference;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2021/04/20
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GetSignInSettingArg extends QYWXBaseArg {

    @ApiModelProperty("会议id")
    private String activityId;

    public boolean isWrongParam() {
        return StringUtils.isBlank(activityId);
    }

}
