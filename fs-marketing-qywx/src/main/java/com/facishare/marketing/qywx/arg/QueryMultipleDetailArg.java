package com.facishare.marketing.qywx.arg;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class QueryMultipleDetailArg extends QYWXBaseArg {

    @ApiModelProperty("营销活动id")
    private String marketingActivityId;

    @ApiModelProperty("传入ea") //兼容定制钉钉全员推广,多图推广员工没有打通微信身份,导致无法从token中解析fsEa
    private String ea;
}
