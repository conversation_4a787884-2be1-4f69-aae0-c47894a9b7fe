package com.facishare.marketing.qywx.controller;

import com.facishare.marketing.api.arg.cta.*;
import com.facishare.marketing.api.result.cta.CreateCtaWxQrCodeResult;
import com.facishare.marketing.api.result.cta.QueryCtaRelationCountResult;
import com.facishare.marketing.api.result.cta.QueryCtaSimpleDetailResult;
import com.facishare.marketing.api.service.MemberService;
import com.facishare.marketing.api.service.kis.CtaService;
import com.facishare.marketing.common.enums.hexagon.MemberCheckTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.qywx.annoation.TokenCheckTrigger;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


/**
 * Created by zhengh on 2020/1/10.
 */
@RestController
@RequestMapping("/cta")
@Slf4j
public class CtaController {
    @Autowired
    private CtaService ctaService;

    @Autowired
    private MemberService memberService;

    /**
     * 获取CTA详情
     * @param arg
     * @return
     */
    @ApiOperation(value = "获取CTA详情")
    @TokenCheckTrigger
    @RequestMapping(value = "/queryCtaSimpleDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<QueryCtaSimpleDetailResult> queryCtaSimpleDetail(@RequestBody com.facishare.marketing.qywx.arg.cta.QueryCtaSimpleDetailArg arg){
        if(StringUtils.isBlank(arg.getToken())){
            log.warn("CtaController.queryCtaSimpleDetail failed token is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        if (arg == null || StringUtils.isBlank(arg.getId())){
            log.warn("CtaController.queryCtaSimpleDetail failed arg param error token:{} arg:{}", arg.getToken(), arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        QueryCtaSimpleDetailArg vo = BeanUtil.copy(arg, QueryCtaSimpleDetailArg.class);
        vo.setEa(arg.getFsEa());
        vo.setWxAppId(arg.getAppId());
        Result<QueryCtaSimpleDetailResult> result = ctaService.queryCtaSimpleDetail(arg.getFsEa(), vo);
        if(result.isSuccess() && result.getData() != null) {
            Result<String> checkMemberResult = memberService.checkWxMiniAppUserHaveMemberAuthReview(arg.getFsEa(), arg.getObjectType(), arg.getObjectId(), arg.getUid(), false);
            if(checkMemberResult.isSuccess() && StringUtils.isNotBlank(checkMemberResult.getData())) {
                result.getData().setHasMember(true);
                if(!Boolean.TRUE.equals(result.getData().getHasEnrollForm())
                        && result.getData().getMemberCheckType() != null && result.getData().getMemberCheckType().equals(MemberCheckTypeEnum.CHECK.getType())) {
                    result.getData().setHasEnrollForm(true);
                }
            } else {
                result.getData().setHasMember(false);
            }
        }
        return result;
    }

    /**
     * 生成关注公众号二维码
     * @param arg
     * @return
     */
    @ApiOperation(value = "生成关注公众号二维码")
    @TokenCheckTrigger
    @RequestMapping(value = "/createWxQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<CreateCtaWxQrCodeResult> createWxQrCode(@RequestBody com.facishare.marketing.qywx.arg.cta.CreateCtaWxQrCodeArg arg){
        if(StringUtils.isBlank(arg.getToken())){
            log.warn("CtaController.createWxQrCode failed token is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        if (arg == null || StringUtils.isBlank(arg.getCtaId())){
            log.warn("CtaController.createWxQrCode failed arg param error token:{} arg:{}", arg.getToken(), arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        CreateCtaWxQrCodeArg vo = BeanUtil.copy(arg, CreateCtaWxQrCodeArg.class);
        vo.setEa(arg.getFsEa());
        vo.setWxAppId(arg.getWxAppId());
        return ctaService.createWxQrCode(arg.getFsEa(), arg.getFsUserId(), vo);
    }

    /**
     * 生成企微添加好友二维码
     * @param arg
     * @return
     */
    @ApiOperation(value = "生成企微添加好友二维码")
    @TokenCheckTrigger
    @RequestMapping(value = "/createQywxQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<CreateCtaWxQrCodeResult> createQywxQrCode(@RequestBody com.facishare.marketing.qywx.arg.cta.CreateCtaQywxQrCodeArg arg){
        if(StringUtils.isBlank(arg.getToken())){
            log.warn("CtaController.createQywxQrCode failed token is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        if (arg == null || StringUtils.isBlank(arg.getCtaId())){
            log.warn("CtaController.createQywxQrCode failed arg param error token:{} arg:{}", arg.getToken(), arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        CreateCtaQywxQrCodeArg vo = BeanUtil.copy(arg, CreateCtaQywxQrCodeArg.class);
        vo.setEa(arg.getFsEa());
        vo.setWxAppId(arg.getWxAppId());
        return ctaService.createQywxQrCode(arg.getFsEa(), arg.getFsUserId(), vo);
    }

    /**
     * 轮询关注公众号状态
     * @param arg
     * @return
     */
    @ApiOperation(value = "轮询关注公众号状态")
    @TokenCheckTrigger
    @RequestMapping(value = "/pollingWxQrCodeStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<Boolean> pollingWxQrCodeStatus(@RequestBody com.facishare.marketing.qywx.arg.cta.PollingQrCodeStatusArg arg){
        if(StringUtils.isBlank(arg.getToken())){
            log.warn("CtaController.pollingWxQrCodeStatus failed token is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        if (arg == null || StringUtils.isBlank(arg.getQrCodeId())){
            log.warn("CtaController.pollingWxQrCodeStatus failed arg param error token:{} arg:{}", arg.getToken(), arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        PollingQrCodeStatusArg vo = BeanUtil.copy(arg, PollingQrCodeStatusArg.class);
        return ctaService.pollingWxQrCodeStatus(arg.getFsEa(), arg.getFsUserId(), vo);
    }

    /**
     * 轮询企微添加好友状态
     * @param arg
     * @return
     */
    @ApiOperation(value = "轮询企微添加好友状态")
    @TokenCheckTrigger
    @RequestMapping(value = "/pollingQywxQrCodeStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<Boolean> pollingQywxQrCodeStatus(@RequestBody com.facishare.marketing.qywx.arg.cta.PollingQrCodeStatusArg arg){
        if(StringUtils.isBlank(arg.getToken())){
            log.warn("CtaController.pollingQywxQrCodeStatus failed token is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        if (arg == null || StringUtils.isBlank(arg.getQrCodeId())){
            log.warn("CtaController.pollingQywxQrCodeStatus failed arg param error token:{} arg:{}", arg.getToken(), arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        PollingQrCodeStatusArg vo = BeanUtil.copy(arg, PollingQrCodeStatusArg.class);
        return ctaService.pollingQywxQrCodeStatus(arg.getFsEa(), arg.getFsUserId(), vo);
    }

    /**
     * 获取Cta行为数据
     * @param arg
     * @return
     */
    @ApiOperation(value = "获取Cta行为数据")
    @TokenCheckTrigger
    @RequestMapping(value = "/queryCtaStaticData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<QueryCtaRelationCountResult> queryCtaStaticData(@RequestBody com.facishare.marketing.qywx.arg.cta.QueryCtaStaticDataArg arg){
        if(StringUtils.isBlank(arg.getToken())){
            log.warn("CtaController.queryCtaStaticData failed token is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        QueryCtaStaticDataArg vo = BeanUtil.copy(arg, QueryCtaStaticDataArg.class);
        return ctaService.queryCtaStaticData(arg.getFsEa(), arg.getFsUserId(), vo);
    }
}
