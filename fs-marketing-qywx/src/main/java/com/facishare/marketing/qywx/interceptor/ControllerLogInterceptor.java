package com.facishare.marketing.qywx.interceptor;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.interceptor.LogInterceptor;
import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldAttributes;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;

import java.io.Serializable;
import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Slf4j
public class ControllerLogInterceptor implements MethodInterceptor {

    private static final Gson GSON = new GsonBuilder().addSerializationExclusionStrategy(new LogInterceptor.LogExclusionStrategy()).create();

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        long beforeTime = System.currentTimeMillis();
        try {
            Object result = invocation.proceed();
          //  Object printResult = result instanceof byte[] ? "-byte[]-" : result;

            long afterTime = System.currentTimeMillis();
//            log.info("Service finished, serviceName={}#{} args={} result={} duration={}ms",
//                invocation.getMethod().getDeclaringClass(), invocation.getMethod().getName(),
//                invocation.getArguments(), printResult, afterTime - beforeTime);
            if (invocation.getMethod().isAnnotationPresent(FilterLog.class)) {
                log.info("Service finished, serviceName={}#{} args={} duration={}ms",
                        invocation.getMethod().getDeclaringClass(), invocation.getMethod().getName(),
                        LogInterceptor.printObjs(invocation.getArguments()), afterTime - beforeTime);
            }else {
                log.info("Service finished, serviceName={}#{} args={} result={} duration={}ms",
                        invocation.getMethod().getDeclaringClass(), invocation.getMethod().getName(),
                        LogInterceptor.printObjs(invocation.getArguments()), LogInterceptor.printObjs(new Object[] {result}), afterTime - beforeTime);
            }

            return result;
        } catch (Throwable e) {
            long afterTime = System.currentTimeMillis();
//            log.error("Service exception, serviceName={}#{} args={} duration={}ms exception：",
//                invocation.getMethod().getDeclaringClass(), invocation.getMethod().getName(),
//                invocation.getArguments(), afterTime - beforeTime, e);
            log.error("Service exception, serviceName={}#{} args={} duration={}ms exception：",
                    invocation.getMethod().getDeclaringClass(), invocation.getMethod().getName(),
                    LogInterceptor.printObjs(invocation.getArguments()), afterTime - beforeTime, e);
            return null;
        } finally {
        }
    }
}
