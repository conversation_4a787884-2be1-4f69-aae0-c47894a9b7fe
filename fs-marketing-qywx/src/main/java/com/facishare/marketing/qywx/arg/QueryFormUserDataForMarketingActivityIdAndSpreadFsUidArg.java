package com.facishare.marketing.qywx.arg;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class QueryFormUserDataForMarketingActivityIdAndSpreadFsUidArg extends QYWXBaseArg {


    @ApiModelProperty("营销活动id")
    private String marketingActivityId;

    @ApiModelProperty("关键词")
    private String keyword;

    @ApiModelProperty("转发营销用户id列表")
    private List<String> fromUserMarketingIds;

    @ApiModelProperty("startDate")
    private Long startDate;

    @ApiModelProperty("endDate")
    private Long endDate;

    @ApiModelProperty("活动类型")
    private Integer actionType;

    @ApiModelProperty(value = "当前页码", required = true)
    private Integer pageNum;
    @ApiModelProperty(value = "每页数量", required = true)
    private Integer pageSize;
    @ApiModelProperty(value = "第一页请求时间戳")
    private Long time;
}
