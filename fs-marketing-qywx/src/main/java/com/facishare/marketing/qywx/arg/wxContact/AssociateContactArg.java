package com.facishare.marketing.qywx.arg.wxContact;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AssociateContactArg extends QYWXBaseArg {

    @ApiModelProperty("企业微信客户数据id")
    private String id;

    public boolean isWrongParam() {
        return StringUtils.isBlank(id);
    }

}
