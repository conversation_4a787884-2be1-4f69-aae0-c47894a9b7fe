package com.facishare.marketing.qywx.arg;

import com.facishare.marketing.api.arg.qywx.BasePageArg;
import com.facishare.marketing.common.typehandlers.value.TagName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/24 17:36
 */
@Data
public class QueryMomentSendCustomerListArg extends BasePageArg {

    @ApiModelProperty("通知发送客户")
    private List<String> userIdList;

    @ApiModelProperty("通知发送部门")
    private List<Integer> departmentIds;

    @ApiModelProperty("企微员工标签")
    private List<Integer> tagIds;

    @ApiModelProperty("企微标签列表")
    private List<TagName> tags;

}
