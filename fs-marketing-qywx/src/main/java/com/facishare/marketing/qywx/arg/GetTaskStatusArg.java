package com.facishare.marketing.qywx.arg;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/30 14:48
 */
@Data
public class GetTaskStatusArg extends QYWXBaseArg implements Serializable {

    @ApiModelProperty("推广活动id")
    private String marketingActivityId;

    @ApiModelProperty("市场活动id")
    private String marketingEventId;

    @ApiModelProperty("物料id")
    private String objectId;

    @ApiModelProperty("物料类型")
    private Integer objectType;
}

