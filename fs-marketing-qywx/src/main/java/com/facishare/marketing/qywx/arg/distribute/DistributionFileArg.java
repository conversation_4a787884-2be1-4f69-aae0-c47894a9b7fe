package com.facishare.marketing.qywx.arg.distribute;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Created by ranluch on 2018/12/5.
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DistributionFileArg extends QYWXBaseArg {
    //根文件夹
    private String folderId;
    /**
     * 文件所属 {@link com.facishare.mankeep.common.enums.FileBelongEnum}
     */
    //文件所属
    private int belong;

    @ApiModelProperty(value = "群ID")
    private String groupId;

    @ApiModelProperty(value = "分销员id，跟运营人员id 和 群id三选一")
    private String distributorId;

    @ApiModelProperty(value = "运营人员id, 跟分销员id 和 群id三选一")
    private String operatorId;
}
