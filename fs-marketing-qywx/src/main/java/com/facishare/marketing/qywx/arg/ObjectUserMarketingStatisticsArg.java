package com.facishare.marketing.qywx.arg;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("物料-营销活动统计参数")
public class ObjectUserMarketingStatisticsArg extends QYWXBaseArg implements Serializable {

    @ApiModelProperty("营销活动ID")
    private String marketingActivityId;

    @ApiModelProperty(value = "ea", hidden = true)
    private String ea;

    @ApiModelProperty(value = "userId", hidden = true)
    private Integer userId;

    @ApiModelProperty("开始时间")
    private Long startDate;

    @ApiModelProperty("结束时间")
    private Long endDate;

    @ApiModelProperty("物料id")
    private String objectId;

    @ApiModelProperty("物料类型")
    private Integer objectType;
}
