package com.facishare.marketing.qywx.arg.customerGroup;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DeleteGroupMemberTagArg extends QYWXBaseArg {
    private List<String> marketingUserIds;     //营销用户列表
    private String tag;                        //标签
}
