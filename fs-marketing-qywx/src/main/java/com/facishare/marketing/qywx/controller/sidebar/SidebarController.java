package com.facishare.marketing.qywx.controller.sidebar;

import com.facishare.mankeep.api.outService.result.file.DrawPhotoResult;
import com.facishare.mankeep.api.outService.service.OutImageService;
import com.facishare.marketing.api.arg.file.UploadFileArg;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import com.facishare.marketing.api.arg.qywx.media.GetMediaIdAndMiniInfoArg;
import com.facishare.marketing.api.arg.qywx.record.MiniAppRecordArg;
import com.facishare.marketing.api.arg.qywx.wxContact.QueryQywxGroupSendFsUserInfoArg;
import com.facishare.marketing.api.result.EnterpriseSettingsResult;
import com.facishare.marketing.api.result.MaterialShowResult;
import com.facishare.marketing.api.result.UploadFileResult;
import com.facishare.marketing.api.result.account.AccountIsApplyForKISResult;
import com.facishare.marketing.api.result.account.QywxEmployeeBindWxUserResult;
import com.facishare.marketing.api.result.file.GenerateUploadFileOmitResult;
import com.facishare.marketing.api.result.kis.CreateWXQRCodeByFeedResult;
import com.facishare.marketing.api.result.kis.GetMarketingActivityIdResult;
import com.facishare.marketing.api.result.kis.GetMiniAppInfoResult;
import com.facishare.marketing.api.result.kis.KisNoticeDetailResult;
import com.facishare.marketing.api.result.qr.CreateQRCodeResult;
import com.facishare.marketing.api.result.qr.QueryQRCodeResult;
import com.facishare.marketing.api.result.qr.QueryQRPosterDetailResult;
import com.facishare.marketing.api.result.qywx.wxContact.QueryQywxGroupSendFsUserInfoResult;
import com.facishare.marketing.api.result.taskCenter.TaskDetailResult;
import com.facishare.marketing.api.service.AccountService;
import com.facishare.marketing.api.service.FileService;
import com.facishare.marketing.api.service.SettingService;
import com.facishare.marketing.api.service.TaskCenterService;
import com.facishare.marketing.api.service.kis.KisActionService;
import com.facishare.marketing.api.service.kis.KisPermissionService;
import com.facishare.marketing.api.service.kis.SpreadTaskService;
import com.facishare.marketing.api.service.kis.SpreadWorkService;
import com.facishare.marketing.api.service.open.material.MaterialShowSettingService;
import com.facishare.marketing.api.service.qr.QRCodeService;
import com.facishare.marketing.api.service.qr.QRPosterService;
import com.facishare.marketing.api.service.qywx.QYWXContactService;
import com.facishare.marketing.api.service.qywx.QYWXMediaService;
import com.facishare.marketing.api.vo.taskCenter.PageTaskListVO;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.qywx.QywxSpreadTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.StringReplaceUtils;
import com.facishare.marketing.qywx.annoation.TokenCheckTrigger;
import com.facishare.marketing.qywx.arg.*;
import com.facishare.marketing.qywx.arg.qr.CreateQRCodeArg;
import com.facishare.marketing.qywx.arg.qr.CreateWxTemplateQrCodeByChannelQrCodeArg;
import com.facishare.marketing.qywx.arg.qr.QueryQRCodeArg;
import com.facishare.marketing.qywx.arg.qr.QueryQRPosterDetailArg;
import com.facishare.marketing.qywx.arg.taskCenter.DrawPhotoArg;
import com.facishare.marketing.qywx.arg.taskCenter.PageTaskListArg;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/sidebar")
@Slf4j
public class SidebarController {

    @Autowired
    private SpreadWorkService spreadWorkService;
    @Autowired
    private QYWXContactService qywxContactService;
    @Autowired
    private SpreadTaskService spreadTaskService;
    @Autowired
    QYWXMediaService qywxMediaService;
    @Autowired
    private OutImageService outImageService;
    @Autowired
    private TaskCenterService taskCenterService;
    @Autowired
    private MaterialShowSettingService materialShowSettingService;
    @Autowired
    private SettingService settingService;
    @Autowired
    private AccountService accountService;
    @Autowired
    private KisPermissionService kisPermissionService;
    @Autowired
    private KisActionService kisActionService;
    @Autowired
    private QRPosterService qrPosterService;
    @Autowired
    private QRCodeService qrCodeService;
    @Autowired
    private FileService fileService;

    @ApiOperation(value = "查询推广物料菜单展示列表")
    @TokenCheckTrigger
    @RequestMapping(value = "queryMaterialShowList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<List<MaterialShowResult>> queryMaterialShowList(@RequestBody QYWXBaseArg arg) {
        return materialShowSettingService.queryMaterialShowList(arg.getFsEa(), arg.getFsUserId());
    }

    @ApiOperation(value = "获取用户所在企业权限信息")
    @TokenCheckTrigger
    @RequestMapping(value = "/getEnterpriseSettings", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<EnterpriseSettingsResult> getEnterpriseSettings(@RequestBody EmployeeMsgArg arg) {
        //没有表示非员工身份,这里直接返回,
        if (StringUtils.isBlank(arg.getFsEa()) || arg.getFsUserId() == null) {
            return Result.newSuccess();
        }
        return settingService.getEnterpriseSettings(arg.getFsEa(), arg.getFsUserId());
    }

    @ApiOperation(value = "获得营销活动ID")
    @TokenCheckTrigger
    @RequestMapping(value = "/getMarketingActivityId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<GetMarketingActivityIdResult> getMarketingActivityId(@RequestBody GetMarketingActivityIdArg arg) {
        if (StringUtils.isBlank(arg.getFsEa()) || arg.getFsUserId() == null) {
            log.warn("SpreadWorkController.getMarketingActivityId identityCheckType error arg:{}", arg);
            return new Result(SHErrorCode.SUCCESS, new GetMarketingActivityIdResult());
        }
        return spreadWorkService.getMarketingActivityId(arg.getFsEa(), arg.getFsUserId(), ObjectTypeEnum.getByType(arg.getObjectType()), arg.getObjectId());
    }

    @ApiModelProperty(value = "获取企业群发负责人纷享身份")
    @TokenCheckTrigger
    @RequestMapping(value = "queryQywxGroupSendFsUserInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<QueryQywxGroupSendFsUserInfoResult> queryQywxGroupSendFsUserInfo(@RequestBody QueryQywxGroupSendFsUserInfoArg arg) {
        if (arg.isWrongParam()) {
            log.warn("MiniAppQYWXContactController.queryQywxGroupSendFsUserInfo error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (QywxSpreadTypeEnum.GROUP_SPREAD.getType() == arg.getSpreadType()) {
            return qywxContactService.queryQywxGroupSendFsUserInfo(arg);
        } else {
            return qywxContactService.queryAllSpreadSendFsUserInfo(arg);
        }
    }

    @ApiOperation(value = "KIS个人推广跳转到KIS端物料详情页")
    @TokenCheckTrigger
    @RequestMapping(value = "/getNoticeDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<KisNoticeDetailResult> getNoticeDetail(@RequestBody GetNoticeDetailArg arg) {
        if (arg.isWrongParam()) {
            log.warn("SpreadTaskController.getNoticeDetail param error arg:{}", arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        return spreadTaskService.getNoticeDetail(arg.getNoticeId());
    }

    @ApiOperation(value = "文件上传至企业微信")
    @TokenCheckTrigger
    @RequestMapping(value = "/preSendMedia", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<String> preSendMedia(@RequestBody GetMediaIdAndMiniInfoArg arg) {
        if (arg.isWrongParam()) {
            log.warn("FileController.uploadToQywx param error arg:{}");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Result<String> mediaIdOrMiniAppId = qywxMediaService.getMediaIdOrMiniAppId(arg);
        if (null == mediaIdOrMiniAppId || !mediaIdOrMiniAppId.isSuccess()) {
            return Result.newError(mediaIdOrMiniAppId.getErrCode(), mediaIdOrMiniAppId.getErrMsg());
        }
        return Result.newSuccess(mediaIdOrMiniAppId.getData());
    }

    @ApiOperation(value = "企业微信推广或sop员工发送记录统计")
    @TokenCheckTrigger
    @RequestMapping(value = "/qywxTaskSendRecord", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<Void> qywxTaskSendRecord(@RequestBody QywxzTaskSendRecordlArg arg) {
        if (arg.isWrongParam()) {
            log.warn("SpreadTaskController.qywxTaskSendRecord param error arg:{}", arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        return spreadTaskService.qywxTaskSendRecord(arg.getFsEa(), arg.getFsUserId(), arg.getExternalUserId(), arg.getQyUserId(), arg.getMarketingActivityId(), arg.getTriggerTaskInstanceId(), arg.getType());
    }

    @ApiOperation(value = "重绘封面图")
    @TokenCheckTrigger
    @RequestMapping(value = "/drawPhotos", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<DrawPhotoResult> drawPhotos(@RequestBody DrawPhotoArg arg) {
        if (arg.isWrongParam()) {
            log.warn("TaskCenterController.drawPhotos param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        DrawPhotoResult data = outImageService.drawPhotos(arg.getPhotoUrl());
        if (data == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        return Result.newSuccess(data);
    }

    @ApiOperation(value = "任务中心列表")
    @TokenCheckTrigger
    @RequestMapping(value = "/pageSpreadTaskList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<PageResult<TaskDetailResult>> pageSpreadTaskList(@RequestBody PageTaskListArg arg) {
        if (arg.isWrongParam()) {
            log.warn("TaskCenterController.pageTaskList param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        PageTaskListVO vo = new PageTaskListVO();
        vo.setFromQywx(true);
        vo.setEa(arg.getFsEa());
        vo.setUserId(arg.getFsUserId());
        vo.setPageNum(arg.getPageNum());
        vo.setPageSize(arg.getPageSize());
        vo.setExternalUserId(arg.getExternalUserId());
        vo.setQyUserId(arg.getQyUserId());
        vo.setChatType(arg.getChatType());
        return taskCenterService.pageSpreadTaskList(vo);
    }

    @ApiOperation(value = "查询企业微信员工H5和个人微信绑定情况")
    @TokenCheckTrigger
    @RequestMapping(value = "/queryQywxUserBindWxUserInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<QywxEmployeeBindWxUserResult> queryQywxUserBindWxUserInfo(@RequestBody QYWXBaseArg arg){
//        if(StringUtils.isBlank(arg.getToken())){
//            log.warn("AccountController.isApply failed token is null");
//            return new Result(SHErrorCode.PARAMS_ERROR);
//        }
//        if (arg == null){
//            log.warn("ArticleController.isApply failed arg param error token:{}", arg.getToken());
//            return new Result(SHErrorCode.PARAMS_ERROR);
//        }
        return accountService.queryQywxH5UserBindWxUserInfo(arg.getFsEa(), arg.getFsUserId(), arg.getCorpId(), arg.getQyUserId(), arg.getAppId());
    }

    @ApiOperation(value = "获取企业绑定小程序appId")
    @TokenCheckTrigger
    @RequestMapping(value = "/getMiniAppInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetMiniAppInfoResult> getMiniAppInfo(@RequestBody QYWXBaseArg arg) {
        Result<GetMiniAppInfoResult> miniAppInfoResult = kisPermissionService.getMiniAppInfo(arg.getFsEa());
        if (miniAppInfoResult.getData() == null){
            return miniAppInfoResult;
        }
        //如果企业还是绑定的客脉，切换到客脉pro
        if (org.apache.commons.lang3.StringUtils.equals(miniAppInfoResult.getData().getAppId(), WxAppInfoEnum.Mankeep.getAppId())){
            kisPermissionService.updateMiniApp(arg.getFsEa(), WxAppInfoEnum.MankeepPro.getAppId());
        }else{
            return miniAppInfoResult;
        }

        return kisPermissionService.getMiniAppInfo(arg.getFsEa());
    }

    @ApiOperation(value = "判断开通状况")
    @TokenCheckTrigger
    @RequestMapping(value = "/isApply", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<AccountIsApplyForKISResult> isApply(@RequestBody QYWXBaseArg arg){
//        if(StringUtils.isBlank(arg.getToken())){
//            log.warn("AccountController.isApply failed token is null");
//            return new Result(SHErrorCode.PARAMS_ERROR);
//        }
        if (arg == null){
            log.warn("ArticleController.isApply failed arg param error token:{}", arg.getToken());
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        return accountService.isApplyForQyWxKIS(arg.getFsEa(), arg.getFsUserId(), arg.getUid(), arg.getAppId());
    }

    @ApiOperation(value = "数据埋点")
    @TokenCheckTrigger
    @RequestMapping(value = "/record", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result record(@RequestBody ActionRecordArg arg) {
//        if (StringUtils.isBlank(arg.getToken())) {
//            log.warn("ActionRecordController.record failed token is null");
//            return new Result(SHErrorCode.PARAMS_ERROR);
//        }
        if (arg == null || !arg.checkParamValid()) {
            log.warn("ActionRecordController.record failed arg param error token:{} arg:{}", arg.getToken(), arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }

        RecordActionArg vo = BeanUtil.copy(arg, RecordActionArg.class);
        vo.setFsUserId(arg.getFsUserId());
        vo.setEa(arg.getFsEa());
        if (arg.getChannelType() == null) {
            vo.setChannelType(MarketingUserActionChannelType.MANKEEP.getChannelType());
        }
        arg.setUserAgent(arg.getUserAgent());
        return kisActionService.record(vo);
    }

    @ApiOperation(value = "查询海报详情")
    @TokenCheckTrigger
    @RequestMapping(value = "/queryDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<QueryQRPosterDetailResult> queryDetail(@RequestBody QueryQRPosterDetailArg arg) {
        if (arg.isWrongParam()) {
            log.warn("QRPosterController.queryDetail param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        if (arg.getNeedAvatar() == null) {
            arg.setNeedAvatar(true);
        }
        String ea = StringUtils.isBlank(arg.getFsEa()) ? arg.getEa() : arg.getFsEa();
        Integer fsUserId = arg.getFsUserId() == null ? arg.getUserId() : arg.getFsUserId();
        return qrPosterService.queryDetail(ea, fsUserId, arg.getQrPosterId(), arg.getInviteId(), arg.getNeedAvatar());
    }

    @TokenCheckTrigger
    @RequestMapping(value = "/createWxTemplateQrCodeByChannelQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<String> createWxTemplateQrCodeByChannelQrCode(@RequestBody CreateWxTemplateQrCodeByChannelQrCodeArg arg){
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getWxAppId()));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getChannelQrCodeId()));
        //Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getMarketingActivityId()));
        String ea = org.apache.commons.lang3.StringUtils.isBlank(arg.getFsEa()) ? arg.getEa() : arg.getFsEa();
        Integer fsUserId = arg.getFsUserId() == null ? arg.getUserId() : arg.getFsUserId();
        return qrCodeService.createWxTemplateQrCodeByChannelQrCode(ea, arg.getWxAppId(), arg.getChannelQrCodeId(), fsUserId, arg.getMarketingEventId(), arg.getMarketingActivityId());
    }

    @TokenCheckTrigger
    @RequestMapping(value = "queryQRCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询二维码")
    public Result<QueryQRCodeResult> queryQRCode(@RequestBody QueryQRCodeArg arg) {
        return qrCodeService.queryQRCode(arg.getId(), arg.getAuthCode());
    }

    @ApiOperation(value = "创建带feed小程序专属码")
    @TokenCheckTrigger
    @RequestMapping(value = "/createWXQRCodeByFeed", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<CreateWXQRCodeByFeedResult> createWXQRCodeByFeed(@RequestBody CreateWXQRCodeByFeedArg arg) {
        if (arg.isWrongParam()) {
            log.warn("SpreadWorkController.createWXQRCodeByFeed failed token is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        String ea = org.apache.commons.lang3.StringUtils.isBlank(arg.getFsEa()) ? arg.getEa() : arg.getFsEa();
        Integer fsUserId = arg.getFsUserId() == null ? arg.getUserId() : arg.getFsUserId();
        return spreadWorkService.createWXQRCodeByFeed(ea, fsUserId, ObjectTypeEnum.getByType(arg.getObjectType()), arg.getObjectId(), arg.getFeedKey(), arg.getMarketingActivityId(), true, arg.getValue(), true);
    }

    @TokenCheckTrigger
    @RequestMapping(value = "createQRCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "创建二维码")
    public Result<CreateQRCodeResult> createQRCode(@RequestBody CreateQRCodeArg arg) {
        String ea = org.apache.commons.lang3.StringUtils.isBlank(arg.getFsEa()) ? arg.getEa() : arg.getFsEa();
        Integer fsUserId = arg.getFsUserId() == null ? arg.getUserId() : arg.getFsUserId();
        return qrCodeService.createQRCode(ea, fsUserId, arg.getType(), arg.getValue(), arg.getLengthOfSide(), null);
    }

    @ApiOperation(value = "获取文件签名上传信息")
    @TokenCheckTrigger
    @RequestMapping(value = "/generateUploadFileOmit", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GenerateUploadFileOmitResult> generateUploadFileOmit(@RequestBody GenerateUploadFileOmitArg arg) {
        if (arg.isWrongParam()) {
            log.warn("FileController.generateUploadFileOmit param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = StringUtils.isBlank(arg.getEa()) ? arg.getFsEa() : arg.getEa();
        if (StringUtils.isBlank(ea)) {
            log.warn("FileController.generateUploadFileOmit ea is null arg:{}", arg);
        }
        Result<GenerateUploadFileOmitResult> uploadFileOmitResult = fileService.generateUploadFileOmit(ea, arg.getResourceType(), arg.getFilename(), arg.getExtension(), arg.getFileSize());
        if (!uploadFileOmitResult.isSuccess()) {
            return Result.newError(uploadFileOmitResult.getErrCode(), uploadFileOmitResult.getErrMsg());
        }

        return Result.newSuccess(uploadFileOmitResult.getData());
    }
}
