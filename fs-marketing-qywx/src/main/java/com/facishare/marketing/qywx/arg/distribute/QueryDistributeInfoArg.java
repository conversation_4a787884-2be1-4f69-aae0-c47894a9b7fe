package com.facishare.marketing.qywx.arg.distribute;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Created by ranluch on 2018/12/5.
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class QueryDistributeInfoArg extends QYWXBaseArg {
    @ApiModelProperty(value = "分销员id")
    private String distributorId;
}
