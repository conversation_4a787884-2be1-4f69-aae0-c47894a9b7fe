package com.facishare.marketing.qywx.arg;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


/**
 * Created by zhengh on 2020/1/10.
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ListArticleArg extends QYWXBaseArg {
    // 文章状态 -1:未启用 1:已启用 4:已停用
    @ApiModelProperty("文章状态")
    private Integer status;

    @ApiModelProperty("文章标题")
    private String title;

    @ApiModelProperty("每页返回数量")
    private Integer pageSize;

    @ApiModelProperty("当前页码")
    private Integer pageNum;

    @ApiModelProperty("文章类型")
    private Integer type;

    @ApiModelProperty("分组ID")
    private String groupId;

    @ApiModelProperty("菜单id")
    private String menuId;

    // 标签过滤
    private MaterialTagFilterArg materialTagFilter;

    public boolean checkParamValid(){
        if (pageNum == null || pageSize == null){
            return false;
        }
        return true;
    }
}
