package com.facishare.marketing.qywx.arg.distribute;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Created by zhengh on 2019/4/8.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QueryDistributorRecruitArg extends QYWXBaseArg {
    private String distributorId;  //分销人员的id

    private Integer status;        //招募人员状态

    private String keyWord;          //搜索关键字

    private Integer pageNum;        //当前页码

    private Integer pageSize;       //每页数量

    private Long time;              //第一页请求时间
}
