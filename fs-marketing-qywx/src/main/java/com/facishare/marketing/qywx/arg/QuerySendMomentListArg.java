package com.facishare.marketing.qywx.arg;

import com.facishare.marketing.api.arg.qywx.BasePageArg;
import com.facishare.marketing.common.typehandlers.value.TagName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class QuerySendMomentListArg extends BasePageArg {

    @ApiModelProperty("员工id")
    private String userId;

    @ApiModelProperty("标签列表")
    private List<TagName> tags;
}
