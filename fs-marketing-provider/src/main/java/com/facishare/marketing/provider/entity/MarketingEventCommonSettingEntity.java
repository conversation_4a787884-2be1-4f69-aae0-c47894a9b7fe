package com.facishare.marketing.provider.entity;

import com.facishare.marketing.common.enums.MarketingEventEnum;
import com.facishare.marketing.common.enums.MarketingEventMappingTypeEnum;
import com.facishare.marketing.common.typehandlers.value.marketingEventCommonSetting.ActivityTypeMapping;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created  By zhoux 2021/03/08
 **/
@Data
public class MarketingEventCommonSettingEntity implements Serializable {

    private String ea;

    private ActivityTypeMapping activityTypeMapping;

    private Integer createBy;

    private Date createTime;

    private Date updateTime;

    private Boolean openMergePhone;

    private Boolean marketingEventAudit;

    private Boolean marketingActivityAudit;

    public void initActivityTypeMapping() {
        ActivityTypeMapping activityTypeMapping = new ActivityTypeMapping();
        // 直播营销
        ActivityTypeMapping.ActivityTypeMappingDetail liveMarketingDetail = new ActivityTypeMapping.ActivityTypeMappingDetail();
        liveMarketingDetail.setActivityType(MarketingEventMappingTypeEnum.LIVE_MARKETING.getType());
        // 直播营销——公开课直播
        ActivityTypeMapping.MappingDetail openClassDetail = new ActivityTypeMapping.MappingDetail();
        openClassDetail.setApiName(MarketingEventEnum.LIVE_OPEN_CLASS.getEventType());
        openClassDetail.setFieldName(MarketingEventEnum.LIVE_OPEN_CLASS.getValue());
        // 直播营销——在线沙龙
        ActivityTypeMapping.MappingDetail onlineSalonDetail = new ActivityTypeMapping.MappingDetail();
        onlineSalonDetail.setApiName(MarketingEventEnum.ONLINE_SALON.getEventType());   
        onlineSalonDetail.setFieldName(MarketingEventEnum.ONLINE_SALON.getValue());
        // 直播营销——线上活动直播
        ActivityTypeMapping.MappingDetail onlineActivityLiveDetail = new ActivityTypeMapping.MappingDetail();
        onlineActivityLiveDetail.setApiName(MarketingEventEnum.ONLINE_ACTIVITY_LIVE.getEventType());
        onlineActivityLiveDetail.setFieldName(MarketingEventEnum.ONLINE_ACTIVITY_LIVE.getValue());
        liveMarketingDetail.setMapping(Lists.newArrayList(openClassDetail, onlineSalonDetail, onlineActivityLiveDetail));
        activityTypeMapping.add(liveMarketingDetail);
        // 会议营销
        ActivityTypeMapping.ActivityTypeMappingDetail meetingMarketingDetail = new ActivityTypeMapping.ActivityTypeMappingDetail();
        meetingMarketingDetail.setActivityType(MarketingEventMappingTypeEnum.MEETING_MARKETING.getType());
        // 会议营销——线下沙龙
        ActivityTypeMapping.MappingDetail offlineSalonDetail = new ActivityTypeMapping.MappingDetail();
        offlineSalonDetail.setApiName(MarketingEventEnum.OFFLINE_SALON.getEventType());
        offlineSalonDetail.setFieldName(MarketingEventEnum.OFFLINE_SALON.getValue());
        // 会议营销——专家论坛
        ActivityTypeMapping.MappingDetail expertForumDetail = new ActivityTypeMapping.MappingDetail();
        expertForumDetail.setApiName(MarketingEventEnum.EXPERT_FORUM.getEventType());
        expertForumDetail.setFieldName(MarketingEventEnum.EXPERT_FORUM.getValue());
        // 会议营销——研讨会
        ActivityTypeMapping.MappingDetail seminarDetail = new ActivityTypeMapping.MappingDetail();
        seminarDetail.setApiName(MarketingEventEnum.SEMINAR.getEventType());
        seminarDetail.setFieldName(MarketingEventEnum.SEMINAR.getValue());
        // 会议营销——交流会
        ActivityTypeMapping.MappingDetail exchangeMeetingDetail = new ActivityTypeMapping.MappingDetail();
        exchangeMeetingDetail.setApiName(MarketingEventEnum.EXCHANGE_MEETING.getEventType());
        exchangeMeetingDetail.setFieldName(MarketingEventEnum.EXCHANGE_MEETING.getValue());
        // 会议营销——行业展会
        ActivityTypeMapping.MappingDetail industryExhibitionDetail = new ActivityTypeMapping.MappingDetail();
        industryExhibitionDetail.setApiName(MarketingEventEnum.INDUSTRY_EXHIBITION.getEventType());
        industryExhibitionDetail.setFieldName(MarketingEventEnum.INDUSTRY_EXHIBITION.getValue());
        meetingMarketingDetail.setMapping(Lists.newArrayList(offlineSalonDetail, expertForumDetail, seminarDetail, exchangeMeetingDetail, industryExhibitionDetail));
        activityTypeMapping.add(meetingMarketingDetail);
        //多活动组合
        ActivityTypeMapping.ActivityTypeMappingDetail multiMarketingDetail = new ActivityTypeMapping.ActivityTypeMappingDetail();
        multiMarketingDetail.setActivityType(MarketingEventMappingTypeEnum.MULTI_MARKETING.getType());
        // 多活动组合——产品发布会
        ActivityTypeMapping.MappingDetail productLaunchDetail = new ActivityTypeMapping.MappingDetail();
        productLaunchDetail.setApiName(MarketingEventEnum.PRODUCT_LAUNCH.getEventType());
        productLaunchDetail.setFieldName(MarketingEventEnum.PRODUCT_LAUNCH.getValue());
         // 多活动组合——品牌系列活动
         ActivityTypeMapping.MappingDetail brandSeriesActivityDetail = new ActivityTypeMapping.MappingDetail();
         brandSeriesActivityDetail.setApiName(MarketingEventEnum.BRAND_SERIES_ACTIVITY.getEventType());
         brandSeriesActivityDetail.setFieldName(MarketingEventEnum.BRAND_SERIES_ACTIVITY.getValue());
         // 多活动组合——大型展会
         ActivityTypeMapping.MappingDetail largeScaleExhibitionDetail = new ActivityTypeMapping.MappingDetail();
         largeScaleExhibitionDetail.setApiName(MarketingEventEnum.LARGE_SCALE_EXHIBITION.getEventType());
         largeScaleExhibitionDetail.setFieldName(MarketingEventEnum.LARGE_SCALE_EXHIBITION.getValue());
         // 多活动组合——企业年会
         ActivityTypeMapping.MappingDetail enterpriseAnnualMeetingDetail = new ActivityTypeMapping.MappingDetail();
         enterpriseAnnualMeetingDetail.setApiName(MarketingEventEnum.ENTERPRISE_ANNUAL_MEETING.getEventType());
         enterpriseAnnualMeetingDetail.setFieldName(MarketingEventEnum.ENTERPRISE_ANNUAL_MEETING.getValue());
         multiMarketingDetail.setMapping(Lists.newArrayList(enterpriseAnnualMeetingDetail, productLaunchDetail, brandSeriesActivityDetail, largeScaleExhibitionDetail));
         activityTypeMapping.add(multiMarketingDetail);
         
         // 线上营销
         ActivityTypeMapping.ActivityTypeMappingDetail onlineMarketingDetail = new ActivityTypeMapping.ActivityTypeMappingDetail();
         onlineMarketingDetail.setActivityType(MarketingEventMappingTypeEnum.CONTENT_MARKETING.getType());
         // 线上营销——日常内容推广
         ActivityTypeMapping.MappingDetail dailyContentPromotionDetail = new ActivityTypeMapping.MappingDetail();
         dailyContentPromotionDetail.setApiName(MarketingEventEnum.DAILY_CONTENT_PROMOTION.getEventType());
         dailyContentPromotionDetail.setFieldName(MarketingEventEnum.DAILY_CONTENT_PROMOTION.getValue());
         // 线上营销——签约喜报
         ActivityTypeMapping.MappingDetail signingContractNewsDetail = new ActivityTypeMapping.MappingDetail();
         signingContractNewsDetail.setApiName(MarketingEventEnum.SIGNING_CONTRACT_NEWS.getEventType());
         signingContractNewsDetail.setFieldName(MarketingEventEnum.SIGNING_CONTRACT_NEWS.getValue());
         // 线上营销——白皮书发布
         ActivityTypeMapping.MappingDetail whitePaperReleaseDetail = new ActivityTypeMapping.MappingDetail();
         whitePaperReleaseDetail.setApiName(MarketingEventEnum.WHITE_PAPER_RELEASE.getEventType());
         whitePaperReleaseDetail.setFieldName(MarketingEventEnum.WHITE_PAPER_RELEASE.getValue());
         onlineMarketingDetail.setMapping(Lists.newArrayList(dailyContentPromotionDetail, signingContractNewsDetail, whitePaperReleaseDetail));
         activityTypeMapping.add(onlineMarketingDetail);
         // 周期性目标人群运营
         ActivityTypeMapping.ActivityTypeMappingDetail periodicityCrowdMarketingDetail = new ActivityTypeMapping.ActivityTypeMappingDetail();
         periodicityCrowdMarketingDetail.setActivityType(MarketingEventMappingTypeEnum.PERIODICITY.getType());
         // 周期性目标人群运营——周期性目标人群运营
         ActivityTypeMapping.MappingDetail periodicityCrowdMarketingDetail2 = new ActivityTypeMapping.MappingDetail();
         periodicityCrowdMarketingDetail2.setApiName(MarketingEventEnum.TARGET_CROWD_OPERATION_PERIODICITY.getEventType());
         periodicityCrowdMarketingDetail2.setFieldName(MarketingEventEnum.TARGET_CROWD_OPERATION_PERIODICITY.getValue());
         periodicityCrowdMarketingDetail.setMapping(Lists.newArrayList(periodicityCrowdMarketingDetail2));
         activityTypeMapping.add(periodicityCrowdMarketingDetail);
         // 单次目标人群运营
         ActivityTypeMapping.ActivityTypeMappingDetail onceCrowdMarketingDetail = new ActivityTypeMapping.ActivityTypeMappingDetail();
         onceCrowdMarketingDetail.setActivityType(MarketingEventMappingTypeEnum.ONCE.getType());
         // 单次目标人群运营——单次目标人群运营
         ActivityTypeMapping.MappingDetail onceCrowdMarketingDetail2 = new ActivityTypeMapping.MappingDetail();
         onceCrowdMarketingDetail2.setApiName(MarketingEventEnum.TARGET_CROWD_OPERATION_ONCE.getEventType());
         onceCrowdMarketingDetail2.setFieldName(MarketingEventEnum.TARGET_CROWD_OPERATION_ONCE.getValue());
         onceCrowdMarketingDetail.setMapping(Lists.newArrayList(onceCrowdMarketingDetail2));
         activityTypeMapping.add(onceCrowdMarketingDetail);
         
        this.activityTypeMapping = activityTypeMapping;
        this.openMergePhone = false;
        this.marketingEventAudit = false;
        this.marketingActivityAudit = false;
    }

}