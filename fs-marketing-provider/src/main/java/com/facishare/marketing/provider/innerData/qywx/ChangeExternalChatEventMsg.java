package com.facishare.marketing.provider.innerData.qywx;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
public class ChangeExternalChatEventMsg extends QywxCallBackEventMsg {
    @XStreamAlias("FromUserName")
    private String fromUserName;
    @XStreamAlias("CreateTime")
    private Long createTime;
    @XStreamAlias("MsgType")
    private String msgType;
    @XStreamAlias("ChatId")
    private String chatId;
    @XStreamAlias("UpdateDetail")
    private String updateDetail;
    @XStreamAlias("JoinScene")
    private String joinScene;
    @XStreamAlias("QuitScene")
    private String quitScene;
    @XStreamAlias("MemChangeCnt")
    private String memChangeCnt;
}
