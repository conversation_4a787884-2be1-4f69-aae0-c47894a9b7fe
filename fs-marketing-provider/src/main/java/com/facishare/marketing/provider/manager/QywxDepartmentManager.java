package com.facishare.marketing.provider.manager;

import com.alibaba.fastjson.JSONArray;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.qywx.QywxDepartmentDAO;
import com.facishare.marketing.provider.entity.qywx.QywxDepartmentEntity;
import com.facishare.marketing.provider.innerData.qywx.ChangeContactEventMsg;
import com.facishare.marketing.provider.innerResult.qywx.Department;
import com.facishare.marketing.provider.innerResult.qywx.DepartmentListResult;
import com.facishare.marketing.provider.innerResult.qywx.SingleDepartmentResult;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component("qywxDepartmentManager")
public class QywxDepartmentManager {

    @Autowired
    private QywxDepartmentDAO qywxDepartmentDAO;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private RedisManager redisManager;


    public void initQywxDepartment(String ea) {
        try {
            boolean lock = redisManager.lockInitQywxDepartment(ea);
            if (!lock) {
                log.warn("QywxDepartmentManager.initQywxDepartment ea:{} lock failed", ea);
                return;
            }
            String accessToken = qywxManager.getAccessToken(ea);
            if (StringUtils.isBlank(accessToken)) {
                log.warn("QywxDepartmentManager.initQywxDepartment ea:{} accessToken is blank", ea);
                return;
            }
            DepartmentListResult departmentListResult = qywxManager.queryDepartment(accessToken);
            if (departmentListResult == null) {
                log.error("QywxDepartmentManager.initQywxDepartment ea:{} departmentListResult is null", ea);
                return;
            }
            if (CollectionUtils.isEmpty(departmentListResult.getDepartmentList())) {
                log.warn("QywxDepartmentManager.initQywxDepartment ea:{} departmentListResult is empty, delete all data", ea);
                qywxDepartmentDAO.deleteEa(ea);
                return;
            }
            Lists.partition(departmentListResult.getDepartmentList(), 100).forEach(e -> createOrUpdate(ea, e));
            // 删除已经不存在的部门
            List<Integer> allQywxIdList = qywxDepartmentDAO.findAllQywxId(ea);
            if (CollectionUtils.isEmpty(allQywxIdList)) {
                return;
            }
            Set<Integer> latestQywxIdSet = departmentListResult.getDepartmentList().stream().map(Department::getId).collect(Collectors.toSet());
            List<Integer> needDeletedIdList = allQywxIdList.stream().filter(e -> !latestQywxIdSet.contains(e)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(needDeletedIdList)) {
                return;
            }
            qywxDepartmentDAO.deleteByQywxIdList(ea, needDeletedIdList);
        } catch (Exception e) {
            log.error("QywxDepartmentManager.initQywxDepartment error, ea: {}", ea, e);
        } finally {
            redisManager.unlockInitQywxDepartment(ea);
        }
    }

    private void createOrUpdate(String ea, List<Department> newDepartmentList) {
        if (CollectionUtils.isEmpty(newDepartmentList)) {
            return;
        }
        List<Integer> newQywxIdList = newDepartmentList.stream().map(Department::getId).collect(Collectors.toList());
        List<QywxDepartmentEntity> qywxDepartmentEntityList = qywxDepartmentDAO.findByQywxIdList(ea, newQywxIdList);
        Map<Integer, QywxDepartmentEntity> oldDepartmentEntityMap = qywxDepartmentEntityList.stream().collect(Collectors.toMap(QywxDepartmentEntity::getQywxId, e -> e, (v1, v2) -> v1));
        List<QywxDepartmentEntity> insertList = Lists.newArrayList();
        List<QywxDepartmentEntity> updateList = Lists.newArrayList();
        for (Department newDepartment : newDepartmentList) {
            int newQywxId = newDepartment.getId();
            QywxDepartmentEntity oldDepartmentEntity = oldDepartmentEntityMap.get(newQywxId);
            // 不存在则插入
            if (oldDepartmentEntity == null) {
                QywxDepartmentEntity qywxDepartmentEntity = buildEntity(ea, newDepartment);
                insertList.add(qywxDepartmentEntity);
                continue;
            }
            // 存在且信息有变更的则更新
            if (fillPropertiesIfChanged(newDepartment, oldDepartmentEntity)) {
                updateList.add(oldDepartmentEntity);
            }
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            qywxDepartmentDAO.batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            qywxDepartmentDAO.batchUpdate(ea, updateList);
        }
    }

    private static boolean fillPropertiesIfChanged(Department newDepartment, QywxDepartmentEntity oldDepartmentEntity) {
        boolean needUpdate = false;
        if (!StringUtils.equals(newDepartment.getName(), oldDepartmentEntity.getName())) {
            oldDepartmentEntity.setName(newDepartment.getName());
            needUpdate = true;
        }
        if (!StringUtils.equals(newDepartment.getNameEn(), oldDepartmentEntity.getEnglishName())) {
            oldDepartmentEntity.setEnglishName(newDepartment.getNameEn());
            needUpdate = true;
        }
        if (oldDepartmentEntity.getParentId() == null || !oldDepartmentEntity.getParentId().equals(newDepartment.getParentId())) {
            oldDepartmentEntity.setParentId(newDepartment.getParentId());
            needUpdate = true;
        }
        if (oldDepartmentEntity.getQywxOrder() == null || !oldDepartmentEntity.getQywxOrder().equals(newDepartment.getOrder())) {
            oldDepartmentEntity.setQywxOrder(newDepartment.getOrder());
            needUpdate = true;
        }
        if (StringUtils.isBlank(oldDepartmentEntity.getDepartmentLeader())) {
            oldDepartmentEntity.setDepartmentLeader(JsonUtil.toJson(newDepartment.getDepartmentLeaderList()));
            needUpdate = true;
        } else if (CollectionUtils.isEmpty(newDepartment.getDepartmentLeaderList())) {
            oldDepartmentEntity.setDepartmentLeader("[]");
            needUpdate = true;
        } else {
            Set<String> oldLeaderSet = Sets.newHashSet(JSONArray.parseArray(oldDepartmentEntity.getDepartmentLeader(), String.class));
            for (String newLeaderId : newDepartment.getDepartmentLeaderList()) {
                if (!oldLeaderSet.contains(newLeaderId)) {
                    oldDepartmentEntity.setDepartmentLeader(JsonUtil.toJson(newDepartment.getDepartmentLeaderList()));
                    needUpdate = true;
                    break;
                }
            }
        }
        return needUpdate;
    }

    private static QywxDepartmentEntity buildEntity(String ea, Department newDepartment) {
        QywxDepartmentEntity qywxDepartmentEntity = new QywxDepartmentEntity();
        qywxDepartmentEntity.setEa(ea);
        qywxDepartmentEntity.setId(UUIDUtil.getUUID());
        qywxDepartmentEntity.setQywxId(newDepartment.getId());
        qywxDepartmentEntity.setName(StringUtils.isBlank(newDepartment.getName()) ? " " : newDepartment.getName());
        qywxDepartmentEntity.setEnglishName(newDepartment.getNameEn());
        qywxDepartmentEntity.setDepartmentLeader(JsonUtil.toJson(newDepartment.getDepartmentLeaderList()));
        qywxDepartmentEntity.setParentId(newDepartment.getParentId());
        qywxDepartmentEntity.setQywxOrder(newDepartment.getOrder());
        return qywxDepartmentEntity;
    }

    public void handleDepartmentChange(String ea, ChangeContactEventMsg eventMsg) {
        String uniqueKey = getLockUniqueKey(eventMsg);
        if (StringUtils.isBlank(uniqueKey)) {
            return;
        }
        boolean lock = redisManager.lockQywxDepartmentChange(ea, uniqueKey);
        if (!lock) {
            log.warn("handleDepartmentChange ea:{} uniqueKey:{} lock failed", ea, uniqueKey);
            return;
        }
        try {
            String changeType = eventMsg.getChangeType();
            switch (changeType) {
                case "create_party":
                case "update_party":
                    String accessToken = qywxManager.getAccessToken(ea);
                    SingleDepartmentResult singleDepartmentResult = qywxManager.querySingleDepartment(accessToken, Integer.parseInt(eventMsg.getId()));
                    if (singleDepartmentResult != null && singleDepartmentResult.getDepartment() != null) {
                        createOrUpdate(ea, Lists.newArrayList(singleDepartmentResult.getDepartment()));
                    }
                    break;
                case "delete_party":
                    List<QywxDepartmentEntity> qywxDepartmentEntityList = qywxDepartmentDAO.findAllQywxIdAndParentId(ea);
                    if (CollectionUtils.isEmpty(qywxDepartmentEntityList)) {
                        return;
                    }
                    int departmentId = Integer.parseInt(eventMsg.getId());
                    Set<Integer> departmentIdsToDelete = getChildDepartmentId(departmentId, qywxDepartmentEntityList, true);
                    if (!departmentIdsToDelete.isEmpty()) {
                        qywxDepartmentDAO.deleteByQywxIdList(ea, Lists.newArrayList(departmentIdsToDelete));
                    }
                    break;
                case "update_user":
                    // 该用户的部门，格式： 5,6
                    String department = eventMsg.getDepartment();
                    // 该用户是否是部门负责人，格式和department一一对应,1是负责人，0不是，1,0 说明该用户是5部门的负责人，不是6部门的负责人
                    String isLeaderInDept = eventMsg.getIsLeaderInDept();
                    if (StringUtils.isBlank(department) || StringUtils.isBlank(isLeaderInDept)) {
                        return;
                    }
                    String[] departmentArr = department.split(",");
                    String[] isLeaderArr = isLeaderInDept.split(",");
                    if (departmentArr.length > isLeaderArr.length) {
                        log.warn("handleDepartmentChange department length is wrong, ea: {} msg: {}", ea, eventMsg);
                        return;
                    }
                    Map<Integer, Boolean> isLeaderDepartmentMap = Maps.newHashMap();
                    for (int i = 0; i < departmentArr.length; i++) {
                        isLeaderDepartmentMap.put(Integer.parseInt(departmentArr[i]), "1".equals(isLeaderArr[i]));
                    }
                    List<Integer> departmentIdList = Arrays.stream(departmentArr).map(Integer::parseInt).collect(Collectors.toList());
                    List<QywxDepartmentEntity> existDepartmentList = qywxDepartmentDAO.findByQywxIdList(ea, departmentIdList);
                    if (CollectionUtils.isEmpty(existDepartmentList)) {
                        return;
                    }
                    Map<Integer, QywxDepartmentEntity> idToDepartmentMAP = existDepartmentList.stream().collect(Collectors.toMap(QywxDepartmentEntity::getQywxId, e -> e));
                    String userId = eventMsg.getUserID();
                    List<QywxDepartmentEntity> needUpdateDepartmentList = Lists.newArrayList();
                    for (Map.Entry<Integer, Boolean> entry : isLeaderDepartmentMap.entrySet()) {
                        Integer id = entry.getKey();
                        QywxDepartmentEntity qywxDepartmentEntity = idToDepartmentMAP.get(id);
                        if (qywxDepartmentEntity == null) {
                            continue;
                        }
                        Set<String> currentDepartmentLeaderSet = StringUtils.isBlank(qywxDepartmentEntity.getDepartmentLeader()) ? Sets.newHashSet() : Sets.newHashSet(JSONArray.parseArray(qywxDepartmentEntity.getDepartmentLeader(), String.class));
                        // 如果是部门负责人且当前库中不是，则更新部门信息
                        if (entry.getValue() && !currentDepartmentLeaderSet.contains(userId)) {
                            currentDepartmentLeaderSet.add(userId);
                            qywxDepartmentEntity.setDepartmentLeader(JsonUtil.toJson(currentDepartmentLeaderSet));
                            needUpdateDepartmentList.add(qywxDepartmentEntity);
                        } else if (!entry.getValue() && currentDepartmentLeaderSet.contains(userId)) {
                            // 如果不是部门负责人且当前库中是，则更新部门信息
                            currentDepartmentLeaderSet.remove(userId);
                            qywxDepartmentEntity.setDepartmentLeader(JsonUtil.toJson(currentDepartmentLeaderSet));
                            needUpdateDepartmentList.add(qywxDepartmentEntity);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(needUpdateDepartmentList)) {
                        Lists.partition(needUpdateDepartmentList, 50).forEach(e -> qywxDepartmentDAO.batchUpdate(ea, e));
                    }
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("handleDepartmentChange error, ea: {} msg: {}", ea, eventMsg, e);
        } finally {
            redisManager.unlockQywxDepartmentChange(ea, uniqueKey);
        }
    }

    private String getLockUniqueKey(ChangeContactEventMsg eventMsg) {
        String changeType = eventMsg.getChangeType();
        if ("create_party".equals(changeType) || "update_party".equals(changeType) || "delete_party".equals(changeType)) {
            return eventMsg.getId();
        } else if ("update_user".equals(changeType) && StringUtils.isNotBlank(eventMsg.getDepartment()) && StringUtils.isNotBlank(eventMsg.getIsLeaderInDept())) {
            return eventMsg.getUserID();
        }
        return null;
    }


    private Set<Integer> getChildDepartmentId(int departmentId, List<QywxDepartmentEntity> qywxDepartmentEntityList, boolean includeSelf) {
        Set<Integer> resultSet = new HashSet<>();
        Queue<Integer> queue = new LinkedList<>();
        queue.add(departmentId);
        while (!queue.isEmpty()) {
            int currentId = queue.poll();
            resultSet.add(currentId);
            for (QywxDepartmentEntity department : qywxDepartmentEntityList) {
                if (department.getParentId() != null && department.getParentId() == currentId && !resultSet.contains(department.getQywxId())) {
                    queue.add(department.getQywxId());
                }
            }
        }
        if (!includeSelf) {
            resultSet.remove(departmentId);
        }
        return resultSet;
    }

    public List<Integer> getChildDepartmentList(String ea, List<Integer> departmentIdList, boolean includeSelf) {
        if (CollectionUtils.isEmpty(departmentIdList)) {
            return Lists.newArrayList();
        }
        List<QywxDepartmentEntity> qywxDepartmentEntityList = qywxDepartmentDAO.findAllQywxIdAndParentId(ea);
        if (CollectionUtils.isEmpty(qywxDepartmentEntityList)) {
            return Lists.newArrayList();
        }
        Set<Integer> resultSet = Sets.newHashSet();
        for (Integer departmentId : departmentIdList) {
            Set<Integer> singleSet = getChildDepartmentId(departmentId, qywxDepartmentEntityList, includeSelf);
            resultSet.addAll(singleSet);
        }
        return Lists.newArrayList(resultSet);
    }
}
