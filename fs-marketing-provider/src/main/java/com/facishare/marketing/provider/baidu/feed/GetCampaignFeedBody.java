package com.facishare.marketing.provider.baidu.feed;

import com.facishare.marketing.provider.baidu.IRequestBody;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GetCampaignFeedBody implements IRequestBody, Serializable {

    // 需要查询的计划属性	 see: https://dev2.baidu.com/content?sceneType=0&pageId=101269&nodeId=106&subhead=
    private List<String> campaignFeedFields;

    // 查询推广计划ID集合 集合长度限制：[0, 100]  输入空返回整个账户的计划ID
    private List<Long> campaignFeedIds;

    // 计划查询过滤条件
    private CampaignFeedFilter campaignFeedFilter;

    public static class CampaignFeedFilter implements Serializable {
        // 计划类型  1 - 普通计划 3 - 商品计划 7 - 原生RTA 不填返回全部
        private List<Integer> bstype;
    }
}
