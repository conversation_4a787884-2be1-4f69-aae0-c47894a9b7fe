package com.facishare.marketing.provider.entity.emailMaterial;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/2/24
 * @Desc
 **/
@Data
public class EmailMaterialEntity implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * ea
     */
    private String ea;

    /**
     * 邮件标题
     */
    private String title;

    /**
     * 邮件内容
     */
    private String content;

    /**
     * 创建人
     */
    private Integer createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新人
     */
    private Integer updateBy;

    /**
     * 最后更新时间
     */
    private Date updateTime;
}
