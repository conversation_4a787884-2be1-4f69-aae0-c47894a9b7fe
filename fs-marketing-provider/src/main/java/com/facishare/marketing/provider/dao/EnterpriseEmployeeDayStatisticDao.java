package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.EnterpriseEmployeeDayStatisticEntity;
import com.github.mybatis.pagination.Page;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 */
public interface EnterpriseEmployeeDayStatisticDao {
    @Update("UPDATE enterprise_employee_day_statistic SET forward_count=#{forwardCount},look_up_count=#{lookUpCount},active_count=#{activeCount},\n"
        + "    spread_count=#{spreadCount},crm_lead_increment_count=#{crmLeadIncrementCount},crm_customer_increment_count=#{crmCustomerIncrementCount},\n"
        + "    all_channel_data=#{allChannelData},update_time=NOW() WHERE id = #{id}")
    void updateStatistic(EnterpriseEmployeeDayStatisticEntity entity);

    @Select("SELECT * FROM enterprise_employee_day_statistic WHERE ea=#{ea} AND fs_user_id=#{fsUserId} AND \"date\"=#{date};")
    EnterpriseEmployeeDayStatisticEntity getUnique(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("date") Date date);

    @Insert("INSERT INTO enterprise_employee_day_statistic(id, ea, fs_user_id, \"date\", create_time) VALUES (#{id}, #{ea},#{fsUserId},#{date}, now()) ON CONFLICT DO NOTHING;")
    int insertIgnore(@Param("id") String id, @Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("date") Date date);

    @Select("<script>" + "SELECT fs_user_id, sum(crm_lead_increment_count) crm_lead_increment_count,\n" + "    sum(spread_count) spread_count\n"
        + "    FROM enterprise_employee_day_statistic WHERE ea = #{ea} AND \"date\" &gt;= #{startDate} AND \"date\"\n" + "    &lt;= #{endDate}\n" + "    GROUP BY fs_user_id\n"
        + "    <if test=\"countType == 5\">\n" + "      ORDER BY crm_lead_increment_count DESC \n" + "    </if>\n" + "    <if test=\"countType == 7\">\n" + "  ORDER BY spread_count DESC\n" + "    </if>\n"
        + "    LIMIT #{limit}" + "</script>")
    List<EnterpriseEmployeeDayStatisticEntity> orderByCountType(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("countType") Integer countType,
        @Param("limit") int limit);

    @Select("<script>" + "SELECT * FROM (SELECT fs_user_id, ea , sum(spread_count) spread_count,sum(active_count)\n" + "    active_count,sum(look_up_count)\n" + "    look_up_count,\n"
        + "    sum(forward_count) forward_count,sum(crm_lead_increment_count) crm_lead_increment_count,\n" + "    sum(crm_customer_increment_count) crm_customer_increment_count\n"
        + "    FROM enterprise_employee_day_statistic WHERE ea = #{ea}\n" + "    <if test=\"startDate != null\">\n" + "      AND \"date\" &gt;= #{startDate}\n" + "    </if>\n"
        + "    <if test=\"endDate != null\">\n" + "      AND \"date\" &lt;= #{endDate}\n" + "    </if>\n" + "    <if test=\"employeeIds != null and employeeIds.size() != 0\">\n"
        + "      AND fs_user_id IN\n"
            + "      <foreach collection=\"employeeIds\" open=\"(\" close=\")\" index=\"idx\" separator=\",\">\n"
            + "        #{employeeIds[${idx}]}\n" + "      </foreach>\n"
        + "    </if>\n" + "    GROUP BY fs_user_id,ea ) t ORDER BY spread_count DESC" + "</script>")
    List<EnterpriseEmployeeDayStatisticEntity> getEmployeeDayStatisticByEmployeeIdsAndDate(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate,
        @Param("employeeIds") List<Integer> employeeIds, @Param("page") Page page);

    @Select(
        "<script>" + "Select COALESCE(count(distinct fs_user_id), 0) FROM enterprise_employee_day_statistic WHERE ea=#{ea} AND spread_count > 0 " + "<if test='startDate != null'>" + "AND \"date\" &gt;= #{startDate}"
            + "</if>" + "<if test='endDate != null'>" + "AND \"date\" &lt;= #{endDate}" + "</if>" + "</script>")
    int countSpreadEmployee(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>" + "select COALESCE(sum(spread_count), 0) as sc FROM enterprise_employee_day_statistic WHERE ea=#{ea} " + "<if test='startDate != null'>" + "AND \"date\" &gt;= #{startDate}" + "</if>"
        + "<if test='endDate != null'>" + "AND \"date\" &lt;= #{endDate}" + "</if>" + "</script>")
    int countEmployeeSpread(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script> SELECT  COALESCE(count(crm_lead_increment_count),0)  FROM  enterprise_employee_day_statistic    where create_time  between #{startDate} and  #{endDate}   </script>")
    Integer getEnterpriseEmployeeLeadsWeeklyStatistics(@Param("startDate") Date startDate,@Param("endDate") Date endDate);
}
