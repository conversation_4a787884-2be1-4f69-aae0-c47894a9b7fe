package com.facishare.marketing.provider.service;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.aizhan.KeyWordRankArg;
import com.facishare.marketing.api.arg.aizhan.SEOPromptCompletionsArg;
import com.facishare.marketing.api.arg.aizhan.TdkDataArg;
import com.facishare.marketing.api.arg.aizhan.WebsiteBasicInfoArg;
import com.facishare.marketing.api.arg.officialWebsite.GetWebsiteStatisticByIdArg;
import com.facishare.marketing.api.result.ai.PromptCompletionsResult;
import com.facishare.marketing.api.result.aizhan.*;
import com.facishare.marketing.api.result.officialWebsite.WebsiteDataBriefingResult;
import com.facishare.marketing.api.service.OfficialWebsiteService;
import com.facishare.marketing.api.service.WebsiteSeoService;
import com.facishare.marketing.common.enums.AiZhanKeywordTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.officialWebsite.OfficialWebsiteDAO;
import com.facishare.marketing.provider.dao.officialWebsite.OfficialWebsiteDaySourceStatisticDAO;
import com.facishare.marketing.provider.dao.seo.OfficialWebsiteSeoDataDAO;
import com.facishare.marketing.provider.dto.officialWebsite.WebsiteTrafficStatisticBySourceDTO;
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteEntity;
import com.facishare.marketing.provider.entity.seo.OfficialWebsiteSeoDataEntity;
import com.facishare.marketing.provider.innerData.aizhan.*;
import com.facishare.marketing.provider.manager.AiZhanManager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.ai.PaaSPromptManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.paas.I18N;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service("websiteSeoService")
@Slf4j
public class WebsiteSeoServiceImpl implements WebsiteSeoService {


    @Autowired
    private OfficialWebsiteDAO officialWebsiteDAO;

    @Autowired
    private OfficialWebsiteService officialWebsiteService;

    @Autowired
    private OfficialWebsiteDaySourceStatisticDAO officialWebsiteDaySourceStatisticDAO;

    @Autowired
    private AiZhanManager aiZhanManager;

    @Autowired
    private OfficialWebsiteSeoDataDAO officialWebsiteSeoDataDAO;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private PaaSPromptManager paaSPromptManager;
    private static String MARKETING_SEO_INIT = "MARKETING_SEO_INIT_";

    private static String MARKETING_SEO_KEYWORD = "MARKETING_SEO_KEYWORD_";

    @ReloadableProperty("seo_tdk_template")
    private String seoTdkTemplate;
    @ReloadableProperty("seo_tdk_template_en")
    private String seoTdkTemplateEN;

    @ReloadableProperty("seo_keyword_template")
    private String seoKeywordTemplate;
    @ReloadableProperty("seo_keyword_template_en")
    private String seoKeywordTemplateEN;

    @ReloadableProperty("seo_header_template")
    private String seoHeaderTemplate;
    @ReloadableProperty("seo_header_template_en")
    private String seoHeaderTemplateEN;

    @Autowired
    private HttpManager httpManager;

    @Override
    public Result<WebsiteBasicInfoResult> queryWebsiteBasicInfo(WebsiteBasicInfoArg arg) {
        OfficialWebsiteEntity officialWebsiteEntity;
        if (StringUtils.isNotBlank(arg.getId())) {
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteById(arg.getId());
        } else {
            // 查询官网信息
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteByEa(arg.getEa());
        }
        if (officialWebsiteEntity == null) {
            log.warn("WebsiteSeoServiceImpl.queryWebsiteBasicInfo arg:{}", arg);
            return new Result<>(SHErrorCode.OFFICIAL_WEBSITE_NOT_FOUND);
        }

        //pv,uv
        WebsiteBasicInfoResult result = new WebsiteBasicInfoResult();
        GetWebsiteStatisticByIdArg statisticByIdArg = new GetWebsiteStatisticByIdArg();
        statisticByIdArg.setId(officialWebsiteEntity.getId());
        statisticByIdArg.setEa(arg.getEa());
        statisticByIdArg.setStartTime(arg.getStartTime());
        statisticByIdArg.setEndTime(arg.getEndTime());
        Result<WebsiteDataBriefingResult> dataBriefing = officialWebsiteService.officialWebsiteDataBriefing(statisticByIdArg);
        if (dataBriefing!=null && dataBriefing.getData()!=null){
            WebsiteDataBriefingResult.DataBriefingInfo pvInfo = dataBriefing.getData().getPvInfo();
            if (pvInfo!=null){
                result.setPv(new WebsiteBasicInfoResult.SeoDataInfo(Integer.parseInt(pvInfo.getCount()),pvInfo.getGrowthRate()));
            }
            WebsiteDataBriefingResult.DataBriefingInfo uvInfo = dataBriefing.getData().getUvInfo();
            if (uvInfo!=null){
                result.setUv(new WebsiteBasicInfoResult.SeoDataInfo(Integer.parseInt(uvInfo.getCount()),uvInfo.getGrowthRate()));
            }
        }

        //设备流量
        String startTimeStr = arg.getStartTime() == null ? null : DateUtil.format2(new Date(arg.getStartTime()));
        String endTimeStr = arg.getEndTime() == null ? null : DateUtil.format2(new Date(arg.getEndTime()));
        List<WebsiteTrafficStatisticBySourceDTO> sourceDTOS = officialWebsiteDaySourceStatisticDAO.trafficClientStatistic(arg.getEa(), arg.getId(), startTimeStr, endTimeStr);
        if (CollectionUtils.isNotEmpty(sourceDTOS)){
            Integer totalCount = sourceDTOS.stream().map(WebsiteTrafficStatisticBySourceDTO::getCount).reduce(0, Integer::sum);
            if(totalCount == 0){
                result.setPcTraffic("0%");
                result.setMobileTraffic("0%");
            }else {
                Map<Integer, Integer> map = sourceDTOS.stream().collect(Collectors.toMap(WebsiteTrafficStatisticBySourceDTO::getClient, WebsiteTrafficStatisticBySourceDTO::getCount, Integer::sum));
                result.setPcTraffic(getRate(map.get(2),totalCount)+"%");
                result.setMobileTraffic((100-getRate(map.get(2),totalCount))+"%");
            }
        }

        //备案信息
        OfficialWebsiteSeoDataEntity seoData = officialWebsiteSeoDataDAO.getLatestOfficialWebsiteSeoData(officialWebsiteEntity.getEa(), officialWebsiteEntity.getId());
        if (seoData != null) {
            result.setRecordInformation(seoData.getSiteRecord());
            result.setLastUpdateTime(seoData.getUpdateTime()==null?null:seoData.getUpdateTime().getTime());
        }

        //自然搜索来源
        WebsiteBasicInfoResult.SeoDataInfo searchInfo = new WebsiteBasicInfoResult.SeoDataInfo();
        result.setSearch(searchInfo);
        //是否算环比,默认是true
        boolean isRingRatio = true ;
        Map<String, Map<String, Long>> sourceMap = officialWebsiteDaySourceStatisticDAO.trafficSourceStatistic(arg.getEa(), arg.getId(), startTimeStr, endTimeStr);
        if(MapUtils.isEmpty(sourceMap)){
            searchInfo.setCount("0%");
            searchInfo.setGrowthRate("0%");
        }else {
            int totalCount = 0;
            for (Map<String, Long> value : sourceMap.values()) {
                totalCount += Math.toIntExact(value.get("count"));
            }
            if (startTimeStr == null || endTimeStr == null) {
                isRingRatio = false;
            }
            Map<String, Map<String, Long>> lastSourceMap = null;
            String previousStartTimeStr = null;
            String previousEndTimeStr = null;
            if(isRingRatio){
                Date startTime = DateUtil.parse3(startTimeStr);
                Date endTime = DateUtil.parse3(endTimeStr);
                int periodDays = DateUtil.getPeriodDays(startTime, endTime);
                Date previousStartTime = DateUtil.getPreviousPeriod(startTime, periodDays);
                Date previousEndTime = DateUtil.getPreviousPeriod(endTime, periodDays);

                previousStartTimeStr = DateUtil.format2(previousStartTime);
                previousEndTimeStr = DateUtil.format2(previousEndTime);

                String oldestDateStr = officialWebsiteDaySourceStatisticDAO.getOldestDateStr(arg.getEa(), arg.getId());
                // 如果最早的数据比上一次的开始时间还要早，那么就不求上周期的算环比
                if(previousStartTime.getTime() < DateUtil.parse3(oldestDateStr).getTime()){
                    isRingRatio = false;
                }
            }
            if(isRingRatio){
                lastSourceMap =  officialWebsiteDaySourceStatisticDAO.trafficSourceStatistic(arg.getEa(), arg.getId(), previousStartTimeStr, previousEndTimeStr);
                int lastTotalCount = 0;
                for (Map<String, Long> value : lastSourceMap.values()) {
                    lastTotalCount += Math.toIntExact(value.get("count"));
                }
                Integer lastRate = getRate(Math.toIntExact(lastSourceMap.get("search").get("count")), lastTotalCount);
                Integer rate = getRate(Math.toIntExact(sourceMap.get("search").get("count")), totalCount);
                searchInfo.setCount(rate +"%");
                searchInfo.setGrowthRate(TextUtil.getGrowthRate(rate, lastRate));
            }else {
                Integer rate = getRate(Math.toIntExact(sourceMap.get("search").get("count")), totalCount);
                searchInfo.setCount(rate +"%");
            }
        }

        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> updateTdkData(TdkDataArg arg){
        OfficialWebsiteEntity officialWebsiteEntity;
        if (StringUtils.isNotBlank(arg.getId())) {
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteById(arg.getId());
        } else {
            // 查询官网信息
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteByEa(arg.getEa());
        }
        OfficialWebsiteSeoDataEntity entity = officialWebsiteSeoDataDAO.getLatestOfficialWebsiteSeoData(officialWebsiteEntity.getEa(), officialWebsiteEntity.getId());
        if (entity == null) {
            return Result.newError(SHErrorCode.OFFICIAL_WEBSITE_SEO_DATA_NOT_FOUND);
        }
        OfficialWebsiteSeoDataEntity updateTdkDataEntity = new OfficialWebsiteSeoDataEntity();
        updateTdkDataEntity.setId(entity.getId());
        updateTdkDataEntity.setEa(entity.getEa());
        updateTdkDataEntity.setRating(arg.getRating());
        updateTdkDataEntity.setHtmlContent(arg.getHtmlContent());
        updateTdkDataEntity.setAiKeyword(arg.getAiKeyword());
        officialWebsiteSeoDataDAO.updateTdkData(updateTdkDataEntity);
        return Result.newSuccess();
    }

    @Override
    public Result<TdkDataResult> queryTdkData(WebsiteBasicInfoArg arg){
        OfficialWebsiteEntity officialWebsiteEntity;
        if (StringUtils.isNotBlank(arg.getId())) {
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteById(arg.getId());
        } else {
            // 查询官网信息
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteByEa(arg.getEa());
        }
        OfficialWebsiteSeoDataEntity entity = officialWebsiteSeoDataDAO.getLatestOfficialWebsiteSeoData(officialWebsiteEntity.getEa(), officialWebsiteEntity.getId());
        if (entity == null) {
            return Result.newError(SHErrorCode.OFFICIAL_WEBSITE_SEO_DATA_NOT_FOUND);
        }
        TdkDataResult result = new TdkDataResult();
        result.setId(entity.getOfficialWebsiteId());
        result.setRating(entity.getRating());
        result.setHtmlContent(entity.getHtmlContent());
        result.setAiKeyword(entity.getAiKeyword());
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> seoInit(WebsiteBasicInfoArg arg){
        OfficialWebsiteEntity officialWebsiteEntity;
        if (StringUtils.isNotBlank(arg.getId())) {
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteById(arg.getId());
        } else {
            // 查询官网信息
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteByEa(arg.getEa());
        }
        String domain = extractDomain(officialWebsiteEntity.getWebsiteUrl());
        if (StringUtils.isBlank(domain)){
            return Result.newError(SHErrorCode.OFFICIAL_WEBSITE_DOMAIN_RESOLUTION_EXCEPTION);
        }

        String timeStr = DateUtil.format2(arg.getTimestamp()==null?new Date():new Date(arg.getTimestamp()));
        //一天只能查一次,查询时间小于数据最新时间不查
        if(officialWebsiteSeoDataDAO.countByTimeStr(officialWebsiteEntity.getEa(),officialWebsiteEntity.getId(), timeStr)>0){
            return Result.newSuccess();
        }
        ThreadPoolUtils.execute(() -> initData(domain, officialWebsiteEntity,timeStr), ThreadPoolUtils.ThreadPoolTypeEnums.LIGHT_BUSINESS);
        return Result.newSuccess();
    }

    private void initData(String domain, OfficialWebsiteEntity officialWebsiteEntity,String timeStr) {
        String key = MARKETING_SEO_INIT+officialWebsiteEntity.getEa()+officialWebsiteEntity.getId()+timeStr;
        try {
            boolean redisLock = redisManager.lock(key, 60*2);
            if (!redisLock) {
                return;
            }

            //备案信息
            String siteInformation = null;
            Map<String, String> siteInformationMap = Maps.newHashMap();
            siteInformationMap.put("domains", domain);
            SiteRecordInformationData informationData = aiZhanManager.siteRecordInformation(siteInformationMap);
            if (informationData!=null){
                siteInformation = JSON.toJSONString(informationData.getSuccess());
            }else {
                siteInformation = JSON.toJSONString(Lists.newArrayList());
            }


            //查关键词排名
            List<KeyWordRankResult> keyWordList = Lists.newArrayList();
            //移动端关键词
            KeyWordRankResult mobileKeyWord = seoKeyWordRank(new KeyWordRankArg(AiZhanKeywordTypeEnum.MOBILE_KEYWORD.getClient(), AiZhanKeywordTypeEnum.MOBILE_KEYWORD.getType(), 1), domain);
            mobileKeyWord.setClient(AiZhanKeywordTypeEnum.MOBILE_KEYWORD.getClient());
            mobileKeyWord.setType(AiZhanKeywordTypeEnum.MOBILE_KEYWORD.getType());
            keyWordList.add(CollectionUtils.isEmpty(mobileKeyWord.getRank()) ? mobileKeyWord : KeyWordRankResult.getTop5(mobileKeyWord));
            //移动端跌出词
            KeyWordRankResult mobileKeyWordRankDown = seoKeyWordRank(new KeyWordRankArg(AiZhanKeywordTypeEnum.MOBILE_RANK_DOWN_KEYWORD.getClient(), AiZhanKeywordTypeEnum.MOBILE_RANK_DOWN_KEYWORD.getType(), 1), domain);
            mobileKeyWordRankDown.setClient(AiZhanKeywordTypeEnum.MOBILE_RANK_DOWN_KEYWORD.getClient());
            mobileKeyWordRankDown.setType(AiZhanKeywordTypeEnum.MOBILE_RANK_DOWN_KEYWORD.getType());
            keyWordList.add(CollectionUtils.isEmpty(mobileKeyWordRankDown.getRank()) ? mobileKeyWordRankDown : KeyWordRankResult.getTop5(mobileKeyWordRankDown));
            //移动端涨入词
            KeyWordRankResult mobileKeyWordRankUp = seoKeyWordRank(new KeyWordRankArg(AiZhanKeywordTypeEnum.MOBILE_RANK_UP_KEYWORD.getClient(),AiZhanKeywordTypeEnum.MOBILE_RANK_UP_KEYWORD.getType(),1), domain);
            mobileKeyWordRankUp.setClient(AiZhanKeywordTypeEnum.MOBILE_RANK_UP_KEYWORD.getClient());
            mobileKeyWordRankUp.setType(AiZhanKeywordTypeEnum.MOBILE_RANK_UP_KEYWORD.getType());
            keyWordList.add(CollectionUtils.isEmpty(mobileKeyWordRankUp.getRank()) ? mobileKeyWordRankUp : KeyWordRankResult.getTop5(mobileKeyWordRankUp));
            //pc端关键词
            KeyWordRankResult pcKeyWord = seoKeyWordRank(new KeyWordRankArg(AiZhanKeywordTypeEnum.PC_KEYWORD.getClient(), AiZhanKeywordTypeEnum.PC_KEYWORD.getType(), 1), domain);
            pcKeyWord.setClient(AiZhanKeywordTypeEnum.PC_KEYWORD.getClient());
            pcKeyWord.setType(AiZhanKeywordTypeEnum.PC_KEYWORD.getType());
            keyWordList.add(CollectionUtils.isEmpty(pcKeyWord.getRank()) ? pcKeyWord : KeyWordRankResult.getTop5(pcKeyWord));
            //pc端跌出词
            KeyWordRankResult pcKeyWordRankDown = seoKeyWordRank(new KeyWordRankArg(AiZhanKeywordTypeEnum.PC_RANK_DOWN_KEYWORD.getClient(),AiZhanKeywordTypeEnum.PC_RANK_DOWN_KEYWORD.getType(),1), domain);
            pcKeyWordRankDown.setClient(AiZhanKeywordTypeEnum.PC_RANK_DOWN_KEYWORD.getClient());
            pcKeyWordRankDown.setType(AiZhanKeywordTypeEnum.PC_RANK_DOWN_KEYWORD.getType());
            keyWordList.add(CollectionUtils.isEmpty(pcKeyWordRankDown.getRank()) ? pcKeyWordRankDown : KeyWordRankResult.getTop5(pcKeyWordRankDown));
            //pc端涨入词
            KeyWordRankResult pcKeyWordRankUp = seoKeyWordRank(new KeyWordRankArg(AiZhanKeywordTypeEnum.PC_RANK_UP_KEYWORD.getClient(),AiZhanKeywordTypeEnum.PC_RANK_UP_KEYWORD.getType(),1), domain);
            pcKeyWordRankUp.setClient(AiZhanKeywordTypeEnum.PC_RANK_UP_KEYWORD.getClient());
            pcKeyWordRankUp.setType(AiZhanKeywordTypeEnum.PC_RANK_UP_KEYWORD.getType());
            keyWordList.add(CollectionUtils.isEmpty(pcKeyWordRankUp.getRank()) ? pcKeyWordRankUp : KeyWordRankResult.getTop5(pcKeyWordRankUp));

            //seo历史数据,关键词趋势
            String seoHistory = null;
            Map<String, String> seoHistoryMap = Maps.newHashMap();
            seoHistoryMap.put("domain", domain);
            seoHistoryMap.put("startdate",DateUtil.getPreviousMonth(new Date(), 3));
            seoHistoryMap.put("enddate", DateUtil.format2(new Date()));
            List<SeoHistoryData> seoHistoryData = aiZhanManager.seoHistoryData(seoHistoryMap);
            seoHistory = JSON.toJSONString(seoHistoryData);


            //seo收录趋势
            String siteInclusionTrend = null;
            Map<String, String> siteInclusionTrendMap = Maps.newHashMap();
            siteInclusionTrendMap.put("domain", domain);
            siteInclusionTrendMap.put("startdate",DateUtil.getPreviousMonth(new Date(), 3));
            siteInclusionTrendMap.put("enddate", DateUtil.format2(new Date()));
            List<SiteInclusionTrendData> siteInclusionTrendData = aiZhanManager.siteInclusionTrend(siteInclusionTrendMap);
            siteInclusionTrend = JSON.toJSONString(siteInclusionTrendData);


            //反链
            String siteAntiChainTrend = null;
            Map<String, String> siteAntiChainTrendMap = Maps.newHashMap();
            siteAntiChainTrendMap.put("domain", domain);
            SiteAntiChainTrendData siteAntiChainTrendData = aiZhanManager.siteAntiChainTrend(siteAntiChainTrendMap);
            if (siteAntiChainTrendData!=null){
                siteAntiChainTrend = JSON.toJSONString(siteAntiChainTrendData);
            }else {
                siteAntiChainTrend = JSON.toJSONString(new SiteAntiChainTrendData());
            }

            OfficialWebsiteSeoDataEntity seoDataEntity = new OfficialWebsiteSeoDataEntity();
            seoDataEntity.setId(UUIDUtil.getUUID());
            seoDataEntity.setEa(officialWebsiteEntity.getEa());
            seoDataEntity.setOfficialWebsiteId(officialWebsiteEntity.getId());
            seoDataEntity.setSiteRecord(siteInformation);
            seoDataEntity.setKeywordRankInfo(JSON.toJSONString(keyWordList));
            seoDataEntity.setSeoHistoryData(seoHistory);
            seoDataEntity.setSiteInclusionTrend(siteInclusionTrend);
            seoDataEntity.setSiteAntiChain(siteAntiChainTrend);
            seoDataEntity.setTimeStr(timeStr);
            officialWebsiteSeoDataDAO.insert(seoDataEntity);
        } catch (Exception e) {
            log.warn("WebsiteSeoServiceImpl.initData ,domain:{}, officialWebsiteEntity:{}, timeStr:{}, error:{}", domain,officialWebsiteEntity,timeStr,e);
        } finally {
            redisManager.unLock(key);
        }
    }

    public KeyWordRankResult seoKeyWordRank(KeyWordRankArg arg,String domain) {
        KeyWordRankResult result = new KeyWordRankResult();
        Integer type = arg.getType();
        //移动端
        switch (arg.getClient()){
            case 1:
                switch (type){
                    case 1:
                        Map<String, String> map = Maps.newHashMap();
                        map.put("domain", domain);
                        map.put("page", arg.getPage()+"");
                        KeywordRankingData keywordRankingData = aiZhanManager.keywordMobilRanking(map);
                        if(keywordRankingData==null){
                            result.setClient(arg.getClient());
                            result.setType(type);
                            return result;
                        }
                        result = BeanUtil.copyByGson(keywordRankingData,KeyWordRankResult.class);
                        break;
                    case 2:
                        Map<String, String> rankDownMap = Maps.newHashMap();
                        rankDownMap.put("domain", domain);
                        rankDownMap.put("ranktype", "rankdown");
                        rankDownMap.put("date", DateUtil.format2(new Date()));
                        rankDownMap.put("page", arg.getPage()+"");
                        KeywordEmulationRankingData emulationRankDownData = aiZhanManager.keywordMobileEmulationRanking(rankDownMap);
                        if (emulationRankDownData==null){
                            result.setClient(arg.getClient());
                            result.setType(type);
                            return result;
                        }
                        result = BeanUtil.copyByGson(emulationRankDownData,KeyWordRankResult.class);
                        break;
                    case 3:
                        Map<String, String> rankUpMap = Maps.newHashMap();
                        rankUpMap.put("domain", domain);
                        rankUpMap.put("ranktype", "rankup");
                        rankUpMap.put("date", DateUtil.format2(new Date()));
                        rankUpMap.put("wordpages", "0");
                        rankUpMap.put("page", arg.getPage()+"");
                        KeywordEmulationRankingData emulationRankUpData = aiZhanManager.keywordMobileEmulationRanking(rankUpMap);
                        if (emulationRankUpData==null){
                            result.setClient(arg.getClient());
                            result.setType(type);
                            return result;
                        }
                        result = BeanUtil.copyByGson(emulationRankUpData,KeyWordRankResult.class);
                        break;
                    default:
                        break;
                }
                break;
            case 2:
                //pc端
                switch (type){
                    case 1:
                        Map<String, String> map = Maps.newHashMap();
                        map.put("domain", domain);
                        map.put("page", arg.getPage()+"");
                        KeywordRankingData keywordRankingData = aiZhanManager.keywordPcRanking(map);
                        if(keywordRankingData==null){
                            result.setClient(arg.getClient());
                            result.setType(type);
                            return result;
                        }
                        result = BeanUtil.copyByGson(keywordRankingData,KeyWordRankResult.class);
                        break;
                    case 2:
                        Map<String, String> rankDownMap = Maps.newHashMap();
                        rankDownMap.put("domain", domain);
                        rankDownMap.put("ranktype", "rankdown");
                        rankDownMap.put("date", DateUtil.format2(new Date()));
                        rankDownMap.put("page", arg.getPage()+"");
                        KeywordEmulationRankingData emulationRankDownData = aiZhanManager.keywordPcEmulationRanking(rankDownMap);
                        if (emulationRankDownData==null){
                            result.setClient(arg.getClient());
                            result.setType(type);
                            return result;
                        }
                        result = BeanUtil.copyByGson(emulationRankDownData,KeyWordRankResult.class);
                        break;
                    case 3:
                        Map<String, String> rankUpMap = Maps.newHashMap();
                        rankUpMap.put("domain", domain);
                        rankUpMap.put("ranktype", "rankup");
                        rankUpMap.put("date", DateUtil.format2(new Date()));
                        rankUpMap.put("wordpages", "0");
                        rankUpMap.put("page", arg.getPage()+"");
                        KeywordEmulationRankingData emulationRankUpData = aiZhanManager.keywordPcEmulationRanking(rankUpMap);
                        if (emulationRankUpData==null){
                            result.setClient(arg.getClient());
                            result.setType(type);
                            return result;
                        }
                        result = BeanUtil.copyByGson(emulationRankUpData,KeyWordRankResult.class);
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
        result.setClient(arg.getClient());
        result.setType(type);
        return result;
    }

    @Override
    public Result<KeyWordRankResult> queryKeyWordRankArg(KeyWordRankArg arg) {
        OfficialWebsiteEntity officialWebsiteEntity;
        if (StringUtils.isNotBlank(arg.getId())) {
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteById(arg.getId());
        } else {
            // 查询官网信息
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteByEa(arg.getEa());
        }
        String domain = extractDomain(officialWebsiteEntity.getWebsiteUrl());
        if (StringUtils.isBlank(domain)){
            return Result.newError(SHErrorCode.OFFICIAL_WEBSITE_DOMAIN_RESOLUTION_EXCEPTION);
        }
        KeyWordRankResult rankResult = new KeyWordRankResult();
        String timeStr = DateUtil.format2(new Date());
        String key = MARKETING_SEO_KEYWORD+officialWebsiteEntity.getEa()+officialWebsiteEntity.getId()+arg.getClient()+arg.getType()+timeStr+arg.getPage();
        String keywordStr = redisManager.get(key);
        if (StringUtils.isBlank(keywordStr)){
            rankResult = seoKeyWordRank(arg, domain);
            if (rankResult!=null&&CollectionUtils.isNotEmpty(rankResult.getRank())){
                redisManager.set(key,JSON.toJSONString(rankResult),60*60*24);
            }
        }else {
            rankResult = JSON.parseObject(keywordStr,KeyWordRankResult.class);
        }
        rankResult.setClient(arg.getClient());
        rankResult.setType(arg.getType());
        return Result.newSuccess(rankResult);
    }

    @Override
    public Result<SeoPageResult> querySeoPage(WebsiteBasicInfoArg arg) {
        OfficialWebsiteEntity officialWebsiteEntity;
        if (StringUtils.isNotBlank(arg.getId())) {
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteById(arg.getId());
        } else {
            // 查询官网信息
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteByEa(arg.getEa());
        }
        OfficialWebsiteSeoDataEntity seoData = officialWebsiteSeoDataDAO.getLatestOfficialWebsiteSeoData(officialWebsiteEntity.getEa(), officialWebsiteEntity.getId());
        if (seoData == null) {
            return new Result<>(SHErrorCode.OFFICIAL_WEBSITE_SEO_DATA_NOT_FOUND);
        }
        SeoPageResult result = new SeoPageResult();
        List<KeyWordTrendResult> keyWordTrendList = JSON.parseArray(seoData.getSeoHistoryData(), KeyWordTrendResult.class);
        List<KeyWordRankResult> keyWordList = JSON.parseArray(seoData.getKeywordRankInfo(), KeyWordRankResult.class);
        List<SiteInclusionTrendResult> siteInclusionTrendData = JSON.parseArray(seoData.getSiteInclusionTrend(), SiteInclusionTrendResult.class);
        //List<OfficialWebsiteSeoDataEntity> seoDataEntities = officialWebsiteSeoDataDAO.listByOfficialWebsiteId(officialWebsiteEntity.getEa(), officialWebsiteEntity.getId());
        SiteAntiChainTrendData chainTrendData = JSON.parseObject(seoData.getSiteAntiChain(), SiteAntiChainTrendData.class);
        List<SiteAntiChainResult> siteAntiChainList = Lists.newArrayList();
        if (chainTrendData!=null){
            siteAntiChainList = BeanUtil.copyByGson(chainTrendData.getItems(), SiteAntiChainResult.class);
        }
        result.setKeyWordTrendList(keyWordTrendList);
        result.setKeyWordRankList(keyWordList);
        result.setSiteInclusionTrendList(siteInclusionTrendData);
        result.setSiteAntiChainList(siteAntiChainList);
        return Result.newSuccess(result);
    }

    public Result<DomainContentResult> getDomainUrl(WebsiteBasicInfoArg arg){
        OfficialWebsiteEntity officialWebsiteEntity;
        if (StringUtils.isNotBlank(arg.getId())) {
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteById(arg.getId());
        } else {
            // 查询官网信息
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteByEa(arg.getEa());
        }
        if (officialWebsiteEntity == null || StringUtils.isBlank(officialWebsiteEntity.getWebsiteUrl())) {
            log.warn("WebsiteSeoServiceImpl.queryWebsiteBasicInfo arg:{}", arg);
            return new Result<>(SHErrorCode.OFFICIAL_WEBSITE_NOT_FOUND);
        }
        StringBuilder html = new StringBuilder();
        try {
            Document doc = Jsoup.connect(officialWebsiteEntity.getWebsiteUrl()).get();
            html.append(doc.html());
        } catch (IOException e) {
            log.warn("WebsiteSeoServiceImpl.getDomainContent error url={},e={}",officialWebsiteEntity.getWebsiteUrl(),e);
            String completeUrl = completeURL(officialWebsiteEntity.getWebsiteUrl());
            if (StringUtils.isBlank(completeUrl)){
                return Result.newError(SHErrorCode.OFFICIAL_WEBSITE_DOMAIN_RESOLUTION_EXCEPTION);
            }
            Document doc1 = null;
            try {
                doc1 = Jsoup.connect(completeUrl).get();
            } catch (Exception ex) {
                log.warn("WebsiteSeoServiceImpl.getDomainContent error,completeUrl={}, e={}",completeUrl, e);
                return Result.newError(SHErrorCode.OFFICIAL_WEBSITE_DOMAIN_RESOLUTION_EXCEPTION);
            }
            if (StringUtils.isNotBlank(completeUrl)){
                html.append(doc1.html());
            }
        }
        DomainContentResult result = new DomainContentResult();
        result.setDomain(officialWebsiteEntity.getWebsiteUrl());
        result.setContent(html.toString());
        return Result.newSuccess(result);
    }

    @Override
    public Result<DomainContentResult> getDomainContent(WebsiteBasicInfoArg arg){
        OfficialWebsiteEntity officialWebsiteEntity;
        if (StringUtils.isNotBlank(arg.getId())) {
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteById(arg.getId());
        } else {
            // 查询官网信息
            officialWebsiteEntity = officialWebsiteDAO.getOfficialWebsiteByEa(arg.getEa());
        }
        if (officialWebsiteEntity == null || StringUtils.isBlank(officialWebsiteEntity.getWebsiteUrl())) {
            log.warn("WebsiteSeoServiceImpl.queryWebsiteBasicInfo arg:{}", arg);
            return new Result<>(SHErrorCode.OFFICIAL_WEBSITE_NOT_FOUND);
        }
        DomainContentResult result = new DomainContentResult();
        result.setDomain(officialWebsiteEntity.getWebsiteUrl());
        if (!URLValidator.validateURLV2(officialWebsiteEntity.getWebsiteUrl())) {
            return Result.newSuccess(result);
        }
        //第一次请求
        String html = httpManager.executeGetHttpReturnStringWithOutTime(officialWebsiteEntity.getWebsiteUrl(),3000L);

        String completeUrl = null;
        //第二次请求
        if (StringUtils.isBlank(html)) {
            completeUrl = completeURL(officialWebsiteEntity.getWebsiteUrl());
            if (StringUtils.isBlank(completeUrl)) {
                return Result.newError(SHErrorCode.OFFICIAL_WEBSITE_DOMAIN_RESOLUTION_EXCEPTION);
            }
            html = httpManager.executeGetHttpReturnStringWithOutTime(completeUrl,2000L);
        }

        //第三次请求
        if (StringUtils.isBlank(html)) {
            completeUrl = completeUrl.replace("https","http");
            html = httpManager.executeGetHttpReturnStringWithOutTime(completeUrl,2000L);
            if (StringUtils.isBlank(html)) {
                return Result.newError(SHErrorCode.OFFICIAL_WEBSITE_DOMAIN_RESOLUTION_EXCEPTION);
            }
        }
        result.setContent(html);
        return Result.newSuccess(result);
    }
    @Override
    public Result<Boolean> checkSeoInitResult(WebsiteBasicInfoArg arg) {
        String timeStr = DateUtil.format2(arg.getTimestamp()==null?new Date():new Date(arg.getTimestamp()));
        if(officialWebsiteSeoDataDAO.countByTimeStr(arg.getEa(),arg.getId(), timeStr)>0){
            return Result.newSuccess(true);
        }
        return Result.newSuccess(false);
    }

    @Override
    public Result<Map<String,String>> getAiTemplate() {
        HashMap<String, String> map = Maps.newHashMap();
        if(I18N.ZH_CN.equals(I18nUtil.getLanguage())) {
            map.put("seoTdkTemplate", seoTdkTemplate);
            map.put("seoKeywordTemplate", seoKeywordTemplate);
            map.put("seoHeaderTemplate", seoHeaderTemplate);
        } else {
            map.put("seoTdkTemplate", seoTdkTemplateEN);
            map.put("seoKeywordTemplate", seoKeywordTemplateEN);
            map.put("seoHeaderTemplate", seoHeaderTemplateEN);
        }
        return Result.newSuccess(map);
    }

    @Override
    public Result<PromptCompletionsResult> promptCompletions(String ea, Integer fsUserId, SEOPromptCompletionsArg arg) {
        return paaSPromptManager.asyncPromptCompletions(arg.getTaskId(), () -> paaSPromptManager.generateSEOContent(ea, fsUserId, arg));
    }

    public static Integer getRate(Integer count, Integer lastCount) {
        if(count==null || lastCount==null || count==0){
            return 0;
        }
        if(lastCount == 0){
            return 100;
        }
        return (int) Math.ceil(Math.abs(Double.parseDouble(String.valueOf(count)) / lastCount*100));
    }

    public static String extractDomain(String url) {
        // 删除开头的协议部分
        String result = url.replaceFirst("^[^:/]+://", "");

        // 判断是否为域名或IP地址
        String pattern = "((?:[\\w-]+\\.)+[a-zA-Z]{2,7}|(?:\\d{1,3}\\.){3}\\d{1,3})";
        Pattern regex = Pattern.compile(pattern);
        Matcher matcher = regex.matcher(result);

        if (matcher.find()) {
            // 获取匹配到的内容
            result = matcher.group(0);
        } else {
            // 不是域名或IP地址，返回null
            result = null;
        }
        return result;
    }



    public static String completeURL(String url) {
        String s = extractDomain(url);
        if (StringUtils.isBlank(s)){
            return null;
        }
        if (s.startsWith("www.")){
            return "https://"+s;
        }
        return "https://www."+s;
//        String regex = "^(https?://)?(www\\.)?([^/]+)(.*)$";
//        String replacement = "https://www.$3$4";
//        return url.replaceAll(regex, replacement);
    }


}
