package com.facishare.marketing.provider.mq.handler.processor;

import com.facishare.marketing.provider.manager.kis.SpreadTaskManager;
import com.facishare.marketing.provider.mq.handler.dto.FxEmailSendEventDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025/4/1
 * @Desc
 **/
@Slf4j
@Component
public class FxEmailSendEventProcessor {

    @Autowired
    private SpreadTaskManager spreadTaskManager;

    public void processMqMessage(FxEmailSendEventDTO msgObj) {
        String ea = msgObj.getEa();
        Integer userId = msgObj.getUserId();
        String marketingActivityId = msgObj.getMarketingActivityId();
        if (StringUtils.isBlank(ea) || userId == null || StringUtils.isBlank(marketingActivityId)) {
            return;
        }
        spreadTaskManager.finishSpreadTask(ea, userId, marketingActivityId);
    }
}
