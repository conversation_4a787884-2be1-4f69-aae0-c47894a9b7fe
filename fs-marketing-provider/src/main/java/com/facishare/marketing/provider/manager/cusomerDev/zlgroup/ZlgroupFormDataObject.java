package com.facishare.marketing.provider.manager.cusomerDev.zlgroup;

import com.facishare.marketing.common.enums.ActivitySignOrEnrollEnum;
import com.facishare.marketing.common.enums.conference.ConferenceEnrollReviewStatusEnum;
import com.facishare.marketing.provider.dao.ActivityDAO;
import com.facishare.marketing.provider.entity.ActivityEnrollDataEntity;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity;
import com.facishare.marketing.provider.manager.ActivityManager;
import com.facishare.marketing.provider.manager.CampaignMergeDataManager;
import com.facishare.marketing.provider.manager.cusomerDev.CustomerConferenceDev;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import java.util.Arrays;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by zhengh on 2020/5/9.
 * zlgroup 深圳中力企业管理顾问有限公司 会议报名进入自定义对象
 * 自定义对象字段映射关系
 */
@Component("zlgroupFormDataObject")
@Slf4j
public class ZlgroupFormDataObject implements CustomerConferenceDev{

    public static final String EA = "zlgroup";
    public static final int EI = 691450;

    public static final String OBJECT_APINAME = "object_0STc2__c";             //参会人员对象
    public static final String COMPANY_APINAME = "field_19yj2__c";             //公司
    public static final String POSITION_APINAME = "field_xYyq3__c";            //职位
    public static final String PHONE_APINAME = "field_eEnk4__c";               //手机
    public static final String NAME_APINAME = "field_yKs8e__c";                //姓名
    public static final String OWNER_APINAME = "owner";                        //负责人
    public static final String CREATED_BY_APINAME = "created_by";              //创建人
    public static final String REVIEW_STATUS_APINAME = "field_2dN0w__c";       //报名审核状态
    public static final String SIGNIN_STATUS_APINAME = "field_1xu95__c";       //签到状态
    public static final String SIGNIN_TIME_APINAME = "field_3ic8J__c";         //签到时间
    public static final String MARKETING_EVENT_ID = "field_2k40v__c";       //市场活动id

    @Autowired
    private ActivityDAO activityDAO;
    @Autowired
    private ActivityManager activityManager;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;

    @Override
    public Optional<ActionAddArg> handleBuildSubmitAction(String ea, CustomizeFormDataUserEntity customizeFormDataUserEntity) {
        String activityId = activityManager.getActivityIdByObject(customizeFormDataUserEntity.getObjectId(), customizeFormDataUserEntity.getObjectType(), ea, customizeFormDataUserEntity.getMarketingEventId());
        if (StringUtils.isBlank(activityId)) {
            return Optional.empty();
        }
        ActivityEntity activityEntity = activityDAO.getById(activityId);
        if (activityEntity == null) {
            return Optional.empty();
        }
        // 查询是否已有会议数据
        ActivityEnrollDataEntity activityEnrollDataEntity = null;
        if (StringUtils.isNotBlank(customizeFormDataUserEntity.getCampaignId())) {
            activityEnrollDataEntity = campaignMergeDataManager.getActivityEnrollDataByCampaignId(customizeFormDataUserEntity.getCampaignId());
        }

        ActionAddArg actionAddArg = new ActionAddArg();
        ObjectData objectData = new ObjectData(ZlgroupFormDataObject.OBJECT_APINAME);
        objectData.put(ZlgroupFormDataObject.COMPANY_APINAME, customizeFormDataUserEntity.getSubmitContent().getCompanyName());
        objectData.put(ZlgroupFormDataObject.POSITION_APINAME, customizeFormDataUserEntity.getSubmitContent().getPosition());
        objectData.put(ZlgroupFormDataObject.PHONE_APINAME, customizeFormDataUserEntity.getSubmitContent().getPhone());
        objectData.put(ZlgroupFormDataObject.NAME_APINAME, customizeFormDataUserEntity.getSubmitContent().getName());
        objectData.put(ZlgroupFormDataObject.MARKETING_EVENT_ID, customizeFormDataUserEntity.getMarketingEventId());
        if (activityEnrollDataEntity != null && activityEnrollDataEntity.getReviewStatus() != null) {
            ZlgroupReviewOptionEnum zlgroupReviewOptionEnum = ZlgroupReviewOptionEnum.getReviewEnumByStatusValue(activityEnrollDataEntity.getReviewStatus());
            if (zlgroupReviewOptionEnum != null) {
                objectData.put(ZlgroupFormDataObject.REVIEW_STATUS_APINAME, zlgroupReviewOptionEnum.getValue());
            } else {
                objectData.put(ZlgroupFormDataObject.REVIEW_STATUS_APINAME, ZlgroupReviewOptionEnum.REVIEW_PENDING.getValue());
            }
        } else {
            if (activityEntity.getEnrollReview()) {
                objectData.put(ZlgroupFormDataObject.REVIEW_STATUS_APINAME, ZlgroupReviewOptionEnum.REVIEW_PENDING.getValue());
            } else {
                objectData.put(ZlgroupFormDataObject.REVIEW_STATUS_APINAME, ZlgroupReviewOptionEnum.REVIEW_SUCCESS.getValue());
            }
        }
        if (activityEnrollDataEntity != null && activityEnrollDataEntity.getSignIn() != null) {
            objectData.put(ZlgroupFormDataObject.SIGNIN_STATUS_APINAME,
                activityEnrollDataEntity.getSignIn().equals(ActivitySignOrEnrollEnum.SIGN_IN.getType()) ? ZlgroupSignOptionEnum.SIGN_IN.getValue() : ZlgroupSignOptionEnum.NOT_SIGN_IN.getValue());
            if (activityEnrollDataEntity.getSignInTime() != null) {
                objectData.put(ZlgroupFormDataObject.SIGNIN_TIME_APINAME, activityEnrollDataEntity.getSignInTime().getTime());
            }
        } else {
            objectData.put(ZlgroupFormDataObject.SIGNIN_STATUS_APINAME, ZlgroupSignOptionEnum.NOT_SIGN_IN.getValue());
        }

        objectData.put(ZlgroupFormDataObject.CREATED_BY_APINAME, -10000);
        objectData.put(ZlgroupFormDataObject.OWNER_APINAME, Arrays.asList("-10000"));
        actionAddArg.setObjectData(objectData);

        return Optional.of(actionAddArg);
    }

    @Override
    public Optional<ActionEditArg> handleBuildUpdateSignInObject(String ea, String crmObjectId, int signIn) {
        ActionEditArg actionEditArg = new ActionEditArg();
        ObjectData objectData = new ObjectData(ZlgroupFormDataObject.OBJECT_APINAME);
        objectData.put(ObjectDescribeContants.ID, crmObjectId);

        if(signIn == ActivitySignOrEnrollEnum.SIGN_IN.getType()){
            objectData.put(ZlgroupFormDataObject.SIGNIN_STATUS_APINAME, ZlgroupSignOptionEnum.SIGN_IN.getValue());
            objectData.put(ZlgroupFormDataObject.SIGNIN_TIME_APINAME, System.currentTimeMillis());
        }
        actionEditArg.setObjectData(objectData);

        return Optional.of(actionEditArg);
    }

    @Override
    public Optional<ActionEditArg> handleBuildUpdateReviewObject(String ea, String crmObjectId, int reviewStatus) {
        ActionEditArg actionEditArg = new ActionEditArg();
        ObjectData objectData = new ObjectData(ZlgroupFormDataObject.OBJECT_APINAME);
        objectData.put(ObjectDescribeContants.ID, crmObjectId);

        if (reviewStatus == ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus()){
            objectData.put(ZlgroupFormDataObject.REVIEW_STATUS_APINAME, ZlgroupReviewOptionEnum.REVIEW_SUCCESS.getValue());
        }else {
            objectData.put(ZlgroupFormDataObject.REVIEW_STATUS_APINAME, ZlgroupReviewOptionEnum.REVIEW_FAILED.getValue());
        }
        actionEditArg.setObjectData(objectData);

        return Optional.of(actionEditArg);
    }

    @Override
    public void syncCrmConferenceDataObjectToMarketing(String ea, Integer ei, String apiName, String objectId, String op) {
        return;
    }


    //for 112 test
/**
    public static final String EA = "74164";
    public static final int EI = 74164;
    public static final String OBJECT_APINAME = "object_x3ro4__c";             //参会人员对象
    public static final String COMPANY_APINAME = "field_0Gbf5__c";             //公司名称
    public static final String POSITION_APINAME = "field_U1tI1__c";            //职位
    public static final String PHONE_APINAME = "field_Nheo8__c";               //手机
    public static final String NAME_APINAME = "field_H0d0r__c";                //姓名

    public static final String OWNER_APINAME = "owner";                        //负责人
    public static final String CREATED_BY_APINAME = "created_by";              //创建人
    public static final String CREATE_TIME_APINAME = "create_time";            //创建时间
    public static final String REVIEW_STATUS_APINAME = "field_e8bBJ__c";       //报名审核状态

    public static final String SIGNIN_STATUS_APINAME = "field_974i3__c";       //签到状态
    public static final String SIGNIN_TIME_APINAME = "field_7E20X__c";         //签到时间
    public static final String MARKETING_EVENT_ID = "field_HPF23__c";         //市场活动
**/
}
