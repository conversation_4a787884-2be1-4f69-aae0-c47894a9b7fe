package com.facishare.marketing.provider.baidu;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by ranluch on 2019/11/22.
 */
@Data
public class ScheduleType implements Serializable {
    /**
     * 		推广暂停时段日
     * 	以星期几为单位，7个值可供输入：
     1 - 星期一
     2 - 星期二
     3 - 星期三
     4 - 星期四
     5 - 星期五
     6 - 星期六
     7 - 星期日
     在updateCampaign接口中，
     该字段设置为空数组，
     即"schedule":[]表示清空原有暂停时段设置
     * */
    @SerializedName("weekDay")
    private Integer weekDay;

    /**
     *  推广时段暂停开始时间	以小时为单位，取值范围：[0,23]
     *
     * */
    @SerializedName("startHour")
    private Long startHour;

    /**
     *  推广时段暂停结束时间	以小时为单位，取值范围：[1,24]
     *
     * */
    @SerializedName("endHour")
    private Long endHour;
}
