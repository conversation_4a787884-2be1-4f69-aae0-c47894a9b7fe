/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.crmobjectcreator;

import com.beust.jcommander.internal.Sets;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.enums.PaasAndCrmOperatorEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignDAO;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignEntity;
import com.facishare.marketing.provider.innerArg.CreateAdvertisingDetailObjArg;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.util.ObjDescribeUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.arg.AddDescribeCustomFieldArg;
import com.fxiaoke.crmrestapi.arg.FindByQueryV3Arg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.marketing.common.contstant.OperatorConstants.EQ;
import static com.facishare.marketing.common.contstant.OperatorConstants.IN;

@Component("advertisingDetailObjManager")
@Slf4j
public class AdvertisingDetailsObjManager extends AbstractObjManager {

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private AdAccountManager adAccountManager;

    @Autowired
    private BaiduCampaignDAO baiduCampaignDAO;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;

    @Autowired
    private AdCommonManager adCommonManager;

    @Override
    public String getApiName() {
        return CrmObjectApiNameEnum.ADVERTISING_DETAILS_OBJ.getName();
    }

    @Override
    public String getJsonData() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/advertising_details_obj_data.json");
    }

    @Override
    public String getJsonLayout() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/advertising_details_obj_layout.json");
    }

    @Override
    public String getJsonListLayout() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/advertising_details_obj_list_layout.json");
    }

    @Override
    public ObjectDescribe getOrCreateObjDescribe(String ea) {
        try {
            ObjectDescribe objectDescribe = super.getOrCreateObjDescribe(ea);
            super.tryHideAddImportFormButton(ea, getApiName());
            addOCPCField(ea);
            return objectDescribe;
        } catch (Exception e) {
            log.error("getOrCreateObjDescribe error ea: {}", ea, e);
        }
        return null;
    }

    private String tryCreateObj(CreateAdvertisingDetailObjArg arg) {
        if (arg == null || !arg.checkParam()) {
            return null;
        }
        try {
            String ea = arg.getEa();
            Map<String, Object> crmObjectData = new HashMap<>();

            Date date = DateUtil.parse(arg.getLaunchDate(), "yyyy-MM-dd");
            long morningTime = DateUtil.getTimesMorning(date);

            crmObjectData.put("launch_date", morningTime);

            if (StringUtils.isNotBlank(arg.getMarketingEventId())) {
                crmObjectData.put("marketing_event_id", arg.getMarketingEventId());
            }

            if (StringUtils.isNotBlank(arg.getAdAccountId())) {
                crmObjectData.put("ad_account_id", arg.getAdAccountId());
            }

            if (arg.getCampaignOrAdGroupId() != null) {
                crmObjectData.put("ad_campaign_group_id", String.valueOf(arg.getCampaignOrAdGroupId()));
            }

            if (arg.getShow() == null) {
                arg.setShow(0L);
            }
            crmObjectData.put("shows", String.valueOf(arg.getShow()));

            if (arg.getClick() == null) {
                arg.setClick(0L);
            }
            crmObjectData.put("clicks", String.valueOf(arg.getClick()));

            if (arg.getCost() == null) {
                arg.setCost(0D);
            }
            crmObjectData.put("costs", String.valueOf(arg.getCost()));

            if (arg.getOcpcConversions() == null) {
                arg.setOcpcConversions(0L);
            }
            crmObjectData.put("ocpc_conversions", String.valueOf(arg.getOcpcConversions()));

            if (arg.getDeepOcpcConversions() == null) {
                arg.setDeepOcpcConversions(0L);
            }
            crmObjectData.put("deep_ocpc_conversions", String.valueOf(arg.getDeepOcpcConversions()));

            int owner = adCommonManager.getAdObjectDataOwner(arg.getEa());
            crmObjectData.put("owner", Lists.newArrayList(String.valueOf(owner)));
            crmObjectData.put("created_by", Lists.newArrayList(String.valueOf(owner)));

            log.info("创建广告投放明细,ea:[{}], param:[{}]", ea, crmObjectData);
            Map<String, Object> result = crmMetadataManager.addMetadata(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.ADVERTISING_DETAILS_OBJ.getName(), crmObjectData);
            return result.get("_id").toString();
        } catch (Exception e) {
            log.error("创建广告明细对象异常, arg: {}", arg, e);
        }
        return null;
    }

    private Result<ActionEditResult> tryUpdateObj(CreateAdvertisingDetailObjArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getId())) {
            return null;
        }
        try {
            String ea = arg.getEa();
            Map<String, Object> crmObjectData = new HashMap<>();

            crmObjectData.put("_id", arg.getId());

            if (arg.getShow() != null) {
                crmObjectData.put("shows", String.valueOf(arg.getShow()));
            }

            if (arg.getClick() != null) {
                crmObjectData.put("clicks", String.valueOf(arg.getClick()));
            }

            if (arg.getCost() != null) {
                crmObjectData.put("costs", String.valueOf(arg.getCost()));
            }

            if (arg.getOcpcConversions() != null) {
                crmObjectData.put("ocpc_conversions", String.valueOf(arg.getOcpcConversions()));
            }

            if (arg.getDeepOcpcConversions() != null) {
                crmObjectData.put("deep_ocpc_conversions", String.valueOf(arg.getDeepOcpcConversions()));
            }

            if (StringUtils.isNotBlank(arg.getMarketingEventId())) {
                crmObjectData.put("marketing_event_id", arg.getMarketingEventId());
            }

            log.info("更新广告投放明细,ea:[{}], param:[{}]", ea, crmObjectData);
            return crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.ADVERTISING_DETAILS_OBJ.getName(), crmObjectData);
        } catch (Exception e) {
            log.error("更新广告明细对象异常, arg: {}", arg, e);
        }
        return null;
    }

    public void tryUpdateOrCreateObj(List<CreateAdvertisingDetailObjArg> argList) {
        if (CollectionUtils.isEmpty(argList)) {
            return;
        }
        initNullValue(argList);
        int pageSize = 2000;
        List<List<CreateAdvertisingDetailObjArg>> partitionList = Lists.partition(argList, pageSize);
        String ea = argList.get(0).getEa();
        for (List<CreateAdvertisingDetailObjArg> part : partitionList) {
            Map<String, List<CreateAdvertisingDetailObjArg>> launchDateToArgMap = part.stream().filter(e -> StringUtils.isNotBlank(e.getAdAccountId())).collect(Collectors.groupingBy(CreateAdvertisingDetailObjArg::getLaunchDate));
            launchDateToArgMap.forEach((launchDate, list) -> {
                Set<String> adAccountIdSet = new HashSet<>();
                Set<String> campaignOrAdGroupIdSet = new HashSet<>();
                for (CreateAdvertisingDetailObjArg arg : list) {
                    adAccountIdSet.add(arg.getAdAccountId());
                    campaignOrAdGroupIdSet.add(String.valueOf(arg.getCampaignOrAdGroupId()));
                }
                List<PaasQueryArg.Condition> conditionList = Lists.newArrayList();
                PaasQueryArg.Condition condition = new PaasQueryArg.Condition("ad_account_id", Lists.newArrayList(adAccountIdSet), IN);
                conditionList.add(condition);
                Date date = DateUtil.parse(launchDate, "yyyy-MM-dd");
                long morningTime = DateUtil.getTimesMorning(date);
                condition = new PaasQueryArg.Condition("launch_date", Lists.newArrayList(String.valueOf(morningTime)), EQ);
                conditionList.add(condition);

                condition = new PaasQueryArg.Condition("ad_campaign_group_id", Lists.newArrayList(campaignOrAdGroupIdSet), IN);
                conditionList.add(condition);

                PaasQueryArg paasQueryArg = new PaasQueryArg(0, pageSize);
                paasQueryArg.setFilters(conditionList);
                FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
                findByQueryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.ADVERTISING_DETAILS_OBJ.getName());
                findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
                List<String> selectFields = Lists.newArrayList("_id", "ad_account_id", "launch_date", "ad_campaign_group_id", "marketing_event_id", "clicks", "costs", "shows");
                findByQueryV3Arg.setSelectFields(selectFields);
                Map<String, ObjectData> dataToIdMap = Maps.newHashMap();
                try {
                    long t1 = System.currentTimeMillis();
                    InnerPage<ObjectData> objectDataInnerPage = crmMetadataManager.listV3(ea, SuperUserConstants.USER_ID, findByQueryV3Arg);
                    log.info("查询耗时: {} size: {}", System.currentTimeMillis() - t1, objectDataInnerPage.getTotalCount());
                    Set<String> deleteIdSet = Sets.newHashSet();
                    if (CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
                        List<ObjectData> objectDataList = objectDataInnerPage.getDataList();
                        objectDataList.forEach(e -> {
                            List<String> keyList = Lists.newArrayList(e.getString("ad_account_id"), e.getString("ad_campaign_group_id"), e.getString("marketing_event_id"));
                            String key = String.join("-", keyList);
                            ObjectData objectData = dataToIdMap.get(key);
                            if (objectData == null || objectData.getId().equals(e.getId())) {
                                dataToIdMap.put(key, e);
                            } else {
                                deleteIdSet.add(e.getId());
                            }
                        });
                    }
                    // 之前的BUG会导致有多余的数据 将多余的数据作废掉
                    if (CollectionUtils.isNotEmpty(deleteIdSet)) {
                        long beginTime = System.currentTimeMillis();
                        List<String> invalidIdList = null;
                        boolean isError = false;
                        try {
                            List<ObjectData> objectDataList = crmV2Manager.bulkInvalid(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.ADVERTISING_DETAILS_OBJ.getName(), Lists.newArrayList(deleteIdSet));
                            invalidIdList = CollectionUtils.isEmpty(objectDataList) ? null : objectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
                        } catch (Exception e) {
                            isError = true;
                            log.error("广告回传明细 bulkInvalid error, ea: {}, idList: {}", ea, deleteIdSet, e);
                        }
                        log.info("作废耗时：{} ms isError: {} ea: {} idList: {}", System.currentTimeMillis() - beginTime, isError, ea, invalidIdList);
                    }
                    for (CreateAdvertisingDetailObjArg arg : list) {
                        List<String> keyList = Lists.newArrayList(arg.getAdAccountId(), String.valueOf(arg.getCampaignOrAdGroupId()), arg.getMarketingEventId());
                        ObjectData objectData = dataToIdMap.get(String.join("-", keyList));
                        long beginTime = System.currentTimeMillis();
                        if (objectData == null) {
                            tryCreateObj(arg);
                        } else {
                            Long clicks = objectData.getLong("clicks");
                            clicks = clicks == null ? 0L : clicks;
                            Long shows = objectData.getLong("shows");
                            shows = shows == null ? 0L : shows;
                            BigDecimal costs = objectData.getBigDecimal("costs");
                            costs = costs == null ? BigDecimal.ZERO : costs;

                            Long ocpcConversions = objectData.getLong("ocpc_conversions");
                            ocpcConversions = ocpcConversions == null ? 0L : ocpcConversions;

                            Long deepOcpcConversions = objectData.getLong("deep_ocpc_conversions");
                            deepOcpcConversions = deepOcpcConversions == null ? 0L : deepOcpcConversions;

                            if (!arg.getClick().equals(clicks) || !arg.getShow().equals(shows) || !arg.getCost().equals(costs.doubleValue()) || !arg.getOcpcConversions().equals(ocpcConversions) || !arg.getDeepOcpcConversions().equals(deepOcpcConversions)) {
                                arg.setId(objectData.getId());
                                tryUpdateObj(arg);
                            }
                        }
                        log.info("更新或创建耗时: {}", System.currentTimeMillis() - beginTime);
                    }
                } catch (Exception e) {
                    log.error("tryUpdateOrCreateObj error, arg: {}", list, e);
                }
            });
        }
    }

    private void initNullValue(List<CreateAdvertisingDetailObjArg> argList) {
        for (CreateAdvertisingDetailObjArg advertisingDetailObjArg : argList) {
            if (advertisingDetailObjArg.getCost() == null) {
                advertisingDetailObjArg.setCost(0D);
            }
            if (advertisingDetailObjArg.getClick() == null) {
                advertisingDetailObjArg.setClick(0L);
            }
            if (advertisingDetailObjArg.getShow() == null) {
                advertisingDetailObjArg.setShow(0L);
            }
            if (advertisingDetailObjArg.getOcpcConversions() == null) {
                advertisingDetailObjArg.setOcpcConversions(0L);
            }
            if (advertisingDetailObjArg.getDeepOcpcConversions() == null) {
                advertisingDetailObjArg.setDeepOcpcConversions(0L);
            }
        }
    }

    public void fixShenHuMarketingEventId(String ea) {
        List<BaiduCampaignEntity> baiduCampaignEntityList = baiduCampaignDAO.queryCampaignByEa(ea, AdSourceEnum.SOURCE_BAIDU.getSource());
        baiduCampaignEntityList = baiduCampaignEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getMarketingEventId())).collect(Collectors.toList());
        int campaignTotalSize = baiduCampaignEntityList.size();
        int updateCampaignCount = 0;
        int updateObjectCount = 0;
        for (BaiduCampaignEntity baiduCampaignEntity : baiduCampaignEntityList) {
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.ADVERTISING_DETAILS_OBJ.getName());
            queryFilterArg.setSelectFields(Lists.newArrayList("_id", "marketing_event_id"));
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
            paasQueryArg.addFilter("ad_campaign_group_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(baiduCampaignEntity.getCampaignId().toString()));
            queryFilterArg.setQuery(paasQueryArg);
            int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
            log.info("广告投放明细数量 ea: {} campaignId: {} 总数：{}", ea, baiduCampaignEntity.getCampaignId(), totalCount);
            if (totalCount <= 0) {
                continue;
            }
            int currentCount = 0;
            int pageSize = 500;
            String lastId = null;
            long beginTime = System.currentTimeMillis();
            while (currentCount < totalCount) {
                InnerPage<ObjectData> innerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, pageSize);
                if (innerPage == null || CollectionUtils.isEmpty(innerPage.getDataList())) {
                    break;
                }
                List<ObjectData> objectDataList = innerPage.getDataList();
                currentCount += objectDataList.size();
                lastId = objectDataList.get(objectDataList.size() - 1).getId();
                for (ObjectData objectData : objectDataList) {
                    String marketingEventId = objectData.getString("marketing_event_id");
                    if (!baiduCampaignEntity.getMarketingEventId().equals(marketingEventId)) {
                        Map<String, Object> editData = Maps.newHashMap();
                        editData.put("_id", objectData.getId());
                        editData.put("marketing_event_id", baiduCampaignEntity.getMarketingEventId());
                        Result<ActionEditResult> result = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.ADVERTISING_DETAILS_OBJ.getName(), editData, false, false);
                        if (result == null || !result.isSuccess()) {
                            log.warn("编辑失败，ea: {} campaignId: {} id: {}", ea, baiduCampaignEntity.getCampaignId(), objectData.getId());
                        }
                        updateObjectCount++;
                    }
                }
            }
            long endTime = System.currentTimeMillis();
            updateCampaignCount++;
            log.info("更新市场活动，ea: {} campaignId: {} 耗时: {} campaignTotalCount: {} updateCampaignCount: {} updateObjectCount: {}", ea, baiduCampaignEntity.getCampaignId(), endTime - beginTime, campaignTotalSize, updateCampaignCount, updateObjectCount);
        }
    }

    public void addOCPCField(String ea) {
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID);
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.ADVERTISING_DETAILS_OBJ.getName());
        if (objectDescribeResult == null || !objectDescribeResult.isSuccess()) {
            log.warn("广告投放明细对象不存在 ea:{}", ea);
            return;
        }

        ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
        if (!objectDescribe.getFields().containsKey("ocpc_conversions")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.ADVERTISING_DETAILS_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"AdvertisingDetailsObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"number\",\"decimal_places\":0,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"max_length\":14,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"display_style\":\"input\",\"step_value\":1,\"length\":14,\"default_value\":\"\",\"label\":\"目标转化量\",\"api_name\":\"ocpc_conversions\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":2,\"help_text\":\"\",\"status\":\"new\"}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"number\",\"api_name\":\"AdvertisingDetailsObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add ocpc_conversions field ea:{} result: {}", ea, result);
        }

        if (!objectDescribe.getFields().containsKey("deep_ocpc_conversions")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.ADVERTISING_DETAILS_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"AdvertisingDetailsObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"number\",\"decimal_places\":0,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"max_length\":14,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"display_style\":\"input\",\"step_value\":1,\"length\":14,\"default_value\":\"\",\"label\":\"深度转化量\",\"api_name\":\"deep_ocpc_conversions\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":2,\"help_text\":\"\",\"status\":\"new\"}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"number\",\"api_name\":\"AdvertisingDetailsObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add deep_ocpc_conversions field ea:{} result: {}", ea, result);
        }

    }
}
