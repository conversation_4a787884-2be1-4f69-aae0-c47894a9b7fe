package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.result.GetEnterpriseSocialGroupGrayResult;
import com.facishare.marketing.api.service.EnterpriseSocialGroupService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.GsonUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2019/04/08
 **/
@Service("enterpriseSocialGroupService")
@Slf4j
public class EnterpriseSocialGroupServiceImpl implements EnterpriseSocialGroupService {
    @Override
    public Result<GetEnterpriseSocialGroupGrayResult> gray(String ea) {
        GetEnterpriseSocialGroupGrayResult getEnterpriseSocialGroupGrayResult = new GetEnterpriseSocialGroupGrayResult();
        return new Result<>(SHErrorCode.SUCCESS, getEnterpriseSocialGroupGrayResult);
    }
}
