package com.facishare.marketing.provider.service.connector.xiaohongshu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.marketing.api.result.ObjectTreeResponse;
import com.facishare.marketing.api.service.connector.xiaohongshu.XiaoHongShuService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.provider.util.XiaoHongShuPresetObjDescribeUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("xiaoHongShuService")
public class XiaoHongShuServiceImpl implements XiaoHongShuService {
    @Override
    public Result<ObjectTreeResponse> getObjectDesc(String objectApiName) {
        List<ObjectTreeResponse> presentObjectList = JSON.parseObject(XiaoHongShuPresetObjDescribeUtil.getXiaoHongShuPresetObjJsonData(), new TypeReference<List<ObjectTreeResponse>>(){});
        for (ObjectTreeResponse objectTree : presentObjectList) {
            if (StringUtils.isNotBlank(objectTree.getErpObjectApiName()) && objectTree.getErpObjectApiName().equals(objectApiName)) {
                return Result.newSuccess(objectTree);
            }
        }
        return Result.newError(SHErrorCode.SYSTEM_ERROR);

    }

    @Override
    public Result<List<ObjectTreeResponse>> listsObject() {
        List<ObjectTreeResponse> resultList = Lists.newArrayList();
        JSONArray jsonArray = JSON.parseArray(XiaoHongShuPresetObjDescribeUtil.getXiaoHongShuPresetObjJsonData());
        for (int i = 0; i < jsonArray.size(); i++) {
            ObjectTreeResponse objectTree = new ObjectTreeResponse();
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            objectTree.setErpObjectApiName(jsonObject.getString("erpObjectApiName"));
            objectTree.setErpObjectName(jsonObject.getString("erpObjectName"));
            resultList.add(objectTree);
        }
        return Result.newSuccess(resultList);

    }
}
