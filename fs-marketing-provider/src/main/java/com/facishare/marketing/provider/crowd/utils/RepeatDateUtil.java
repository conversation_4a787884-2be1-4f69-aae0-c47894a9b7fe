package com.facishare.marketing.provider.crowd.utils;

import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Slf4j
public class RepeatDateUtil {

    /**
     * 计算最近一次执行时间
     *
     * @param repeatRangeStart   开始时间
     * @param repeatRangeEnd     结束时间
     * @param repeatType         周期:0-季;1-月;2-周;3-天
     * @param includeRepeatValue 周期中具体日期(包含), 如: 第n月
     * @param repeatValue        周期中具体日期(具体), 如: 周-周三/月-3号
     * @param triggerAtMinutes   周期中具体时间
     * @return
     */
    public static Long calFirstExecuteTime(Long repeatRangeStart, Long repeatRangeEnd, Integer repeatType, List<Integer> includeRepeatValue, List<Integer> repeatValue, Integer triggerAtMinutes) {
        Long result = null;
        if (repeatRangeStart == null || repeatType == null || triggerAtMinutes == null) {
            return null;
        }
        switch (repeatType) {
            case 0:
                if (CollectionUtils.isEmpty(includeRepeatValue) || CollectionUtils.isEmpty(repeatValue)) return null;
                break;
            case 1:
            case 2:
                if (CollectionUtils.isEmpty(repeatValue)) return null;
                break;
        }
        if (CollectionUtils.isNotEmpty(repeatValue)) {
            Collections.sort(repeatValue);
        }
        repeatRangeStart = Math.max(new Date().getTime(), repeatRangeStart);
        switch (repeatType) {
            case 0:
                result = getFirstTimeByQuarter(repeatRangeStart, includeRepeatValue, repeatValue, triggerAtMinutes);
                break;
            case 1:
                result = getFirstTimeByMonth(repeatRangeStart, repeatValue, triggerAtMinutes);
                break;
            case 2:
                result = getFirstTimeByWeek(repeatRangeStart, repeatValue, triggerAtMinutes);
                break;
            case 3:
                result = getFirstTimeByDay(repeatRangeStart, triggerAtMinutes);
            default:
                break;
        }
        if (result != null && result <= repeatRangeEnd) {
            return result;
        }
        return null;
    }

    private static Long getFirstTimeByDay(Long repeatRangeStart, Integer triggerAtMinutes) {
        // 初始时间
        Calendar c = Calendar.getInstance();
        c.setTime(new Date(repeatRangeStart));
        int offSetMinute = c.get(Calendar.MINUTE) + c.get(Calendar.HOUR_OF_DAY) * 60;
        Long nextTime = null;
        if (offSetMinute < triggerAtMinutes) {
            //当天执行
            nextTime = DateUtil.getExactTime(c.getTime(), 0, triggerAtMinutes);
        } else {
            //明天执行
            nextTime = DateUtil.getExactTime(c.getTime(), 1, triggerAtMinutes);
        }
        return nextTime;
    }

    private static Long getFirstTimeByWeek(Long repeatRangeStart, List<Integer> repeatValue, Integer triggerAtMinutes) {
        // 初始时间
        Calendar c = Calendar.getInstance();
        c.setTime(new Date(repeatRangeStart));
        int currentDayOfWeek = c.get(Calendar.DAY_OF_WEEK);
        if (1 == currentDayOfWeek) {
            currentDayOfWeek = 7;
        } else {
            currentDayOfWeek -= 1;
        }
        int offSetMinute = c.get(Calendar.MINUTE) + c.get(Calendar.HOUR_OF_DAY) * 60;
        Long nextTime = null;
        //当天执行
        if (repeatValue.contains(currentDayOfWeek) && offSetMinute < triggerAtMinutes) {
            nextTime = DateUtil.getExactTime(c.getTime(), 0, triggerAtMinutes);
        } else {
            for (Integer nextRepeat : repeatValue) {
                //本周执行
                if (nextRepeat > currentDayOfWeek) {
                    nextTime = DateUtil.getExactTime(c.getTime(), nextRepeat - currentDayOfWeek, triggerAtMinutes);
                    break;
                }
            }
            if (null == nextTime) {
                //下周执行
                nextTime = DateUtil.getExactTime(c.getTime(), 7 - currentDayOfWeek + repeatValue.get(0), triggerAtMinutes);
            }
        }
        return nextTime;
    }

    private static Long getFirstTimeByMonth(Long repeatRangeStart, List<Integer> repeatValue, Integer triggerAtMinutes) {
        // 初始时间
        Calendar c = Calendar.getInstance();
        c.setTime(new Date(repeatRangeStart));
        int offSetMinute = c.get(Calendar.MINUTE) + c.get(Calendar.HOUR_OF_DAY) * 60;
        int currentDayOfMonth = c.get(Calendar.DAY_OF_MONTH);
        // 当月最后一天
        int lastDayOfMonth = c.getActualMaximum(Calendar.DAY_OF_MONTH);
        Long nextTime = null;
        //当天执行
        if (repeatValue.contains(currentDayOfMonth) && offSetMinute < triggerAtMinutes) {
            nextTime = DateUtil.getExactTime(c.getTime(), 0, triggerAtMinutes);
        } else {
            //本月执行
            for (Integer temp : repeatValue) {
                if (temp > currentDayOfMonth) {
                    //本月执行
                    nextTime = DateUtil.getExactTime(c.getTime(), currentDayOfMonth <= lastDayOfMonth ? temp - currentDayOfMonth : lastDayOfMonth - currentDayOfMonth, triggerAtMinutes);
                    break;
                }
            }
            //下月执行
            if (null == nextTime) {
                Date firstDayOfMonth = DateUtil.getFirstDayOfMonth(c.get(Calendar.YEAR), c.get(Calendar.MONTH) + 1);
                Date firstDayOfNextMonth = DateUtil.plusMonth(firstDayOfMonth, 1);
                Calendar nextMonth = Calendar.getInstance();
                nextMonth.setTime(firstDayOfNextMonth);
                nextTime = DateUtil.getExactTime(c.getTime(), lastDayOfMonth - currentDayOfMonth + (nextMonth.getActualMaximum(Calendar.DAY_OF_MONTH) < repeatValue.get(0) ? nextMonth.getActualMaximum(Calendar.DAY_OF_MONTH) : repeatValue.get(0)), triggerAtMinutes);
            }
        }
        return nextTime;
    }

    private static Long getFirstTimeByQuarter(Long repeatRangeStart, List<Integer> includeRepeatValue, List<Integer> repeatValue, Integer triggerAtMinutes) {
        // 初始时间
        Calendar c = Calendar.getInstance();
        c.setTime(new Date(repeatRangeStart));
        int currentMonthIndexOfQuarter = getCurrentMonthIndexOfQuarter(repeatRangeStart);
        int offSetMinute = c.get(Calendar.MINUTE) + c.get(Calendar.HOUR_OF_DAY) * 60;
        int currentDayOfMonth = c.get(Calendar.DAY_OF_MONTH);
        int lastDayOfMonth = c.getActualMaximum(Calendar.DAY_OF_MONTH);
        Long nextTime = null;
        if (CollectionUtils.isNotEmpty(includeRepeatValue)) {
            Collections.sort(includeRepeatValue);
        }
        if (includeRepeatValue.contains(currentMonthIndexOfQuarter) && repeatValue.contains(currentDayOfMonth) && offSetMinute < triggerAtMinutes) {
            //本季度->今天执行
            nextTime = DateUtil.getExactTime(c.getTime(), 0, triggerAtMinutes);
        } else {
            for (Integer idx : includeRepeatValue) {
                if (idx >= currentMonthIndexOfQuarter) {
                    int monthOffset = idx - currentMonthIndexOfQuarter;
                    Date currentDay = DateUtil.plusMonth(c.getTime(), monthOffset);
                    //本季度->本月执行
                    for (Integer temp : repeatValue) {
                        if (monthOffset == 0 && temp > currentDayOfMonth) {
                            nextTime = DateUtil.getExactTime(currentDay, currentDayOfMonth <= lastDayOfMonth ? temp - currentDayOfMonth : lastDayOfMonth - currentDayOfMonth, triggerAtMinutes);
                            return nextTime;
                        }
                    }
                    //本季度->下月执行
                    for (Integer temp : repeatValue) {
                        if (monthOffset > 0) {
                            nextTime = DateUtil.getExactTime(currentDay, currentDayOfMonth <= lastDayOfMonth ? temp - currentDayOfMonth : lastDayOfMonth - currentDayOfMonth, triggerAtMinutes);
                            return nextTime;
                        }
                    }
                }
            }
            //下季度执行
            Date firstDayOfQuarter = currentQuarterStart(repeatRangeStart);
            Date firstDayOfNextQuarter = DateUtil.plusMonth(firstDayOfQuarter, 3);
            Calendar nextQuarter = Calendar.getInstance();
            nextQuarter.setTime(firstDayOfNextQuarter);
            Calendar nextMonth = Calendar.getInstance();
            nextMonth.setTime(DateUtil.plusMonth(firstDayOfNextQuarter, includeRepeatValue.get(0)));
            nextTime = DateUtil.getExactTime(nextMonth.getTime(), nextMonth.getActualMaximum(Calendar.DAY_OF_MONTH) < repeatValue.get(0) ? nextMonth.getActualMaximum(Calendar.DAY_OF_MONTH) : repeatValue.get(0), triggerAtMinutes);
        }
        return nextTime;
    }

    private static Date currentQuarterStart(Long repeatRangeStart) {
        Calendar calBegin = Calendar.getInstance();
        calBegin.setTime(new Date(repeatRangeStart));
        int remainder = (calBegin.get(Calendar.MONTH) + 1) % 3;
        int startMonth = remainder != 0 ? remainder == 1 ? calBegin.get(Calendar.MONTH) : calBegin.get(Calendar.MONTH) + (remainder - 3) : calBegin.get(Calendar.MONTH) - 2;
        calBegin.set(Calendar.MONTH, startMonth);
        calBegin.set(Calendar.DAY_OF_MONTH, calBegin.getActualMinimum(Calendar.DAY_OF_MONTH));
        calBegin.set(Calendar.HOUR_OF_DAY, 0);
        calBegin.set(Calendar.MINUTE, 0);
        calBegin.set(Calendar.SECOND, 0);
        calBegin.set(Calendar.MILLISECOND, 0);
        return calBegin.getTime();
    }

    private static int getCurrentMonthIndexOfQuarter(Long repeatRangeStart) {
        Calendar calBegin = Calendar.getInstance();
        calBegin.setTime(new Date(repeatRangeStart));
        int currentMonth = calBegin.get(Calendar.MONTH);
        int remainder = (calBegin.get(Calendar.MONTH) + 1) % 3;
        int startMonth = remainder != 0 ? remainder == 1 ? calBegin.get(Calendar.MONTH) : calBegin.get(Calendar.MONTH) + (remainder - 3) : calBegin.get(Calendar.MONTH) - 2;
        return currentMonth - startMonth + 1;
    }

    public static void main(String[] args) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd HH:mm:ss");
        Long nextTime = calFirstExecuteTime(
                1724774400000L,
                1751382000000L,
                1,
                Lists.newArrayList(),
                Lists.newArrayList(1),
                540
        );
        System.out.println(sdf.format(new Date(nextTime)));
    }

}
