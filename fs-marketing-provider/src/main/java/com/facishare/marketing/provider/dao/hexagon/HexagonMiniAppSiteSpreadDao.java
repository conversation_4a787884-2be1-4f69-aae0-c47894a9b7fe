package com.facishare.marketing.provider.dao.hexagon;

import com.facishare.marketing.provider.entity.hexagon.HexagonMiniAppSiteSpreadEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface HexagonMiniAppSiteSpreadDao {
    @Insert("INSERT INTO enterprise_miniapp_site_spread_hexagon ("
            + "        \"id\",\n"
            + "        \"ea\",\n"
            + "        \"hexagon_site_id\",\n"
            + "        \"status\",\n"
            + "        \"type\",\n"
            + "        \"create_time\",\n"
            + "        \"update_time\"\n"
            + "        )\n"
            + "        VALUES (\n"
            + "        #{obj.id},\n"
            + "        #{obj.ea},\n"
            + "        #{obj.hexagonSiteId},\n"
            + "        #{obj.status},\n"
            + "        #{obj.type},\n"
            + "        now(),\n"
            + "        now()\n"
            + "        ) ON CONFLICT DO NOTHING;")
    int insertHexagonMiniAppSiteSpreadInfo(@Param("obj") HexagonMiniAppSiteSpreadEntity hexagonActivityCenterEntity);


    @Select(" SELECT A.* from enterprise_miniapp_site_spread_hexagon AS A JOIN hexagon_site AS B ON A.hexagon_site_id = B.id WHERE A.ea = #{ea} AND A.type = #{type} AND B.status = 1 ORDER BY A.create_time DESC")
    List<HexagonMiniAppSiteSpreadEntity> getHexagonMiniAppSiteSpreadInfoByEa(@Param("ea") String ea, @Param("type") Integer type);
}
