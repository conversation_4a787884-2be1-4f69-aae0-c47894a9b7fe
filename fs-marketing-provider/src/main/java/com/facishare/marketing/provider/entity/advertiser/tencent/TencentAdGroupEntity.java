package com.facishare.marketing.provider.entity.advertiser.tencent;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TencentAdGroupEntity implements Serializable {
    private String id;
    private String ea;
    private String adAccountId;
    private Long campaignId;
    private Long adgroupId;
    private String adgroupName;
    private Integer status;
    private String subMarketingEventId;
    private String marketingEventId;
    private Double bidAmount;
    private Double totalBudget;
    private Double dailyBudget;
    private Integer bidMode;
    private List<String> siteSet;
    private Boolean isDeleted;
    private Date createTime;
    private Date updateTime;
}
