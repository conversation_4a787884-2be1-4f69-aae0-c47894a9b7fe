package com.facishare.marketing.provider.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.open.app.center.api.service.QueryAppAdminService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.organization.adapter.api.business.model.organization.GetSeenEmployeeIds;
import com.facishare.organization.adapter.api.business.service.OrganizationService;
import com.facishare.organization.adapter.api.model.biz.RunStatus;
import com.facishare.organization.adapter.api.model.biz.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.adapter.api.model.biz.employee.arg.GetAllEmployeeIdsArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.adapter.api.model.biz.employee.result.GetAllEmployeeIdsResult;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.BatchGetDepartmentDtoArg;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.model.department.arg.GetOrderlyUpperDepartmentArg;
import com.facishare.organization.api.model.department.result.BatchGetDepartmentDtoResult;
import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
import com.facishare.organization.api.model.department.result.GetOrderlyUpperDepartmentResult;
import com.facishare.organization.api.model.departmentmember.MainDepartment;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeesDtoByDepartmentIdArg;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeesDtoByDepartmentIdResult;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created By tianh, 2018/5/30.
 **/
@Service
@Slf4j
public class AuthManager {
    @Autowired
    private com.facishare.organization.adapter.api.service.EmployeeService organizationEmployeeService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private DepartmentProviderService departmentProviderService;
    @Autowired
    private QueryAppAdminService appAdminService;
    @Autowired
    private EIEAConverter eieaConverter;
    @ReloadableProperty("marketing_appid")
    private String appId;
    @ReloadableProperty("marketing_large_address_book_enterprises")
    private String largeAddressBookEnterprises;
    //默认全部
    public static final int defaultAllDepartment = 999999;

    /**
     * 隔离通讯录数据
     */
    public List<EmployeeDto> getIsolationAllEmployee(String ea, Integer currentUserId, List<Integer> departmentIds) {
        List<EmployeeDto> resultList = new ArrayList<>();
        int ei = eieaConverter.enterpriseAccountToId(ea);
        log.info("ea ,{},fsEi ,{}", ea, ei);
        BatchGetEmployeesDtoByDepartmentIdArg arg = new BatchGetEmployeesDtoByDepartmentIdArg();
        arg.setEnterpriseId(ei);
        arg.setMainDepartment(MainDepartment.ALL);//员工的部门属性
        arg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);//员工状态
        arg.setIncludeLowDepartment(true);//是否向下递归下级部门
        boolean isolation = organizationService.isOpenIsolation(ei);
        /**
         * 1.如果不选择部门，默认公司所有部门
         * 2.选择部门，会把主部门下所有子部门也筛选出来
         * **/
        if (departmentIds == null || departmentIds.isEmpty()) {
            List<Integer> list = new ArrayList<>();
            list.add(defaultAllDepartment);
            arg.setDepartmentIds(list);//部门ids
        } else {
            arg.setDepartmentIds(departmentIds);//部门ids
        }

        // 通讯录人数庞大的企业
        if (StringUtils.isNotEmpty(largeAddressBookEnterprises) && largeAddressBookEnterprises.contains(ea) && Objects.equals(arg.getDepartmentIds().get(0), defaultAllDepartment)) {
            com.facishare.organization.api.model.employee.arg.GetAllEmployeeIdsArg getAllEmployeeIdsArg = new com.facishare.organization.api.model.employee.arg.GetAllEmployeeIdsArg();
            getAllEmployeeIdsArg.setEnterpriseId(ei);
            getAllEmployeeIdsArg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
            com.facishare.organization.api.model.employee.result.GetAllEmployeeIdsResult getAllEmployeesDtoResult = employeeProviderService.getAllEmployeeIds(getAllEmployeeIdsArg);
            if (getAllEmployeesDtoResult == null || CollectionUtils.isEmpty(getAllEmployeesDtoResult.getEmployeeIds())) {
                log.warn("largeAddressBookEnterprises AuthManager.getIsolationAllEmployee.employeeProviderService.getAllEmployeeIds fail, ea:{}", ea);
                return resultList;
            }
            log.info("largeAddressBookEnterprises getAllEmployeesDtoResult.getEmployeeIds.size:{}", getAllEmployeesDtoResult.getEmployeeIds().size());
            PageUtil<Integer> pageUtil = new PageUtil<>(getAllEmployeesDtoResult.getEmployeeIds(), 5000);
            for (int i = 1; i <= pageUtil.getPageCount(); i++) {
                List<Integer> pageEmployeeIds = pageUtil.getPagedList(i);
                com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg();
                batchGetEmployeeDtoArg.setEmployeeIds(pageEmployeeIds);
                batchGetEmployeeDtoArg.setEnterpriseId(ei);
                batchGetEmployeeDtoArg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
                com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult batchGetEmployeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
                if (batchGetEmployeeDtoResult == null || batchGetEmployeeDtoResult.getEmployeeDtos().isEmpty()) {
                    log.info("largeAddressBookEnterprises BatchGetEmployeeDtoResult is  null, ea:{}", ea);
                    return resultList;
                }
                //判断企业是否需要筛选隔离数据
                if (!isolation) {
                    resultList.addAll(batchGetEmployeeDtoResult.getEmployeeDtos());
                    continue;
                }
                //筛选通讯录隔离数据 currentUserId只能查询到自己可见的员工
                GetSeenEmployeeIds.Argument argument = new GetSeenEmployeeIds.Argument();
                argument.setCurrentEmployeeId(currentUserId);
                argument.setEnterpriseId(ei);
                argument.setEmployeeIds(pageEmployeeIds);
                GetSeenEmployeeIds.Result result = organizationService.getSeenEmployeeIds(argument);
                if (result.getEmployeeIds() == null || result.getEmployeeIds().isEmpty()) {
                    log.info("largeAddressBookEnterprises GetSeenEmployeeIds.Result is null, ea:{}", ea);
                    return resultList;
                }
                List<Integer> employeeIds = Lists.newArrayList(result.getEmployeeIds());
                log.info("largeAddressBookEnterprises Isolation is after,{}", employeeIds.size());
                batchGetEmployeeDtoArg.setEmployeeIds(employeeIds);
                batchGetEmployeeDtoArg.setEnterpriseId(ei);
                batchGetEmployeeDtoArg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
                com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult batchGetSeenEmployeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
                if (batchGetSeenEmployeeDtoResult == null || batchGetSeenEmployeeDtoResult.getEmployeeDtos().isEmpty()) {
                    log.info("largeAddressBookEnterprises batchGetSeenEmployeeDtoResult is  null, ea:{}", ea);
                    return resultList;
                }
                resultList.addAll(batchGetSeenEmployeeDtoResult.getEmployeeDtos());
            }
        } else {
            BatchGetEmployeesDtoByDepartmentIdResult bresult = employeeProviderService.batchGetEmployeesByDepartmentId(arg);
            if (bresult == null || bresult.getEmployeeDtos().isEmpty()) {
                log.info("BatchGetEmployeesDtoByDepartmentIdResult is  null ");
                return resultList;
            }
            //判断企业是否需要筛选隔离数据
            if (!isolation) {
                resultList.addAll(bresult.getEmployeeDtos());
                return resultList;
            }
            List<Integer> employeeIdsAll = bresult.getEmployeeDtos().stream().map(EmployeeDto::getEmployeeId).collect(Collectors.toList());
            //筛选通讯录隔离数据 currentUserId只能查询到自己可见的员工
            log.info("Isolation is before,{}", employeeIdsAll.size());
            GetSeenEmployeeIds.Argument argument = new GetSeenEmployeeIds.Argument();
            argument.setCurrentEmployeeId(currentUserId);
            argument.setEnterpriseId(ei);
            argument.setEmployeeIds(employeeIdsAll);
            GetSeenEmployeeIds.Result result = organizationService.getSeenEmployeeIds(argument);
            if (result.getEmployeeIds() == null || result.getEmployeeIds().isEmpty()) {
                log.info(" GetSeenEmployeeIds.Result is null ");
                return resultList;
            }
            List<Integer> employeeIds = Lists.newArrayList(result.getEmployeeIds());
            log.info("Isolation is after,{}", employeeIds.size());
            com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg();
            batchGetEmployeeDtoArg.setEmployeeIds(employeeIds);
            batchGetEmployeeDtoArg.setEnterpriseId(ei);
            batchGetEmployeeDtoArg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
            com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult batchGetSeenEmployeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
            if (batchGetSeenEmployeeDtoResult == null || batchGetSeenEmployeeDtoResult.getEmployeeDtos().isEmpty()) {
                log.info("BatchGetEmployeeDtoResult is  null ");
                return resultList;
            }
            resultList.addAll(batchGetSeenEmployeeDtoResult.getEmployeeDtos());
        }
        log.info("Isolation resultList.size={}", resultList.size());
        return resultList;
    }

    public List<Integer> getIsolationAllEmployeeIdList(String ea, Integer currentUserId, List<Integer> departmentIds) {
        List<Integer> resultList = new ArrayList<>();
        int ei = eieaConverter.enterpriseAccountToId(ea);
        BatchGetEmployeesDtoByDepartmentIdArg arg = new BatchGetEmployeesDtoByDepartmentIdArg();
        arg.setEnterpriseId(ei);
        arg.setMainDepartment(MainDepartment.ALL);//员工的部门属性
        arg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);//员工状态
        arg.setIncludeLowDepartment(true);//是否向下递归下级部门
        boolean isolation = organizationService.isOpenIsolation(ei);
        /**
         * 1.如果不选择部门，默认公司所有部门
         * 2.选择部门，会把主部门下所有子部门也筛选出来
         * **/
        if (departmentIds == null || departmentIds.isEmpty()) {
            List<Integer> list = new ArrayList<>();
            list.add(defaultAllDepartment);
            arg.setDepartmentIds(list);//部门ids
        } else {
            arg.setDepartmentIds(departmentIds);//部门ids
        }

        // 通讯录人数庞大的企业
        if (StringUtils.isNotEmpty(largeAddressBookEnterprises) && largeAddressBookEnterprises.contains(ea) && Objects.equals(arg.getDepartmentIds().get(0), defaultAllDepartment)) {
            com.facishare.organization.api.model.employee.arg.GetAllEmployeeIdsArg getAllEmployeeIdsArg = new com.facishare.organization.api.model.employee.arg.GetAllEmployeeIdsArg();
            getAllEmployeeIdsArg.setEnterpriseId(ei);
            getAllEmployeeIdsArg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
            com.facishare.organization.api.model.employee.result.GetAllEmployeeIdsResult getAllEmployeesDtoResult = employeeProviderService.getAllEmployeeIds(getAllEmployeeIdsArg);
            if (getAllEmployeesDtoResult == null || CollectionUtils.isEmpty(getAllEmployeesDtoResult.getEmployeeIds())) {
                log.warn("largeAddressBookEnterprises AuthManager.getIsolationAllEmployeeIdList fail, ea:{}", ea);
                return resultList;
            }
            log.info("largeAddressBookEnterprises getAllEmployeesDtoResult.getEmployeeIds.size:{}", getAllEmployeesDtoResult.getEmployeeIds().size());
            PageUtil<Integer> pageUtil = new PageUtil<>(getAllEmployeesDtoResult.getEmployeeIds(), 5000);
            for (int i = 1; i <= pageUtil.getPageCount(); i++) {
                List<Integer> pageEmployeeIds = pageUtil.getPagedList(i);
                //判断企业是否需要筛选隔离数据
                if (!isolation) {
                    resultList.addAll(pageEmployeeIds);
                    continue;
                }
                //筛选通讯录隔离数据 currentUserId只能查询到自己可见的员工
                GetSeenEmployeeIds.Argument argument = new GetSeenEmployeeIds.Argument();
                argument.setCurrentEmployeeId(currentUserId);
                argument.setEnterpriseId(ei);
                argument.setEmployeeIds(pageEmployeeIds);
                GetSeenEmployeeIds.Result result = organizationService.getSeenEmployeeIds(argument);
                if (result == null || CollectionUtils.isEmpty(result.getEmployeeIds())) {
                    log.info("largeAddressBookEnterprises getIsolationAllEmployeeIdList GetSeenEmployeeIds.Result is null, ea:{}", ea);
                    continue;
                }
                log.info("largeAddressBookEnterprises Isolation is before: {} after {}", pageEmployeeIds.size(), result.getEmployeeIds().size());
                resultList.addAll(result.getEmployeeIds());
            }
        } else {
            BatchGetEmployeesDtoByDepartmentIdResult employeesDtoByDepartmentIdResult = employeeProviderService.batchGetEmployeesByDepartmentId(arg);
            if (employeesDtoByDepartmentIdResult == null || CollectionUtils.isEmpty(employeesDtoByDepartmentIdResult.getEmployeeDtos())) {
                log.info("BatchGetEmployeesDtoByDepartmentIdResult is  null ");
                return resultList;
            }
            List<Integer> employeeIdsAll = employeesDtoByDepartmentIdResult.getEmployeeDtos().stream().map(EmployeeDto::getEmployeeId).collect(Collectors.toList());
            //判断企业是否需要筛选隔离数据
            if (!isolation) {
                resultList.addAll(employeeIdsAll);
                return resultList;
            }

            //筛选通讯录隔离数据 currentUserId只能查询到自己可见的员工
            log.info("Isolation is ea: {} before {}", ea, employeeIdsAll.size());
            GetSeenEmployeeIds.Argument argument = new GetSeenEmployeeIds.Argument();
            argument.setCurrentEmployeeId(currentUserId);
            argument.setEnterpriseId(ei);
            argument.setEmployeeIds(employeeIdsAll);
            GetSeenEmployeeIds.Result result = organizationService.getSeenEmployeeIds(argument);
            if (result == null || CollectionUtils.isEmpty(result.getEmployeeIds())) {
                log.info(" GetSeenEmployeeIds.Result is null");
                return resultList;
            }
            resultList.addAll(result.getEmployeeIds());
        }
        log.info("Isolation ea: {} resultList.size = {}", ea, resultList.size());
        return resultList;
    }

    /**
     * 获取一个企业所有员工信息(包含隔离的通讯录)
     */
    public List<EmployeeDto> getAllEmployee(Integer ei, Integer currentUserId) {
        GetAllEmployeeIdsArg getAllEmployeeIdsArg = new GetAllEmployeeIdsArg();
        getAllEmployeeIdsArg.setRunStatus(RunStatus.ACTIVE);
        getAllEmployeeIdsArg.setEnterpriseId(ei);
        getAllEmployeeIdsArg.setCurrentEmployeeId(currentUserId);
        GetAllEmployeeIdsResult getAllEmployeeIdsResult = organizationEmployeeService.getAllEmployeeIds(getAllEmployeeIdsArg);
        if (getAllEmployeeIdsResult == null) {
            log.error("getAllEmployeeIdsResult is null, ei={}, currentUserId={}", ei, currentUserId);
            return null;
        }
        List<Integer> employeeIds = getAllEmployeeIdsResult.getEmployeeIds();
        if (CollectionUtils.isEmpty(employeeIds)) {
            log.error("employeeIds is null, getAllEmployeeIdsResult={}", getAllEmployeeIdsResult.toString());
            return null;
        }

        List<EmployeeDto> resultList = new ArrayList<>();
        int pointsDataLimit = 1000;
        List<List<Integer>> employeeIdsLists = Lists.partition(employeeIds, pointsDataLimit);
        log.info("employeeIdsLists.size={}", employeeIdsLists.size());
        for (List<Integer> employeeIdsListsUnit : employeeIdsLists) {
            log.info("employeeIdsListsUnit.size={}", employeeIdsListsUnit.size());
            BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
            batchGetEmployeeDtoArg.setEmployeeIds(employeeIdsListsUnit);
            batchGetEmployeeDtoArg.setRunStatus(RunStatus.ACTIVE);
            batchGetEmployeeDtoArg.setEnterpriseId(ei);
            batchGetEmployeeDtoArg.setCurrentEmployeeId(currentUserId);
            BatchGetEmployeeDtoResult batchGetEmployeeDtoResult = organizationEmployeeService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
            if (batchGetEmployeeDtoResult == null) {
                log.error("batchGetEmployeeDtoResult is null, batchGetEmployeeDtoArg={}, resultList={}", batchGetEmployeeDtoArg.toString(), resultList);
                return null;
            }
            List<EmployeeDto> list = batchGetEmployeeDtoResult.getEmployees();
            if (CollectionUtils.isEmpty(list)) {
                log.error("getList is null, resultList={}", resultList);
                return null;
            }
            resultList.addAll(list);
        }
        log.info("resultList.size={}", resultList.size());
        return resultList;
    }

    public List<Integer> getAppAdmins(String ea) {
        com.facishare.open.common.result.BaseResult<List<FsUserVO>> tmpFsUserList = appAdminService.findAppAdminListByAppId(ea, appId);
        if (!tmpFsUserList.isSuccess()) {
            log.error("Invoke appAdminService:findAppAdminListByAppId failed. ea={}, result={}", ea, tmpFsUserList);
            return null;
        }

        return tmpFsUserList.getResult().stream().map(userVo -> userVo.getUserId()).collect(Collectors.toList());
    }

    /**
     * 获取员工所在部门(包含主属和附属)
     * @param ei
     * @param currentUserId
     * @return
     */
    public List<Integer> getDepartmentIds(Integer ei, Integer currentUserId){
        GetEmployeeDtoArg getEmployeeDtoArg = new GetEmployeeDtoArg();
        getEmployeeDtoArg.setEnterpriseId(ei);
        getEmployeeDtoArg.setEmployeeId(currentUserId);
        GetEmployeeDtoResult employeeDto = employeeProviderService.getEmployeeDto(getEmployeeDtoArg);
        if (Objects.nonNull(employeeDto)) {
            return employeeDto.getEmployeeDto().getDepartmentIds();
        }
        return Lists.newArrayList();
    }

    /**
     * 获取员工所在部门及级联上级部门(包含主属和附属)
     * @param ei
     * @param currentUserId
     * @return
     */
    public List<Integer> getDepartmentIdsIncludeUp(Integer ei, Integer currentUserId){
        Set<Integer> departmentIds = Sets.newHashSet();
        // 查询员工所在部门
        GetEmployeeDtoArg getEmployeeDtoArg = new GetEmployeeDtoArg();
        getEmployeeDtoArg.setEnterpriseId(ei);
        getEmployeeDtoArg.setEmployeeId(currentUserId);
        GetEmployeeDtoResult employeeDto = employeeProviderService.getEmployeeDto(getEmployeeDtoArg);
        if (Objects.nonNull(employeeDto) && Objects.nonNull(employeeDto.getEmployeeDto())) {
            List<Integer> currentDepartmentIds = employeeDto.getEmployeeDto().getDepartmentIds();
            // 有部门
            if (CollectionUtils.isNotEmpty(currentDepartmentIds)) {
                departmentIds.addAll(currentDepartmentIds);
                for (Integer currentDepartmentId : currentDepartmentIds) {
                    // 获取部门信息，里面包含上级部门
                    GetDepartmentDtoArg arg = new GetDepartmentDtoArg();
                    arg.setEnterpriseId(ei);
                    arg.setDepartmentId(currentDepartmentId);
                    GetDepartmentDtoResult departmentDto = departmentProviderService.getDepartmentDto(arg);
                    if (Objects.nonNull(departmentDto) && Objects.nonNull(departmentDto.getDepartment())) {
                        DepartmentDto dto = departmentDto.getDepartment();
                        if (CollectionUtils.isNotEmpty(dto.getAncestors())) {
                            departmentIds.addAll(dto.getAncestors());
                        }
                    }
                }
            }
        }
        return Lists.newArrayList(departmentIds);
    }

    /**
     * 根据部门id集合批量获取部门信息
     * @param ei
     * @param departmentIds
     * @return
     */
    public List<DepartmentDto> batchGetByDepartmentIds(Integer ei, List<Integer> departmentIds){
        if (CollectionUtils.isEmpty(departmentIds)) {
            return Lists.newArrayList();
        }
        BatchGetDepartmentDtoArg arg = new BatchGetDepartmentDtoArg();
        arg.setDepartmentIds(departmentIds);
        arg.setEnterpriseId(ei);
        BatchGetDepartmentDtoResult result = departmentProviderService.batchGetDepartmentDto(arg);
        if (Objects.nonNull(result)) {
            return result.getDepartments();
        }
        return Lists.newArrayList();
    }

    /**
     * 获取员工的主属部门以及主属部门的上级级联部门，有序（例如：全公司->1级部门->...->父部门->当前部门)）
     * @param ei
     * @param currentUserId
     * @return
     */
    public List<Integer> getUserMainDepartmentsOrderly(Integer ei, Integer currentUserId){
        List<Integer> departmentIds = Lists.newArrayList();
        // 查询员工所在部门
        GetEmployeeDtoArg getEmployeeDtoArg = new GetEmployeeDtoArg();
        getEmployeeDtoArg.setEnterpriseId(ei);
        getEmployeeDtoArg.setEmployeeId(currentUserId);
        GetEmployeeDtoResult employeeDto = employeeProviderService.getEmployeeDto(getEmployeeDtoArg);
        if (Objects.nonNull(employeeDto) && Objects.nonNull(employeeDto.getEmployeeDto())) {
            // 主属部门
            Integer currentDepartmentId = employeeDto.getEmployeeDto().getMainDepartmentId();
            GetOrderlyUpperDepartmentArg arg = new GetOrderlyUpperDepartmentArg();
            arg.setEnterpriseId(ei);
            arg.setSelf(true);
            arg.setDepartmentId(currentDepartmentId);
            GetOrderlyUpperDepartmentResult orderlyUpperDepartment = departmentProviderService.getOrderlyUpperDepartment(arg);
            if (orderlyUpperDepartment != null) {
                List<DepartmentDto> departments = orderlyUpperDepartment.getDepartments();
                if (CollectionUtils.isNotEmpty(departments)) {
                    for (DepartmentDto department : departments) {
                        departmentIds.add(department.getDepartmentId());
                    }
                }
            }
        }
        // 顺序反转
        if (CollectionUtils.isNotEmpty(departmentIds)) {
            Collections.reverse(departmentIds);
        }
        return departmentIds;
    }
}
