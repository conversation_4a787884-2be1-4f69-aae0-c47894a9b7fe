package com.facishare.marketing.provider.entity.data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.common.typehandlers.value.TagName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019-09-17
 */
@Data
public class WeChatTemplateMessageData implements Serializable {
    private String title;  //推广标题
    /** 0-旧文章类型，1-素材类型，2-网页类型，3-小程序类型。 */
    private Integer redirectType;
    /** 小程序appId ，redirectType = 3时使用 */
    private String miniAppId;
    /** 小程序pagePath ，redirectType = 3时使用 */
    private String miniAppPagePath;
    /** 重定向页面 */
    private String redirectUrl;
    /** 素材Id */
    private String materialId;
    /** 素材Type */
    private Integer materialType;
    /** 发送范围  0-全部 1-筛选条件 2-CRM客户 3-指定wxAppId + wxOpenId列表 */
    private Integer sendRange;
    /** 人群id  只有sendRange=4才有效 */
    private List<String> marketingUserGroupIds;
    /** 旧文章文本内容 */
    private String msgContent;
    /** 图片 */
    private String nPath;
    /** 微联服务好AppId */
    private String appId;
    /** 发送时间 */
    private Long fixedTime;
    /** 过滤掉N天内发送过的用户， 为空表示不过滤 */
    private Integer filterNDaySentUser;
    /**
     * 模版Id
     */
    private String weChatOfficialTemplateId;
    /**
     * 模板消息内容
     */
    private com.facishare.marketing.api.vo.WeChatTemplateMessageData.TemplateMessageDatas templateMessageDatas;
    /** 发送类型 1：立即发送 2：定时发送 */
    private Integer type;
    // 是否旧数据迁移，数据迁移用
    private Boolean migration = false;
    /** 过滤条件 */
    private List<Map<String, Object>> filters;
    /** 标签筛选 */
    private List<TagName> tagIdList;
    private List<String> campaignIds;
}
