package com.facishare.marketing.provider.dao.conference;

import com.facishare.marketing.provider.entity.conference.ConferenceUserGroupEntity;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Created  By zhoux 2019/11/26
 **/
public interface ConferenceUserGroupDAO {

    @Insert("INSERT INTO conference_user_group (\n"
        + "        \"ea\",\n"
        + "        \"conference_id\",\n"
        + "        \"group_name\",\n"
        + "        \"status\",\n"
        + "        \"create_by\",\n"
        + "        \"create_time\",\n"
        + "        \"update_by\",\n"
        + "        \"update_time\"\n"
        + "        )\n"
        + "        VALUES (\n"
        + "        #{ea},\n"
        + "        #{conferenceId},\n"
        + "        #{groupName},\n"
        + "        #{status},\n"
        + "        #{createBy},\n"
        + "        now(),\n"
        + "        #{updateBy},\n"
        + "        now()\n"
        + "        )")
    @Options(useGeneratedKeys=true, keyProperty = "id", keyColumn = "id")
    Integer insertConferenceUserGroup(ConferenceUserGroupEntity conferenceUserGroupEntity);

    @Select("SELECT * FROM conference_user_group WHERE id = #{id}")
    ConferenceUserGroupEntity getConferenceUserGroupById(@Param("id") Integer id);

    @Select("SELECT * FROM conference_user_group WHERE ea = #{ea} AND conference_id = #{conferenceId} AND group_name = #{groupName}")
    ConferenceUserGroupEntity getConferenceUserGroupByEaConferenceIdAndName(@Param("ea") String ea, @Param("conferenceId") String conferenceId, @Param("groupName") String groupName);

    @Select(" SELECT * FROM conference_user_group WHERE ea = #{ea} AND conference_id = #{conferenceId}")
    List<ConferenceUserGroupEntity> getConferenceUserGroupByEaAndConferenceId(@Param("ea") String ea, @Param("conferenceId") String conferenceId);

}
