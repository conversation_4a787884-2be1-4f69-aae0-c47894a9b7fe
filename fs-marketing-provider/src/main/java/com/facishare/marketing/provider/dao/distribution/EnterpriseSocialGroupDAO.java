package com.facishare.marketing.provider.dao.distribution;

import com.facishare.marketing.provider.entity.distribution.EnterpriseSocialGroup;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface EnterpriseSocialGroupDAO {
	@Select("SELECT * FROM enterprise_social_group WHERE uid = #{uid} and fs_ea = #{ea}")
	List<EnterpriseSocialGroup> queryEnterpriseSocialGroupByEaAndUid(@Param("ea") String ea, @Param("uid") String uid);
}
