package com.facishare.marketing.provider.crowd.dao;

import com.facishare.marketing.provider.crowd.entity.MarketingEventMarketingUserGroupRelationEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

public interface MarketingEventMarketingUserGroupRelationDao {

    @Select("<script>" +
            "select * from marketing_event_marketing_user_group_relation where marketing_event_id = #{marketingEventId}" +
            "</script>")
    MarketingEventMarketingUserGroupRelationEntity getByMarketingEventId(@Param("marketingEventId") String marketingEventId);

    @Select("<script>" +
            "select * from marketing_event_marketing_user_group_relation where marketing_event_id in " +
            "<foreach collection='marketingEventIds' item='item'  open='(' separator=',' close=')'>#{item}</foreach>" +
            "</script>")
    List<MarketingEventMarketingUserGroupRelationEntity> getByMarketingEventIds(@Param("marketingEventIds") Set<String> marketingEventIds);

    @Insert("<script>" +
            "INSERT INTO marketing_event_marketing_user_group_relation\n" +
            "(id, ea, marketing_event_id, marketing_user_group_id, create_time, create_by, update_time, update_by)\n" +
            "VALUES(#{entity.id}, #{entity.ea}, #{entity.marketingEventId}, #{entity.marketingUserGroupId}, now(), #{entity.createBy}, now(), #{entity.createBy})\n" +
            "</script>")
    int insert(@Param("entity") MarketingEventMarketingUserGroupRelationEntity entity);
}
