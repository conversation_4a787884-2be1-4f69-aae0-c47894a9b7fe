package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.SystemOperationRecordEntity;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmPageResult;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * Created on 2021-01-12.
 */

public interface SystemOperationRecordDao {
	@Insert("INSERT INTO system_operation_record(id, ea, \"module\", module_target_id, \"operator\", operation_type, description, create_time) " +
		" VALUES(#{id}, #{ea}, #{module}, #{moduleTargetId}, #{operator}, #{operationType}, #{description}, NOW())")
	int insert(@Param("id") String id, @Param("ea") String ea, @Param("module")String module, @Param("moduleTargetId") String moduleTargetId,
	           @Param("operator") Integer operator, @Param("operationType")String operationType, @Param("description") String description);
	
	@Select("<script>" +
		"SELECT * FROM system_operation_record WHERE ea=#{ea} AND \"module\"=#{module} AND module_target_id=#{moduleTargetId} " +
		" <if test='operationType!=null'> AND operation_type=#{operationType} </if>" +
		" ORDER BY create_time DESC" +
		"</script>")
	List<SystemOperationRecordEntity> listByModuleTargetIdAndOperationType(@Param("ea") String ea, @Param("module")String module, @Param("moduleTargetId") String moduleTargetId, @Param("operationType")String operationType, Page page);
}
