package com.facishare.marketing.provider.entity.advertiser.headlines;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021/8/9 1:10 上午
 */
@Data
@ToString
public class HeadlinesAdDataEntity implements Serializable {
    private String id;
    private String ea;
    private Long adId;
    private String adAccountId;
    private Long campaignId;
    private Date statDatetime;
    private Long show;
    private Long click;
    private Double cost;
    private Double avgClickCost;
    private Integer leads;
    private Date createTime;
    private Date updateTime;
    private String subMarketingEventId;
    private String type;
//    private String subMarketingEventName;
}
