package com.facishare.marketing.provider.innerResult.ding;

import com.facishare.marketing.api.result.dingding.DingBaseResult;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 2021/10/20 2:27 下午
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DingDepartmentUserIdsResult extends DingBaseResult {
    /**
     * 请求ID。
     */
    @SerializedName("request_id")
    private String requestId;

    /**
     *
     *  返回结果
     */
    @SerializedName("result")
    private ListUserByDeptResponse result;

    @Data
    public static class ListUserByDeptResponse implements Serializable {

        /**
         * 指定部门的userid列表
         */
        @SerializedName("userid_list")
        private String[] useridList;
    }
}
