package com.facishare.marketing.provider.mq.handler;

import com.alibaba.fastjson.JSONObject;
import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.open.msg.model.base.ProtoBase;
import io.protostuff.Tag;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

@Slf4j
@Component
public class CrmAppViewChangeHandler extends AbstractMessageHandler<CrmAppViewChangeHandler.AppViewChangeEvent> {

    @Autowired
    private UserRelationManager userRelationManager;

    @Override
    protected CrmAppViewChangeHandler.AppViewChangeEvent getMsgObj(MessageExt msg) {
        return JSONObject.parseObject(msg.getBody(), AppViewChangeEvent.class);
    }

    @Override
    protected String getEa(CrmAppViewChangeHandler.AppViewChangeEvent msgObj) {
        return msgObj.getFsEa();
    }

    @Override
    protected void directHandle(CrmAppViewChangeHandler.AppViewChangeEvent msgObj) {
        // 营销通的应用范围变更，处理userRelation
        userRelationManager.handleYxtAppViewChange(msgObj, true);
    }



    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AppViewChangeEvent implements Serializable {

        private String fsEa;

        private String appId;

        private String componentId;
        /**
         * 添加全公司可见
         */
        private Boolean addEaAuth;
        /**
         * 删除全公司可见
         */
        private Boolean delEaAuth;
        /**
         * 添加可见部门
         */
        private List<Integer> insertDepartmentList;
        /**
         * 删除可见部门
         */
        private List<Integer> deleteDepartmentList;
        /**
         * 添加可见人员
         */
        private List<Integer> insertUserList;
        /**
         * 删除可见人员
         */
        private List<Integer> deleteUserList;

        /**
         * @see AppViewChangeTypeEnum
         */
        private Integer changeType;

    }

    @Getter
    public enum AppViewChangeTypeEnum {
        /**
         * 组件新增
         */
        COMPONENT_ADD(1),
        /**
         * 组件删除
         */
        COMPONENT_DELETE(2),
        /**
         * 组件可见范围变更
         */
        COMPONENT_VIEW_CHANGE(3),
        /**
         * 组件信息变更（名称、描述、logo、url等）
         */
        COMPONENT_INFO_CHANGE(4),
        /**
         * 自建应用启用/停用
         */
        APP_BIND_STATUS_CHANGE(5)
        ;

        private final int type;

        AppViewChangeTypeEnum(int type) {
            this.type = type;
        }

    }
}
