package com.facishare.marketing.provider.service.kis;

import com.facishare.marketing.api.arg.kis.GetActivityDetailByObjectArg;
import com.facishare.marketing.api.result.kis.GetActivityDetailByObjectResult;
import com.facishare.marketing.api.service.kis.KisMarketingActivityService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.dto.kis.MarketingActivityObjectInfoDTO;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2019/03/05
 **/
@Slf4j
@Service("kisMarketingReportService")
public class KisMarketingActivityServiceImpl implements KisMarketingActivityService {

    @Autowired
    private MarketingActivityManager marketingActivityManager;

    @Override
    public Result<GetActivityDetailByObjectResult> getActivityDetailByObject(GetActivityDetailByObjectArg vo) {
        Map<String, Integer> object = Maps.newHashMap();
        object.put(vo.getObjectId(), vo.getObjectType());
        GetActivityDetailByObjectResult result = new GetActivityDetailByObjectResult();
        Map<String, List<MarketingActivityObjectInfoDTO.ActivityObjectInfo>> resultMap = marketingActivityManager.getActivityIdsByObject(object, vo.getEa(), vo.getFsUid());
        if (MapUtils.isNotEmpty(resultMap)) {
            result.setObjectId(vo.getObjectId());
            result.setDataList(BeanUtil.copy(resultMap.get(vo.getObjectId()), GetActivityDetailByObjectResult.ActivityObjectInfo.class));
        } else {
            result.setObjectId(vo.getObjectId());
            result.setDataList(Lists.newArrayList());
        }
        return Result.newSuccess(result);
    }
}
