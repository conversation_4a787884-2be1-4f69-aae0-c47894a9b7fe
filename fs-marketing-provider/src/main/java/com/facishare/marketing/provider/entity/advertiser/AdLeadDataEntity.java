package com.facishare.marketing.provider.entity.advertiser;

import com.facishare.marketing.provider.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
public class AdLeadDataEntity extends BaseEntity implements Serializable {

    private String adAccountId;

    // 线索名称
    private String leadName;

    // 线索id
    private String leadId;

    // 市场活动id
    private String marketingEventId;

    // 关键词id
    private String keywordId;

    // 转换后的客户id
    private String transferCustomerId;

    // 转换后的联系人id
    private String transferContactId;

    // 转换后的新商机id  2023-11-14 这个字段已经废弃 商机的相关数据在ad_lead_new_opportunity_data表中
    private String transferNewOpportunityId;

    // 线索的阶段
    private String leadsStage;

    // 线索的号码
    private String mobile;

    // 号码的归属省份
    private String mobileProvince;

    // 号码的归属城市
    private String mobileCity;

    // 线索的创建时间
    private Date leadCreateTime;

    // 线索的转换时间
    private Long transformTime;

    // 线索转MQL周期 以天为单位的毫秒数
    private Long changedToMqlPeriod;

    // 线索占MQL周期
    private Long changedToMqlTime;
}
