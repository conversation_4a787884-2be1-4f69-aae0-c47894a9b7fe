package com.facishare.marketing.provider.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class NewHeadLineAdListDTO implements Serializable {
    private String id;
    private String adAccountId;
    private Long campaignId;
    private String campaignName;
    private Long adId;
    private String adName;
    private Integer status;
    private String subMarketingEventId;
    private String marketingEventId;
    private Integer optStatus;
    private Integer deliveryRange;
    private Integer inventoryCatalog;
    private List<String> inventoryType;
    private Date createTime;
    private Date updateTime;
    private Date refreshTime;
    private Integer pv;
    private Integer click;
    private Double cost;
    private Double clickPrice;
}
