package com.facishare.marketing.provider.dao.hexagon;

import com.facishare.marketing.provider.entity.hexagon.HexagonBackgroudColorSettingEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface HexagonBackgroudColorSettingDAO {

    @Select("select * from hexagon_background_color_setting where ea = #{ea}")
    HexagonBackgroudColorSettingEntity queryByEa(@Param("ea") String ea);

    @Insert("insert into hexagon_background_color_setting(id, ea, background_color, status,create_time, update_time) values(#{obj.id}, #{obj.ea}, #{obj.backgroundColor}, #{obj.status}, now(), now())")
    int insert(@Param("obj") HexagonBackgroudColorSettingEntity hexagonBackgroudColorSettingEntity);

    @Update("update hexagon_background_color_setting set background_color = #{obj.backgroundColor}, update_time = now() where id = #{obj.id}")
    int updateBackgroundColor(@Param("obj") HexagonBackgroudColorSettingEntity hexagonBackgroudColorSettingEntity);
}
