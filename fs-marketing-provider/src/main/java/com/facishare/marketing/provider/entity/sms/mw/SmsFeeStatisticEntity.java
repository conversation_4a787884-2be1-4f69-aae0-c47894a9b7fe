package com.facishare.marketing.provider.entity.sms.mw;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;

/**
 * Created by ranluch on 2019/3/20.
 */
@Data
@Entity
public class SmsFeeStatisticEntity implements Serializable {
    // 公司账号
    private String ea;

    /**
     * {@link com.facishare.marketing.common.enums.sms.ChannelTypeEnum}
     */
    private Integer channelType;

    //消费短信数量
    private Integer totalFee;
}
