package com.facishare.marketing.provider.dao.conference;

import com.facishare.marketing.provider.entity.ConferenceTagEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface ConferenceTagDAO {
    @Insert("INSERT INTO conference_tag(id, conference_id, tags, create_time, update_time, ea)\n"
            + "        VALUES (#{id}, #{conferenceId}, #{tags}, now(), now(), #{ea})")
    int addConferenceTag(ConferenceTagEntity entity);

    @Select("select * from conference_tag where id = #{id}")
    ConferenceTagEntity queryById(@Param("id") String id);

    @Select("select * from conference_tag where conference_id = #{conferenceId} order by create_time desc limit 1")
    ConferenceTagEntity queryByConferenceId(@Param("conferenceId") String conferenceId);

    @Update("update conference_tag set ea = (select ea from activity act where act.id = conference_id limit 1) where ea is null")
    int refreshEaByActivityId();

    @Update("update conference_tag set ea = (select ea from activity act where act.marketing_event_id = conference_id limit 1) where ea is null")
    int refreshEaByMarketingEventId();
}
