package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.RecordUtmParamArg;
import com.facishare.marketing.api.service.BrowserUserService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.BrowserUserDao;
import com.facishare.marketing.provider.dao.UserMarketingAccountDAO;
import com.facishare.marketing.provider.dao.UserMarketingBrowserUserRelationDao;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.BrowserUserRelationManager;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;

@Service("browserUserService")
@Slf4j
public class BrowserUserServiceImpl implements BrowserUserService {
    @Autowired
    private BrowserUserDao browserUserDao;
    @Autowired
    private UserMarketingBrowserUserRelationDao userMarketingBrowserUserRelationDao;
    @Autowired
    private UserMarketingAccountDAO userMarketingAccountDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private BrowserUserRelationManager browserUserRelationManager;

    @Autowired
    private RedisManager redisManager;

    @ReloadableProperty("user_agent_forbid_token_list")
    private String userAgentForbidTokenList;

    @Override
    @FilterLog
    public Result<String> getIdentity(String userAgent) {
        if (userAgent != null && userAgent.length() > 2048) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Set<String> forbidTokenSet = Sets.newHashSet(JSONObject.parseArray(userAgentForbidTokenList, String.class));
        if (userAgent != null && forbidTokenSet.stream().anyMatch(e -> userAgent.contains(e)) || StringUtils.contains(userAgent, "/etc") || StringUtils.contains(userAgent, "../") || StringUtils.contains(userAgent, "..\\") ) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String browserUserId = UUIDUtil.getUUID();
        browserUserDao.insert(browserUserId, toEncodedString(userAgent), null);
        return Result.newSuccess(browserUserId);
    }


    @Override
    public Result<String> createIdentityByUserMarketingId(String ua, String userMarketingId) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(userMarketingId));
        UserMarketingAccountEntity userMarketingAccount = userMarketingAccountDAO.getById(userMarketingId);
        Preconditions.checkArgument(userMarketingAccount != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BROWSERUSERSERVICEIMPL_68));
        String browserUserId = UUIDUtil.getUUID();
        browserUserDao.insert(browserUserId, toEncodedString(ua), eieaConverter.enterpriseIdToAccount(userMarketingAccount.getTenantId()));
        userMarketingBrowserUserRelationDao.insertIgnore(UUIDUtil.getUUID(), eieaConverter.enterpriseIdToAccount(userMarketingAccount.getTenantId()), userMarketingId, browserUserId);
        return Result.newSuccess(browserUserId);
    }

    private String toEncodedString(String ua) {
        String encodedString = ua;
        if (StringUtils.isNotEmpty(ua)) {
            try {
                encodedString = StringUtils.toEncodedString(ua.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
            } catch (Exception e) {
                log.warn("toEncodedString error ua:{}", ua, e);
            }
        }
        return encodedString;
    }

    @Override
    public Result<Boolean> bindPhoneWithBrowserUserId(String ea, String browserUserId, String phone) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea) && !Strings.isNullOrEmpty(phone) && !Strings.isNullOrEmpty(browserUserId));
        return Result.newSuccess(browserUserRelationManager.bindPhoneWithBrowserUserId(ea, browserUserId, phone));
    }

    @Override
    public Result<Void> recordUtmParam(RecordUtmParamArg arg) {
        if(arg!=null && arg.checkParam()){
            redisManager.setUtmParam(arg.getVisitorId(),arg);
        }
        return null;
    }
}