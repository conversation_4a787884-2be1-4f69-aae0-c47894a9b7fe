package com.facishare.marketing.provider.entity;

import lombok.Data;
import org.codehaus.groovy.runtime.dgmimpl.arrays.IntegerArrayGetAtMetaMethod;

import java.io.Serializable;
import java.util.Date;

@Data
public class ObjectGroupEntity implements Serializable{
    private String id;          //主键
    private String ea;          //企业账号
    private Integer createBy;   //fsUserId;
    private String name;        //分组名称
    private Integer objectType; //物料类型
    private Integer status;     //分组状态
    private Date createTime;    //创建时间
    private Date updateTime;    //更新时间
    private boolean mobileDisplay; //是否在移动端展示
    private Integer innerVisible; // 企业内部可见性
    private Integer outerVisible; // 企业外部可见性
    private String parentId; //父id
    private Integer seqNo; //序号
    private Integer level; //层级
}
