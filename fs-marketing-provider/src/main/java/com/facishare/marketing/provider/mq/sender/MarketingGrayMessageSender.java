package com.facishare.marketing.provider.mq.sender;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.util.GsonUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
public class MarketingGrayMessageSender {

    /**
     * 配置格式:
     * marketing_gray_msg_handler_sender_mapping={"wxUserActionMessageHandler":"grayLightMessageSender",
     * "RequestBufferMessageHandler$QYWX_CALLBACK_TAG":"grayLightMessageSender"}
     * 解释:
     * 1. messageHandler=wxUserActionMessageHandler的消息, 由grayLightMessageSender发往MARKETING_GRAY_LIGHT_MESSAGE_QUEUE
     * 2. messageHandler=RequestBufferMessageHandler && tag=QYWX_CALLBACK_TAG的消息, 由grayLightMessageSender发往MARKETING_GRAY_LIGHT_MESSAGE_QUEUE
     */
    @ReloadableProperty("marketing_gray_msg_handler_sender_mapping")
    private String marketingGrayMsgHandlerSenderMapping;

    private final String defaultMarketingMessageSender = "grayMessageSender";

    @Autowired
    private EIEAConverter eieaConverter;

    /**
     * 说明:
     * 常规消息转发 grayMessageSender
     * 实时/消息转发 grayLightMessageSender
     */
    @Autowired
    private Map<String, MarketingMessageSender> senderMap;

    /**
     * 发送消息
     *
     * @param msgObj
     */
    public void send(String messageHandler, Object msgObj, String tags, String ea) {
        MarketingMessageSender sender = senderMap.get(defaultMarketingMessageSender);
        if (StringUtils.isNotEmpty(marketingGrayMsgHandlerSenderMapping)) {
            Map<String, String> marketingGrayQueueMap = GsonUtil.getGson().fromJson(marketingGrayMsgHandlerSenderMapping, new TypeToken<Map<String, String>>() {
            }.getType());
            // 先找指定到tag的配置, 如: RequestBufferMessageHandler$QYWX_CALLBACK_TAG
            MarketingMessageSender appointSender = null;
            if (StringUtils.isNotEmpty(tags)) {
                String appointSenderName = marketingGrayQueueMap.get(messageHandler + "$" + tags);
                appointSender = senderMap.get(appointSenderName);
            }
            // 再找指定到hadnler的配置
            if (appointSender == null) {
                appointSender = senderMap.get(marketingGrayQueueMap.get(messageHandler));
            }
            if (appointSender != null) {
                sender = appointSender;
            }
        }
        sender.send(messageHandler, msgObj, tags, ea);
    }

}
