package com.facishare.marketing.provider.entity.mail;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by zhengh on 2020/6/5.
 */
@Data
public class MailTemplateEntity implements Serializable{
    private String id;        //主键&邮件模板调用名称
    private String ea;        //企业账号
    private Integer fsUserId; //员工id
    private String name;      //模板名称
    private Integer status;   //模板状态
    private Integer type;     //0:批量  1:批量
    private Date createTime;  //创建时间
    private Date updateTime;  //更新时间
}
