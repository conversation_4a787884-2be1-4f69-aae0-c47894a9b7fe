package com.facishare.marketing.provider.dao.officialWebsite;

import com.facishare.marketing.provider.entity.officialWebsite.WxServiceQrCodeOfficialWebsiteRelationEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface WxServiceQrCodeOfficialWebsiteRelationDAO {

    @Select("select * from wx_service_qr_code_official_website_relation where ea=#{ea} and scene_id=#{sceneId}")
    WxServiceQrCodeOfficialWebsiteRelationEntity queryRelationByEaAndSceneId(@Param("ea") String ea, @Param("sceneId") String sceneId);

    @Insert("INSERT INTO wx_service_qr_code_official_website_relation (\n"
            + "        \"id\",\n"
            + "        \"ea\",\n"
            + "        \"scene_id\",\n"
            + "        \"main_scene_id\",\n"
            + "        \"browser_user_id\",\n"
            + "        \"create_time\",\n"
            + "        \"update_time\"\n"
            + "        )\n"
            + "        VALUES (\n"
            + "        #{entity.id},\n"
            + "        #{entity.ea},\n"
            + "        #{entity.sceneId},\n"
            + "        #{entity.mainSceneId},\n"
            + "        #{entity.browserUserId},\n"
            + "        now(),\n"
            + "        now()\n"
            + "        ) ON CONFLICT DO NOTHING;")
    int relateSceneIdAndBrowserUserId(@Param("entity")WxServiceQrCodeOfficialWebsiteRelationEntity entity);

    @Update("UPDATE wx_service_qr_code_official_website_relation SET scene_id=#{sceneId}, update_time=now() WHERE ea=#{ea} AND id=#{id}")
    int updateScenIdById(@Param("ea")String ea, @Param("id")String id, @Param("sceneId")String sceneId);


    @Select("SELECT * FROM wx_service_qr_code_official_website_relation WHERE ea=#{ea} AND main_scene_id=#{mainSceneId} AND browser_user_id=#{browserUserId}")
    WxServiceQrCodeOfficialWebsiteRelationEntity getByMainScenIdAndBrowserUserId(@Param("ea")String ea, @Param("mainSceneId")String mainSceneId, @Param("browserUserId")String browserUserId);
}
