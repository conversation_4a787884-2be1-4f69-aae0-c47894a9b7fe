package com.facishare.marketing.provider.innerData.live;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
public class XiaoetongLiveListInnerData implements Serializable{
    @SerializedName("page_count")
    private int pageCount;

    @SerializedName("total")
    private int total;

    @SerializedName("page")
    private int page;

    @SerializedName("list")
    private List<liveListEntity> list;

    @Data
    @ToString
    public static class liveListEntity implements Serializable {
        @SerializedName("id")
        private String id;               //直播id

        @SerializedName("title")
        private String title;            //直播名称

        @SerializedName("alive_type")
        private int aliveType;           //直播类型,10 横屏直播；11竖屏直播；12语音直播；13录播直播

        @SerializedName("purchase_count")
        private int purchaseCount;       //订阅量

        @SerializedName("reward_sum")
        private String rewardSum;           //打赏金额（元）

        @SerializedName("recycle_bin_state")
        private int rescycleBinState;    //直播商品状态：-1 全部； 0 已上架； 1 已下架；2 待上架

        @SerializedName("alive_start_at")
        private String startTime;        //直播开始时间

        @SerializedName("alive_state")
        private String aliveState;       //直播状态

        @SerializedName("page_url")
        private String pageUrl;          //直播地址

        @SerializedName("create_mode")
        private int createMode;          //直播课程类型：-1 全部；0 店铺课程； 1 转播课程

        @SerializedName("img_url")
        private String imgUrl;           //封面地址
    }
}
