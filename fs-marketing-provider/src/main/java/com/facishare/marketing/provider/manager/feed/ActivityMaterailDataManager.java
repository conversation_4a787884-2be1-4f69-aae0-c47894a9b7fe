package com.facishare.marketing.provider.manager.feed;

import com.facishare.marketing.api.data.material.ActivityBriefData;
import com.facishare.marketing.api.data.material.ProductBriefData;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.provider.dao.ActivityDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dto.hexagon.HexagonBaseInfoDTO;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.HexagonManager;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by ranluch on 2019/7/29.
 */
@Component
public class ActivityMaterailDataManager extends MaterailDataManager<ActivityBriefData> {
    @Autowired
    private ActivityDAO activityDAO;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private HexagonPageDAO hexagonPageDAO;

    @Override
    public List<ActivityBriefData> get(String ea,String... objectIds) {
        List<ActivityBriefData> dataList = Lists.newArrayList();
        List<ActivityEntity> activityEntities = activityDAO.getByIds(Lists.newArrayList(objectIds));
        if (CollectionUtils.isNotEmpty(activityEntities)) {
            List<String> imageUrls = Lists.newArrayList();
            activityEntities.forEach(activityEntity -> {
                ActivityBriefData briefData = new ActivityBriefData();
                briefData.setId(activityEntity.getId());
                briefData.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                briefData.setCreateTime(activityEntity.getCreateTime().getTime());
                briefData.setTitle(activityEntity.getTitle());
                briefData.setLocation(activityEntity.getLocation());
                PhotoEntity photoEntity = photoManager.querySinglePhotoByEa(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), activityEntity.getId(), activityEntity.getEa());
                if (photoEntity != null) {
                    briefData.setCoverImageThumbUrl(photoEntity.getThumbnailUrl());
                    briefData.setCoverImageThumbApath(photoEntity.getThumbnailPath());
                    briefData.setUpdateTime(photoEntity.getUpdateTime().getTime());
                    if (StringUtils.isNotBlank(photoEntity.getThumbnailPath())) {
                        imageUrls.add(photoEntity.getThumbnailPath());
                    }
                }
                briefData.setStartTime(activityEntity.getStartTime() != null ? activityEntity.getStartTime().getTime() : 0);
                briefData.setEndTime(activityEntity.getEndTime() != null ? activityEntity.getEndTime().getTime() : 0);
                briefData.setType(activityEntity.getType());
                briefData.setCreator(activityEntity.getCreateBy());
                briefData.setActivityDetailSiteId(activityEntity.getActivityDetailSiteId());
                dataList.add(briefData);
            });
            List<String> siteIds = activityEntities.stream().map(ActivityEntity::getActivityDetailSiteId).collect(Collectors.toList());
            List<HexagonPageEntity> homePages = hexagonPageDAO.getHomePageByIds(ea, siteIds);
            List<String> homePageIds = homePages.stream().map(HexagonPageEntity::getId).collect(Collectors.toList());
            Map<String, String> siteToPageMap = homePages.stream().collect(Collectors.toMap(HexagonPageEntity::getHexagonSiteId, HexagonPageEntity::getId, (v1, v2) -> v1));
            Map<String, Map<Integer, PhotoEntity>> map = photoManager.batchQueryPhotoByTypesAndIds(Lists.newArrayList(43, 44, 45), homePageIds);
            for (ActivityBriefData data : dataList) {
                String homeId = siteToPageMap.get(data.getActivityDetailSiteId());
                if(StringUtils.isEmpty(homeId)){
                    continue;
                }
                Map<Integer, PhotoEntity> photoEntityMap = map.get(homeId);
                if(photoEntityMap ==null){
                    continue;
                }
                if (photoEntityMap.get(43) != null) {
                    data.setSharePicMiniAppCutUrl(photoEntityMap.get(43).getThumbnailUrl());
                }
                if (photoEntityMap.get(44)  != null) {
                    data.setSharePicH5CutUrl(photoEntityMap.get(44).getThumbnailUrl());
                }
                if (photoEntityMap.get(45)  != null) {
                    data.setSharePicOrdinaryCutUrl(photoEntityMap.get(45).getThumbnailUrl());
                    //返回原图
                    data.setSharePicOrdinaryUrl(photoEntityMap.get(45).getUrl());
                }
            }
            Map<String, Long> coverMap = fileV2Manager.processImageSizes(ea, imageUrls);
            if(MapUtils.isNotEmpty(coverMap)){
                dataList.forEach(data -> {
                    if (data.getCoverImageThumbApath() != null && coverMap.containsKey(data.getCoverImageThumbApath())) {
                        data.setCoverSize(coverMap.get(data.getCoverImageThumbApath()));
                    }
                });
            }
        }
        return dataList;
    }

    @Override
    public Integer getType() {
        return ObjectTypeEnum.ACTIVITY.getType();
    }
}
