package com.facishare.marketing.provider.outservice;

import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.outapi.result.ShareContentResult;
import com.facishare.marketing.outapi.service.OutShareContentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by zhengh on 2020/12/9.
 */
@Service("outShareContentService")
@Slf4j
public class OutShareContentServiceImpl implements OutShareContentService {
    @Autowired
    private com.facishare.marketing.api.service.ShareContentService shareContentService;
    @Override
    public Result<ShareContentResult> getDetail(String ea, String objectId, Integer objectType) {
        Result<com.facishare.marketing.api.result.ShareContentResult> result = shareContentService.getDetail(ea, objectId, objectType);
        ShareContentResult outResult = new ShareContentResult();
        if (result.isSuccess()) {
            outResult.setTitle(result.getData().getTitle());
            outResult.setDescription(result.getData().getDescription());
            outResult.setImageUrl(result.getData().getImageUrl());
            outResult.setImagePath(result.getData().getImagePath());
            outResult.setThumbnailUrl(result.getData().getThumbnailUrl());
            outResult.setThumbnailPath(result.getData().getImagePath());
        }
        return Result.newSuccess(outResult);
    }
}
