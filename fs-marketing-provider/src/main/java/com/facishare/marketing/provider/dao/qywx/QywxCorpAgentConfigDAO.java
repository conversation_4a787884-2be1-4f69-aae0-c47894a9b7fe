package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created by ranluch on 2020/1/7.
 */
public interface QywxCorpAgentConfigDAO {
    @Insert("insert into qywx_corp_agent_config(\"id\", \"ea\", \"corpid\", \"agentid\", \"app_name\", \"secret\", \"self_app_token\", \"self_app_encoding_aes_key\", \"customer_contact_secret\", \"create_time\", " +
            "\"update_time\", \"customer_auth_url\", \"is_encrypt\", \"origin_corp_id\") values("
        + " #{obj.id},\n"
        + " #{obj.ea},\n"
        + " #{obj.corpid},\n"
        + " #{obj.agentid},\n"
        + " #{obj.appName},\n"
        + " #{obj.secret},\n"
        + " #{obj.selfAppToken},\n"
        + " #{obj.selfAppEncodingAesKey},\n"
        + " #{obj.customerContactSecret},\n"
        + " now(),\n"
        + " now(),\n"
        + " #{obj.customerAuthUrl},\n"
        + " #{obj.isEncrypt},\n"
        + " #{obj.originCorpId}\n"
        + ") ON CONFLICT DO NOTHING;")
    boolean addAccount(@Param("obj") QywxCorpAgentConfigEntity agentConfig);

    @Select("SELECT * FROM qywx_corp_agent_config WHERE id=#{id}")
    QywxCorpAgentConfigEntity queryAgentById(@Param("id") String id);

    @FilterLog
    @Select("SELECT * FROM qywx_corp_agent_config WHERE ea=#{ea}")
    QywxCorpAgentConfigEntity queryAgentByEa(@Param("ea") String ea);

    @Select("SELECT * FROM qywx_corp_agent_config")
    List<QywxCorpAgentConfigEntity> queryAllQywxCorp();

    @Update("UPDATE qywx_corp_agent_config SET app_name=#{appName}, agentid=#{agentId}, secret=#{secret}, self_app_token=#{selfAppToken}, self_app_encoding_aes_key=#{selfAppEncodingAesKey}, " +
            "customer_contact_secret=#{customerContactSecret}, update_time=now(), customer_auth_url = #{customerAuthUrl}, origin_corp_id = #{originCorpId}\n"
            + "WHERE id=#{id}")
    void updateConfigById(@Param("id")String id, @Param("appName")String appName, @Param("agentId")String agentId, @Param("secret")String secret,
                          @Param("selfAppToken")String selfAppToken, @Param("selfAppEncodingAesKey")String selfAppEncodingAesKey, @Param("customerContactSecret") String customerContactSecret,
                          @Param("customerAuthUrl") String customerAuthUrl, @Param("originCorpId") String originCorpId);

    @Select(" SELECT * FROM qywx_corp_agent_config WHERE corpid = #{corpId} ")
    QywxCorpAgentConfigEntity queryAgentBycorpId(@Param("corpId") String corpId);

    @Select(" SELECT * FROM qywx_corp_agent_config WHERE corpid = #{corpId} order by create_time asc ")
    List<QywxCorpAgentConfigEntity> listAgentByCorpId(@Param("corpId") String corpId);

    @Select("<script>"
        + " SELECT * FROM qywx_corp_agent_config "
        + "  <if test=\"ea != null\">\n"
        + "        WHERE ea = #{ea}\n"
        + "  </if>\n"
        + "</script>")
    List<QywxCorpAgentConfigEntity> queryQywxCorpAgentConfig(@Param("ea") String ea);

    @Update("UPDATE qywx_corp_agent_config SET corpid = #{corpId}, update_time=now() WHERE ea = #{ea}")
    int updateCorpIdByEa(@Param("corpId") String corpId, @Param("ea") String ea);

    @Update("UPDATE qywx_corp_agent_config SET agentid = #{agentId}, update_time=now() WHERE ea = #{ea}")
    void updateAgentIdByEa(@Param("agentId") String agentId, @Param("ea") String ea);

    @Update("UPDATE qywx_corp_agent_config SET customer_auth_url = #{customerAuthUrl}, update_time=now() WHERE ea = #{ea}")
    void updateCustomerAuthUrlByEa(@Param("customerAuthUrl") String customerAuthUrl, @Param("ea") String ea);

    @Select("<script>"
            + "SELECT * FROM qywx_corp_agent_config where 1 = 1 "
            + " <if test =\"eaList != null and eaList.size != 0\">\n"
            + "     and ea in "
            + "     <foreach open='(' close=')' separator=',' collection='eaList' index='idx'>"
            + "         #{eaList[${idx}]}"
            + "     </foreach>"
            + "</if>"
            + "</script>")
    List<QywxCorpAgentConfigEntity> queryQywxCorpByEaList(@Param("eaList") List<String> eaList);

    @Update("UPDATE qywx_corp_agent_config SET confirm_status = 1, update_time=now() WHERE ea = #{ea}")
    int confirm(@Param("ea") String ea);

    @Update("update qywx_corp_agent_config set create_time = now(),is_encrypt = 1 where ea = #{ea} and id = #{id}")
    void updateInfoAfterUpgradeSuccess(@Param("ea") String ea, @Param("id") String id);

    @Update("update qywx_corp_agent_config set create_time = now() where ea = #{ea} and id = #{id}")
    void updateCreateTime(@Param("ea") String ea, @Param("id") String id);

    @Select("<script>"
            + "select * from qywx_corp_agent_config where corpid in\n"
            + "     <foreach open='(' close=')' separator=',' collection='corpids' index='idx'>"
            + "         #{corpids[${idx}]}"
            + "     </foreach>"
            + " order by update_time desc limit 1"
            + "</script>")
    List<QywxCorpAgentConfigEntity> queryAgentByCorpids(@Param("corpids")List<String> corpids);
}
