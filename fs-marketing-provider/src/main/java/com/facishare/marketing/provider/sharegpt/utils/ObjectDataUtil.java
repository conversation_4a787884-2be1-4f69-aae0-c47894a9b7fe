package com.facishare.marketing.provider.sharegpt.utils;

import com.facishare.marketing.api.result.ai.AgentExecuteResponse;
import com.facishare.marketing.api.result.ai.AiChatRecordResult;
import com.facishare.marketing.provider.sharegpt.store.chat.obj.AIChatRecordObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;

import java.util.List;

public class ObjectDataUtil {

    public static String getObjectDataFieldStringValue(ObjectData objectData, String field, String defaultValue) {
        return objectData != null ?
                objectData.get(field) != null ? String.valueOf(objectData.get(field)) : defaultValue
                : defaultValue;
    }

    public static List<String> getObjectDataFieldListValue(ObjectData objectData, String field, List<String> defaultValue) {
        return objectData != null ?
                objectData.get(field) != null ? (List) (objectData.get(field)) : defaultValue
                : defaultValue;
    }

    public static Double getObjectDataFieldDoubleValue(ObjectData objectData, String field, Double defaultValue) {
        return objectData != null ?
                objectData.get(field) != null ? Double.valueOf(String.valueOf(objectData.get(field))) : defaultValue
                : defaultValue;
    }

    public static Integer getObjectDataFieldIntValue(ObjectData objectData, String field, Integer defaultValue) {
        return objectData != null ?
                objectData.get(field) != null ? Integer.valueOf(String.valueOf(objectData.get(field))) : defaultValue
                : defaultValue;
    }

    public static AiChatRecordResult objectDataResultMapping() {
        return new AiChatRecordResult();
    }

    public static AiChatRecordResult objectDataResultMapping(AgentExecuteResponse.AgentExecuteResult agentExecuteResult) {
        AiChatRecordResult.AiChatRecordResultBuilder builder = AiChatRecordResult.builder();
        AgentExecuteResponse.AgentMessage agentMessage = agentExecuteResult.getAgentMessage();
        if (agentMessage != null) {
            builder.tips(agentMessage.getTips())
                    .content(agentMessage.getContent())
                    .previewContent(MarkdownParseUtil.parse(agentMessage.getContent()))
                    .actions(agentMessage.getActions())
                    .displayData(agentMessage.getDisplayData());
        }
        AgentExecuteResponse.ActionResult actionResult = agentExecuteResult.getActionResult();
        if (actionResult != null) {
            builder.actionType(actionResult.getActionType())
                    .originalData(actionResult.getOriginalData());
        }
        return builder
                .process(agentExecuteResult.getProcess())
                .build();
    }

    public static AiChatRecordResult objectDataResultMapping(AIChatRecordObj recordObj) {
        return AiChatRecordResult.builder()
                .id(recordObj.getId())
                .sessionId(recordObj.getSessionId())
                .contentType(recordObj.getContentType())
                .cardType(recordObj.getCardType())
                .tips(recordObj.getTips())
                .content(recordObj.getContent())
                .contentList(recordObj.getProperty() == null ? null : (List) recordObj.getProperty().getContentList())
                .referenceText(recordObj.getProperty() == null ? null : recordObj.getProperty().getReferenceText())
                .action(recordObj.getAction())
                .process(recordObj.getProcess())
                .actions(recordObj.getActions())
                .data(recordObj.getData())
                .previewContent(MarkdownParseUtil.parse(recordObj.getContent()))
                .likeStatus(recordObj.getLikeStatus())
                .actionStatus(recordObj.getActionStatus())
                .createBy(recordObj.getSenderId())
                .createTime(recordObj.getCreateTime())
                .status(2)
                .displayData(recordObj.getDisplayData())
                .originalData(recordObj.getOriginalData())
                .actionType(recordObj.getActionType())
                .build();
    }
}
