package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.outapi.arg.qrcode.GetWxServiceQRCodeArg;
import com.facishare.marketing.outapi.service.QRCodeService;
import com.facishare.marketing.provider.manager.PictureManager;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.union.core.api.model.arg.QueryQrCodeArg;
import com.facishare.wechat.union.core.api.model.result.QrCodeResult;
import com.facishare.wechat.union.core.api.service.OuterServiceWechatService;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("qRCodeService")
@Slf4j
public class QRCodeServiceImpl implements QRCodeService {
    @Autowired
    private OuterServiceWechatService outerServiceWechatService;
    @Autowired
    private PictureManager pictureManager;
    @Autowired
    private MergeJedisCmd jedisCmd;

    @Override
    public Result<String> getWxServiceQRCode(GetWxServiceQRCodeArg arg) {
        String qrKey = generalQRKey(arg.getFsEa(), arg.getAppId(), arg.getKfUserId());
        String aPath = jedisCmd.get(qrKey);
        log.info("qrKey ={} aPath={}", qrKey, aPath);
        if (!Strings.isNullOrEmpty(aPath)) {
            return Result.newSuccess(aPath);
        }

        QueryQrCodeArg queryQrCodeArg = BeanUtil.copy(arg, QueryQrCodeArg.class);
        ModelResult<QrCodeResult> qrCodeResult = outerServiceWechatService.queryQrCodeByKfUserId(queryQrCodeArg);
        if (!qrCodeResult.isSuccess()) {
            Result.newError(qrCodeResult.getErrorCode(), qrCodeResult.getErrorMessage());
        }

        aPath = pictureManager.generalQRImage(arg.getFsEa(), qrCodeResult.getResult().getAppLogoFileId(), qrCodeResult.getResult().getFileId(), qrCodeResult.getResult().getWxAppName(), 3);
        if (!Strings.isNullOrEmpty(aPath)) {
            jedisCmd.set(qrKey, aPath);
            jedisCmd.expire(qrKey, 2 * 24 * 60 * 60);
        }
        return Result.newSuccess(aPath);
    }

    private String generalQRKey(String ea, String appId, Integer userId) {
        StringBuffer sb = new StringBuffer();
        sb.append("QRCode");
        if (!Strings.isNullOrEmpty(appId)) {
            sb.append(".").append(appId);
        }
        if (!Strings.isNullOrEmpty(ea)) {
            sb.append(".").append(ea);
        }
        if (userId != null) {
            sb.append(".").append(userId);
        }
        return sb.toString();
    }
}
