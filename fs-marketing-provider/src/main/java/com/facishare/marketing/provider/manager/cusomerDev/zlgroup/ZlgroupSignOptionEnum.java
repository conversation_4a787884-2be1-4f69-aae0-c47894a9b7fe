package com.facishare.marketing.provider.manager.cusomerDev.zlgroup;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created by zhengh on 2020/5/9.
 */
@Getter
@AllArgsConstructor
public enum ZlgroupSignOptionEnum {

    //foneshare

    SIGN_IN("已签到", "option1"),

    NOT_SIGN_IN("未签到", "u2tmeS72g");


    //ceshi112
/**
    SIGN_IN("已签到", "a4to0xW8d"),

    NOT_SIGN_IN("未签到", "option1");
**/
    private String label;
    private String value;
}
