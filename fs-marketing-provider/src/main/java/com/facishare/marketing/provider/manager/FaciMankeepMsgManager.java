package com.facishare.marketing.provider.manager;

import com.facishare.mankeep.common.util.UUIDUtil;
import com.facishare.qixin.api.model.open.OpenBaseResult;
import com.facishare.qixin.api.model.open.kemai.arg.GetKeMaiSessionIdArg;
import com.facishare.qixin.api.model.open.kemai.result.GetKeMaiSessionIdResult;
import com.facishare.qixin.api.open.OpenServiceForKeMai;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class FaciMankeepMsgManager {

    @Autowired
    private OpenServiceForKeMai openServiceForKeMai;

    public GetKeMaiSessionIdResult getSessionId(String ea, Integer userId, String otherUid) {
        GetKeMaiSessionIdArg arg = new GetKeMaiSessionIdArg();
        arg.setEnterpriseAccount(ea);
        arg.setEmployId(userId);
        arg.setPostId(UUIDUtil.getUUID());
        arg.setSource("system");
        arg.setOtherId(otherUid);
        OpenBaseResult<GetKeMaiSessionIdResult> openBaseResult = openServiceForKeMai.getKeMaiSessionId(arg);
        log.warn("FaciMankeepMsgManager.getSessionId getKeMaiSessionId arg={}, result={}", arg, openBaseResult);
        if (openBaseResult != null && openBaseResult.getCode() != 0) {
            return null;
        }

        return openBaseResult.getResult();
    }
}
