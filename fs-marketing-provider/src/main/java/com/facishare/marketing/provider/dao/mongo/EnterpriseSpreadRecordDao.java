package com.facishare.marketing.provider.dao.mongo;

import com.facishare.marketing.provider.entity.EnterpriseSpreadRecordEntity;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.Calendar;
import java.util.List;


/**
 * Created by zhengh on 2020/9/23.
 */
@Repository
public class EnterpriseSpreadRecordDao {
    @Autowired
    private DatastoreExt datastoreExt;

    /**
     * 插入记录
     * @param
     */
    public void inserRecord(List<EnterpriseSpreadRecordEntity> list){
        datastoreExt.save(list);
    }

    /**
     * 过滤已经发送的地址，返回需要发送的地址
     * @param address
     * @param ea
     * @param type
     * @param days
     * @return
     */
    public List<EnterpriseSpreadRecordEntity> querySendedAddressByDays(List<String> address, String ea, int type, int days){
        if (CollectionUtils.isEmpty(address)){
            return Lists.newArrayList();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - days);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Query<EnterpriseSpreadRecordEntity> query = datastoreExt.createQuery(EnterpriseSpreadRecordEntity.class);
        query.criteria("sendTime").greaterThan(calendar.getTime());
        query.criteria("ea").equal(ea);
        query.criteria("type").equal(type);
        query.criteria("address").in(address);

        return query.asList();
    }

    public List<EnterpriseSpreadRecordEntity> queryAddress(List<String> address, String ea, int type){
        if (CollectionUtils.isEmpty(address)){
            return Lists.newArrayList();
        }
        Query<EnterpriseSpreadRecordEntity> query = datastoreExt.createQuery(EnterpriseSpreadRecordEntity.class);
        query.criteria("ea").equal(ea);
        query.criteria("type").equal(type);
        query.criteria("address").in(address);

        return query.asList();
    }

    public void updateCurrentTimeByIds(List<ObjectId> ids){
        Query<EnterpriseSpreadRecordEntity> query = datastoreExt.createQuery(EnterpriseSpreadRecordEntity.class);
        query.criteria("_id").in(ids);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        UpdateOperations<EnterpriseSpreadRecordEntity> updateOperations = datastoreExt.createUpdateOperations(EnterpriseSpreadRecordEntity.class).set("sendTime", calendar.getTime());
        datastoreExt.update(query, updateOperations);
    }

    public void updateOldUserMarketingIdToNewUserMarketingId(String ea, String oldUserMarketing, String newUserMarketing) {
        Query<EnterpriseSpreadRecordEntity> query = datastoreExt.createQuery(EnterpriseSpreadRecordEntity.class);
        query.criteria("ea").equal(ea);
        query.criteria("address").equal(oldUserMarketing);
        UpdateOperations<EnterpriseSpreadRecordEntity> updateOperations = datastoreExt.createUpdateOperations(EnterpriseSpreadRecordEntity.class).set("address", newUserMarketing);
        datastoreExt.update(query, updateOperations);
    }

    public void updateTimeByDays(List<ObjectId> ids, int days){
        Query<EnterpriseSpreadRecordEntity> query = datastoreExt.createQuery(EnterpriseSpreadRecordEntity.class);
        query.criteria("_id").in(ids);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - days);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        UpdateOperations<EnterpriseSpreadRecordEntity> updateOperations = datastoreExt.createUpdateOperations(EnterpriseSpreadRecordEntity.class).set("sendTime", calendar.getTime());
        datastoreExt.update(query, updateOperations);
    }


    public void deleteOneDoc(String id){
        Query<EnterpriseSpreadRecordEntity> query = datastoreExt.createQuery(EnterpriseSpreadRecordEntity.class);
        query.criteria("_id").equal(new ObjectId(id));
        datastoreExt.delete(query);
    }

    public void deleteByEaAndPhones(String ea, int type, List<String> address) {
        Query<EnterpriseSpreadRecordEntity> query = datastoreExt.createQuery(EnterpriseSpreadRecordEntity.class);
        query.criteria("ea").equal(ea);
        query.criteria("type").equal(type);
        query.criteria("address").in(address);
        datastoreExt.delete(query);
    }
}
