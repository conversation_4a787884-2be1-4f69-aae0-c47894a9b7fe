package com.facishare.marketing.provider.manager.qywx;

import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.marketing.api.arg.NoticeSendArg;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.qywx.ResetDataStatusEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.ChannelData;
import com.facishare.marketing.common.typehandlers.value.ChannelDataList;
import com.facishare.marketing.common.typehandlers.value.IntegerList;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.conference.ConferenceInvitationUserDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceTicketManagerDAO;
import com.facishare.marketing.provider.dao.qywx.BindAppUserInfoDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.dao.qywx.ResetDataStatusDAO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.kis.*;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.innerResult.qywx.DepartmentStaffResult.StaffInfo;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.QywxVirtualFsUserManager;
import com.facishare.marketing.provider.manager.user.UserManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created  By zhoux 2020/05/15
 **/
@Component
@Slf4j
public class QywxBindAppUserManager {

    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;

    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;

    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;

    @Autowired
    private EnterpriseEmployeeAmountStatisticDao enterpriseEmployeeAmountStatisticDao;

    @Autowired
    private WechatAccountManager wechatAccountManager;

    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private BindAppUserInfoDAO bindAppUserInfoDAO;

    @Autowired
    private EnterpriseEmployeeDayStatisticDao enterpriseEmployeeDayStatisticDao;

    @Autowired
    private EnterpriseEmployeeObjectDayStatisticDao enterpriseEmployeeObjectDayStatisticDao;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private EnterpriseEmployeeObjectAmountStatisticDao enterpriseEmployeeObjectAmountStatisticDao;

    @Autowired
    private ResetDataStatusDAO resetDataStatusDAO;

    @Autowired
    private ConferenceInvitationUserDAO conferenceInvitationUserDAO;

    @Autowired
    private ConferenceTicketManagerDAO conferenceTicketManagerDAO;

    @Autowired
    private BoardCardDao boardCardDao;

    @Autowired
    private BoardCardTaskDao boardCardTaskDao;

    @Autowired
    private BoardUserDao boardUserDao;


    public Result bindAppUser(String ea, Integer fsUserId, String updateId) {
        // 1.校验是否绑定企业微信
        QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (qywxMiniappConfigEntity == null) {
            log.warn("QywxBindAppUserManager.bindAppUser qywxMiniappConfigEntity is null ea:{}", ea);
            return Result.newError(SHErrorCode.ENTERPRISE_NOT_BIND_QYWX);
        }
        // 2.查询纷享手机号
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIdsWithoutQywx(ea, Lists.newArrayList(fsUserId), true);
        if (MapUtils.isEmpty(fsEmployeeMsgMap) || fsEmployeeMsgMap.get(fsUserId) == null) {
            log.warn("QywxBindAppUserManager.bindAppUser fsEmployeeMsgMap is null fsEmployeeMsgMap:{}", fsEmployeeMsgMap);
            return Result.newError(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }
        String fsPhone = fsEmployeeMsgMap.get(fsUserId).getMobile();
        // 3.查询是否存在企业微信相同手机号
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            log.warn("QywxBindAppUserManager.bindAppUser agentConfig is null ea:{}", ea);
            return Result.newError(SHErrorCode.QYWX_AGENT_CONFIG_NOT_FOUND);
        }
        String accessToken = qywxManager.getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxBindAppUserManager.bindAppUser accessToken is null ea:{}", ea);
            return Result.newError(SHErrorCode.QYWX_GET_ACCESS_TOKEN_FAIL);
        }
        StaffInfo staffInfo = qywxUserManager.getQywxUserInfoByPhone(ea, fsPhone);
        if (staffInfo == null) {
            log.warn("QywxBindAppUserManager.bindAppUser staffInfo is null");
            return Result.newError(SHErrorCode.QYWX_PHONE_NOT_SAME_FROM_FS);
        }
        String qywxUserId = staffInfo.getUserId();
        QywxVirtualFsUserEntity qywxVirtualFsUserEntity = qywxUserManager.getVirtualUserByQywxInfo(agentConfig.getCorpid(), qywxUserId, ea);
        if (qywxVirtualFsUserEntity == null) {
            log.warn("QywxBindAppUserManager.bindAppUser qywxMiniappConfigEntity qywxUserId:{}, corpid:{}", qywxUserId, agentConfig.getCorpid());
            return Result.newError(SHErrorCode.QYWX_VIRTUAL_USER_NOT_FOUND);
        }
        // 4.判断用户当前是否为虚拟身份
        Integer virtualUserId = qywxVirtualFsUserEntity.getUserId();
        if (!QywxUserConstants.isVirtualUserId(virtualUserId)) {
            log.warn("QywxBindAppUserManager.bindAppUser is not virtual user");
            return Result.newError(SHErrorCode.QYWX_NOT_NEED_RESET_DATA);
        }
        UserEntity userEntity = userManager.queryByCorpIdAndQYUserIdAndAppid(agentConfig.getCorpid(), qywxUserId, qywxMiniappConfigEntity.getAppid());
        if (userEntity == null) {
            log.warn("QywxBindAppUserManager.bindAppUser userEntity is null corpId:{}, qywxUserId:{}, appId:{}", agentConfig.getCorpid(), qywxUserId, qywxMiniappConfigEntity.getAppid());
            return Result.newError(SHErrorCode.QYWX_MINIAPP_USER_NOT_FOUND);
        }
        String uid = userEntity.getUid();
        // 5.刷新数据 虚拟身份 -> 纷享身份
        ThreadPoolUtils.execute(() -> {
            try {
                reSetFsUserId(updateId, ea, virtualUserId, fsUserId, fsPhone, uid);
            } catch (Exception e) {
                log.warn("QywxBindAppUserManager.bindAppUser error e:{}", e);
                // 发生错误时删除记录数据
                resetDataStatusDAO.deleteDataById(updateId);
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Transactional
    public void reSetFsUserId(String updateId, String ea, Integer virtualUserId, Integer fsUserId, String phone, String uid) {

        // baord
        List<BoardCardEntity> boardCardEntities = boardCardDao.listBoardCardByEa(ea);
        if (CollectionUtils.isNotEmpty(boardCardEntities)) {
            IntegerList principals = new IntegerList();
            principals.add(fsUserId);
            for (BoardCardEntity entity : boardCardEntities) {
                if (entity != null && CollectionUtils.isNotEmpty(entity.getPrincipals()) && virtualUserId.equals(entity.getPrincipals().get(0))) {
                    boardCardDao.updateBoardCardPrincipals(ea, principals, entity.getId());
                }
            }
        }

        // board_user
        boardUserDao.updateBoardUserEmployeeId(ea, fsUserId, virtualUserId);

        // board_card_task
        boardCardTaskDao.updateBoardCardTaskExecutor(ea, fsUserId, virtualUserId);

        // activity_enroll_data
        List<String> activityEnrollId = bindAppUserInfoDAO.queryActivityEnrollIdByEaAndUserId(ea, virtualUserId);
        if (CollectionUtils.isNotEmpty(activityEnrollId)) {
            bindAppUserInfoDAO.updateActivityEnrollData(fsUserId, activityEnrollId);
        }

        // article
        bindAppUserInfoDAO.updateArticleFsUserId(ea, fsUserId, virtualUserId);

        // conference_review_employee
        bindAppUserInfoDAO.updateConferenceReviewEmployeeFsUser(ea, fsUserId, virtualUserId);

        // conference_invitation_user
        conferenceInvitationUserDAO.updateConferenceInvitationUserId(ea, virtualUserId, fsUserId);

        // conference_ticket_manager
        conferenceTicketManagerDAO.updateConferenceTicketMangerUserId(ea, virtualUserId, fsUserId);

        // customize_form_data_user
        List<String> formUserIds = bindAppUserInfoDAO.queryCustomizeFormUserByEaAndUserId(ea, virtualUserId);
        if (CollectionUtils.isNotEmpty(formUserIds)) {
            bindAppUserInfoDAO.updateCustomizeFormDataUserData(fsUserId, formUserIds);
        }

        try {
            // enterprise_employee_amount_statistic
            handleEnterpriseEmployeeAmountStatistic(ea, virtualUserId, fsUserId);

            // enterprise_employee_day_statistic
            handleEnterpriseEmployeeDayStatistic(ea, virtualUserId, fsUserId);

            // enterprise_employee_object_amount_statistic
            handleEnterpriseEmployeeObjectAmountStatistic(ea, virtualUserId, fsUserId);

            // enterprise_employee_object_day_statistic
            handleEnterpriseEmployeeObjectDayStatistic(ea, virtualUserId, fsUserId);

            // enterprise_employee_object_uv_statistic
            handleEnterpriseEmployeeObjectUvStatistic(ea, virtualUserId, fsUserId);

            // marketing_activity_employee_day_statistic
            handleMarketingActivityEmployeeDayStatistic(ea, virtualUserId, fsUserId);

            // marketing_activity_employee_statistic
            handleMarketingActivityEmployeeStatistic(ea, virtualUserId, fsUserId);

            // marketing_activity_external_config
            handleMarketingActivityExternalConfig(ea, virtualUserId, fsUserId);

            // marketing_activity_spread_record
            handleMarketingActivitySpreadRecord(ea, virtualUserId, fsUserId);

            // marketing_event_employee_amount_statistic
            handleMarketingEventEmployeeAmountStatistic(ea, virtualUserId, fsUserId);

            // marketing_event_employee_statistic
            handleMarketingEventEmployeeStatistic(ea, virtualUserId, fsUserId);

            // marketing_object_employee_statistic
            handleMarketingObjectEmployeeStatistic(ea, virtualUserId, fsUserId);

            // moment_poster_user
            bindAppUserInfoDAO.updateMomentPosterUser(ea, fsUserId, virtualUserId);

            // notice
            handleNotice(ea, virtualUserId, fsUserId);

            // spread_stat 1-10
            handleSpreadStat(ea, virtualUserId, fsUserId);

            // spread_task
            handleSpreadTask(ea, virtualUserId, fsUserId);

            // qr_poster_user
            bindAppUserInfoDAO.updateQrPosterUser(ea, fsUserId, virtualUserId);
        }catch (Exception e){
            log.info("reSetFsUserId error ea:{} userId:{} virtualUserId:{} e:", ea, fsUserId, virtualUserId, e);
        }

        //防止报错
        List<FSBindEntity> entities = bindAppUserInfoDAO.queryByEaAndFsUserId(ea, fsUserId);
        if(CollectionUtil.isEmpty(entities)){
            // fs_bind
            bindAppUserInfoDAO.updateFsBind(fsUserId, ea, virtualUserId);
        }

        // account
        if (StringUtils.isNotBlank(uid)) {
            bindAppUserInfoDAO.updateAccountPhone(phone, uid);
        }
        // 最后刷新虚拟身份表
        bindAppUserInfoDAO.updateQywxVirtualFsUser(ea, virtualUserId, fsUserId);

        // 更新记录表
        resetDataStatusDAO.updateStatus(updateId, ResetDataStatusEnum.UPDATE_COMPLETED.getStatus());

    }

    @Transactional
    public void handleEnterpriseEmployeeAmountStatistic(String ea, Integer virtualUserId, Integer fsUserId) {
        // 查出纷享身份数据
        EnterpriseEmployeeAmountStatisticEntity fsEmployeeAmountData = enterpriseEmployeeAmountStatisticDao.getUnique(ea, fsUserId);
        // 查询虚拟身份数据
        EnterpriseEmployeeAmountStatisticEntity virtualEmployeeAmountData = enterpriseEmployeeAmountStatisticDao.getUnique(ea, virtualUserId);
        if (virtualEmployeeAmountData != null) {
            if (fsEmployeeAmountData == null) {
                // 更新数据
                bindAppUserInfoDAO.updateEnterpriseEmployeeAmountStatisticUser(ea, fsUserId, virtualUserId);
            } else {
                // 合并数据
                fsEmployeeAmountData.mergeStatisticData(virtualEmployeeAmountData);
                Map<String, ChannelData> fsUserChannelMap = handleChannelData(fsEmployeeAmountData.getAllChannelData(), virtualEmployeeAmountData.getAllChannelData());

                for (ChannelData channelData : Lists.newArrayList(fsUserChannelMap.values())) {
                    fsEmployeeAmountData.addAndUpdateChannelData(channelData);
                }
                // 保存新数据
                enterpriseEmployeeAmountStatisticDao.updateStatistic(fsEmployeeAmountData);
                // 删除旧虚拟身份数据
                bindAppUserInfoDAO.deleteEnterpriseEmployeeAmountStatisticUser(virtualEmployeeAmountData.getId());
            }
        }
    }

    @Transactional
    public void handleEnterpriseEmployeeDayStatistic(String ea, Integer virtualUserId, Integer fsUserId) {
        // 查出纷享身份数据
        List<EnterpriseEmployeeDayStatisticEntity> fsEmployeeData = bindAppUserInfoDAO.getEnterpriseEmployeeDayStatisticByEaUserId(ea, fsUserId);
        // 查询虚拟身份数据
        List<EnterpriseEmployeeDayStatisticEntity> virtualEmployeeData = bindAppUserInfoDAO.getEnterpriseEmployeeDayStatisticByEaUserId(ea, virtualUserId);
        if (CollectionUtils.isNotEmpty(virtualEmployeeData)) {
            // 需要删除数据
            List<String> needDeleteIds = Lists.newArrayList();
            if (CollectionUtils.isEmpty(fsEmployeeData)) {
                // 将虚拟身份刷新为纷享身份
                bindAppUserInfoDAO.updateEnterpriseEmployeeDayStatisticUser(ea, fsUserId, virtualUserId);
            } else {
                Map<String, EnterpriseEmployeeDayStatisticEntity> virtualEmployeeMap = virtualEmployeeData.stream().collect(Collectors.toMap(data -> data.getDate().toString(), data -> data));
                // 合并同一天数据
                for (EnterpriseEmployeeDayStatisticEntity fsData : fsEmployeeData) {
                    EnterpriseEmployeeDayStatisticEntity virtualData = virtualEmployeeMap.get(fsData.getDate().toString());
                    if (virtualData != null) {
                        // 更新数据
                        fsData.mergeStatisticData(virtualData);
                        Map<String, ChannelData> fsUserChannelMap = handleChannelData(fsData.getAllChannelData(), virtualData.getAllChannelData());
                        for (ChannelData channelData : Lists.newArrayList(fsUserChannelMap.values())) {
                            fsData.addAndUpdateChannelData(channelData);
                        }
                        // 更新合并数据
                        enterpriseEmployeeDayStatisticDao.updateStatistic(fsData);
                        virtualEmployeeMap.remove(fsData.getDate().toString());
                        needDeleteIds.add(virtualData.getId());
                    }
                }
                // 需要新建的数据
                if (MapUtils.isNotEmpty(virtualEmployeeMap)) {
                    for (Map.Entry<String, EnterpriseEmployeeDayStatisticEntity> entry : virtualEmployeeMap.entrySet()) {
                        EnterpriseEmployeeDayStatisticEntity virtualData = entry.getValue();
                        bindAppUserInfoDAO.updateEnterpriseEmployeeDayStatisticUserById(fsUserId, virtualData.getId());
                    }
                }
                // 删除虚拟身份数据
                if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                    bindAppUserInfoDAO.deleteEnterpriseEmployeeDayStatisticData(needDeleteIds);
                }
            }
        }
    }

    @Transactional
    public void handleEnterpriseEmployeeObjectAmountStatistic(String ea, Integer virtualUserId, Integer fsUserId) {
        // 查询纷享身份数据
        List<EnterpriseEmployeeObjectAmountStatisticEntity> fsEmployeeData = bindAppUserInfoDAO.getEnterpriseEmployeeObjectAmountStatisticByEaUserId(ea, fsUserId);
        // 查询虚拟身份
        List<EnterpriseEmployeeObjectAmountStatisticEntity> virtualEmployeeData = bindAppUserInfoDAO.getEnterpriseEmployeeObjectAmountStatisticByEaUserId(ea, virtualUserId);
        if (CollectionUtils.isNotEmpty(virtualEmployeeData)) {
            // 需要删除数据
            List<String> needDeleteIds = Lists.newArrayList();
            if (CollectionUtils.isEmpty(fsEmployeeData)) {
                bindAppUserInfoDAO.updateEnterpriseEmployeeObjectAmountStatisticUser(ea, fsUserId, virtualUserId);
            } else {
                // 合并同一物料数据
                Map<String, EnterpriseEmployeeObjectAmountStatisticEntity> virtualEmployeeMap = virtualEmployeeData.stream().collect(Collectors.toMap(
                    EnterpriseEmployeeObjectAmountStatisticEntity::getObjectId, data -> data, (v1, v2) -> v1));
                for (EnterpriseEmployeeObjectAmountStatisticEntity fsData : fsEmployeeData) {
                    EnterpriseEmployeeObjectAmountStatisticEntity virtualData = virtualEmployeeMap.get(fsData.getObjectId());
                    if (virtualData != null) {
                        fsData.mergeData(virtualData);
                        Map<String, ChannelData> fsUserChannelMap = handleChannelData(fsData.getAllChannelData(), virtualData.getAllChannelData());
                        for (ChannelData channelData : Lists.newArrayList(fsUserChannelMap.values())) {
                            fsData.addAndUpdateChannelData(channelData);
                        }
                        // 合并数据
                        enterpriseEmployeeObjectAmountStatisticDao.updateStatistic(fsData);
                        virtualEmployeeMap.remove(fsData.getObjectId());
                        needDeleteIds.add(virtualData.getId());
                    }
                }
                if (MapUtils.isNotEmpty(virtualEmployeeMap)) {
                    for (Map.Entry<String, EnterpriseEmployeeObjectAmountStatisticEntity> entry : virtualEmployeeMap.entrySet()) {
                        EnterpriseEmployeeObjectAmountStatisticEntity virtualData = entry.getValue();
                        bindAppUserInfoDAO.updateEnterpriseEmployeeObjectAmountStatisticUserById(fsUserId, virtualData.getId());
                    }
                }
                // 删除虚拟身份数据
                if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                    bindAppUserInfoDAO.deleteEnterpriseEmployeeObjectAmountData(needDeleteIds);
                }
            }
        }
    }

    @Transactional
    public void handleEnterpriseEmployeeObjectDayStatistic(String ea, Integer virtualUserId, Integer fsUserId) {
        // 查询纷享身份数据
        List<EnterpriseEmployeeObjectDayStatisticEntity> fsEmployeeData = bindAppUserInfoDAO.getEnterpriseEmployeeObjectDayStatisticByEaUserId(ea, fsUserId);
        // 查询虚拟身份数据
        List<EnterpriseEmployeeObjectDayStatisticEntity> virtualEmployeeData = bindAppUserInfoDAO.getEnterpriseEmployeeObjectDayStatisticByEaUserId(ea, virtualUserId);
        if (CollectionUtils.isNotEmpty(virtualEmployeeData)) {
            // 需要删除数据
            List<String> needDeleteIds = Lists.newArrayList();
            if (CollectionUtils.isEmpty(fsEmployeeData)) {
                bindAppUserInfoDAO.updateEnterpriseEmployeeObjectDayStatisticUser(ea, fsUserId, virtualUserId);
            } else {
                Map<String, EnterpriseEmployeeObjectDayStatisticEntity> virtualEmployeeMap = virtualEmployeeData.stream()
                    .collect(Collectors.toMap(data -> data.getObjectId() + "#" + data.getDate().toString(), data -> data, (v1, v2) -> v1));
                // 合并同一天且物料相同数据
                for (EnterpriseEmployeeObjectDayStatisticEntity fsData : fsEmployeeData) {
                    EnterpriseEmployeeObjectDayStatisticEntity virtualData = virtualEmployeeMap.get(fsData.getObjectId() + "#" + fsData.getDate().toString());
                    if (virtualData != null) {
                        fsData.mergeData(virtualData);
                        Map<String, ChannelData> fsUserChannelMap = handleChannelData(fsData.getAllChannelData(), virtualData.getAllChannelData());
                        for (ChannelData channelData : Lists.newArrayList(fsUserChannelMap.values())) {
                            fsData.addAndUpdateChannelData(channelData);
                        }
                        // 合并数据
                        enterpriseEmployeeObjectDayStatisticDao.updateStatistic(fsData);
                        virtualEmployeeMap.remove(fsData.getObjectId() + "#" + fsData.getDate().toString());
                        needDeleteIds.add(virtualData.getId());
                    }
                }
                if (MapUtils.isNotEmpty(virtualEmployeeMap)) {
                    for (Map.Entry<String, EnterpriseEmployeeObjectDayStatisticEntity> entry : virtualEmployeeMap.entrySet()) {
                        EnterpriseEmployeeObjectDayStatisticEntity virtualData = entry.getValue();
                        bindAppUserInfoDAO.updateEnterpriseEmployeeObjectDayStatisticUserById(fsUserId, virtualData.getId());
                    }
                }
                // 删除虚拟身份数据
                if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                    bindAppUserInfoDAO.deleteEnterpriseEmployeeObjectDayStatisticData(needDeleteIds);
                }
            }
        }
    }

    @Transactional
    public void handleEnterpriseEmployeeObjectUvStatistic(String ea, Integer virtualUserId, Integer fsUserId) {
        // 纷享身份数据
        List<EnterpriseEmployeeObjectUVStatisticEntity> fsEmployeeData = bindAppUserInfoDAO.getEnterpriseEmployeeObjectUVStatisticByEaUserId(ea, fsUserId);
        // 虚拟身份数据
        List<EnterpriseEmployeeObjectUVStatisticEntity> virtualEmployeeData = bindAppUserInfoDAO.getEnterpriseEmployeeObjectUVStatisticByEaUserId(ea, virtualUserId);
        if (CollectionUtils.isNotEmpty(virtualEmployeeData)) {
            // 需要删除数据
            List<Integer> needDeleteIds = Lists.newArrayList();
            if (CollectionUtils.isEmpty(fsEmployeeData)) {
                bindAppUserInfoDAO.updateEnterpriseEmployeeObjectUVStatisticUser(ea, fsUserId, virtualUserId);
            } else {
                Map<String, EnterpriseEmployeeObjectUVStatisticEntity> virtualEmployeeMap = virtualEmployeeData.stream().collect(Collectors.toMap(
                    EnterpriseEmployeeObjectUVStatisticEntity::getObjectId, data -> data, (v1, v2) -> v1));
                // 合并相同数据
                for (EnterpriseEmployeeObjectUVStatisticEntity fsData : fsEmployeeData) {
                    EnterpriseEmployeeObjectUVStatisticEntity virtualData = virtualEmployeeMap.get(fsData.getObjectId());
                    if (virtualData != null) {
                        int userUv = fsData.getUv() == null ? 0 : fsData.getUv().intValue();
                        int virtualUv = virtualData.getUv() == null ? 0 :virtualData.getUv().intValue();
                        bindAppUserInfoDAO.updateEnterpriseEmployeeObjectUvStatisticUV(fsData.getId(), userUv + virtualUv);
                        virtualEmployeeMap.remove(fsData.getObjectId());
                        needDeleteIds.add(virtualData.getId());
                    }
                }
                if (MapUtils.isNotEmpty(virtualEmployeeMap)) {
                    for (Map.Entry<String, EnterpriseEmployeeObjectUVStatisticEntity> entry : virtualEmployeeMap.entrySet()) {
                        EnterpriseEmployeeObjectUVStatisticEntity virtualData = entry.getValue();
                        bindAppUserInfoDAO.updateEnterpriseEmployeeObjectUvStatisticUserById(fsUserId, virtualData.getId());
                    }
                }
                // 删除虚拟身份数据
                if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                    bindAppUserInfoDAO.deleteEnterpriseEmployeeObjectUvStatisticData(needDeleteIds);
                }
            }
        }
    }

    @Transactional
    public void handleMarketingActivityEmployeeDayStatistic(String ea, Integer virtualUserId, Integer fsUserId) {
        // 查询纷享身份
        List<MarketingActivityEmployeeDayStatisticEntity> fsEmployeeData = bindAppUserInfoDAO.getMarketingActivityEmployeeDayStatisticByEaUserId(ea, fsUserId);
        // 查询虚拟身份
        List<MarketingActivityEmployeeDayStatisticEntity> virtualEmployeeData = bindAppUserInfoDAO.getMarketingActivityEmployeeDayStatisticByEaUserId(ea, virtualUserId);
        if (CollectionUtils.isNotEmpty(virtualEmployeeData)) {
            // 需要删除数据
            List<String> needDeleteIds = Lists.newArrayList();
            if (CollectionUtils.isEmpty(fsEmployeeData)) {
                bindAppUserInfoDAO.updateMarketingActivityEmployeeDayStatisticUser(ea, fsUserId, virtualUserId);
            } else {
                Map<String, MarketingActivityEmployeeDayStatisticEntity> virtualEmployeeMap = virtualEmployeeData.stream()
                    .collect(Collectors.toMap(data -> data.getMarketingActivityId() + "#" + data.getDate().toString(), data -> data, (v1, v2) -> v1));
                // 合并同一天相同营销活动
                for (MarketingActivityEmployeeDayStatisticEntity fsData : fsEmployeeData) {
                    MarketingActivityEmployeeDayStatisticEntity virtualData = virtualEmployeeMap.get(fsData.getMarketingActivityId() + "#" + fsData.getDate().toString());
                    if (virtualData != null) {
                        fsData.mergeData(virtualData);
                        bindAppUserInfoDAO.incMarketingActivityEmployeeDayByAddEntity(fsData);
                        virtualEmployeeMap.remove(fsData.getMarketingActivityId() + "#" + fsData.getDate().toString());
                        needDeleteIds.add(virtualData.getId());
                    }
                }
                if (MapUtils.isNotEmpty(virtualEmployeeMap)) {
                    for (Map.Entry<String, MarketingActivityEmployeeDayStatisticEntity> entry : virtualEmployeeMap.entrySet()) {
                        MarketingActivityEmployeeDayStatisticEntity virtualData = entry.getValue();
                        bindAppUserInfoDAO.updateMarketingActivityEmployeeDayStatisticUserById(fsUserId, virtualData.getId());
                    }
                }
                // 需要删除数据
                if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                    bindAppUserInfoDAO.deleteMarketingActivityEmployeeDayStatisticData(needDeleteIds);
                }
            }
        }

    }

    @Transactional
    public void handleMarketingActivityEmployeeStatistic(String ea, Integer virtualUserId, Integer fsUserId) {
        // 查询纷享身份
        List<MarketingActivityEmployeeStatisticEntity> fsEmployeeData = bindAppUserInfoDAO.getMarketingActivityEmployeeStatisticByEaUserId(ea, fsUserId);
        // 查询虚拟身份
        List<MarketingActivityEmployeeStatisticEntity> virtualEmployeeData = bindAppUserInfoDAO.getMarketingActivityEmployeeStatisticByEaUserId(ea, virtualUserId);
        if (CollectionUtils.isNotEmpty(virtualEmployeeData)) {
            // 需要删除数据
            List<String> needDeleteIds = Lists.newArrayList();
            if (CollectionUtils.isEmpty(fsEmployeeData)) {
                bindAppUserInfoDAO.updateMarketingActivityEmployeeStatisticUser(ea, fsUserId, virtualUserId);
            } else {
                Map<String, MarketingActivityEmployeeStatisticEntity> virtualEmployeeMap = virtualEmployeeData.stream()
                    .collect(Collectors.toMap(MarketingActivityEmployeeStatisticEntity::getMarketingActivityId, data -> data, (v1, v2) -> v1));
                for (MarketingActivityEmployeeStatisticEntity fsData : fsEmployeeData) {
                    MarketingActivityEmployeeStatisticEntity virtualData = virtualEmployeeMap.get(fsData.getMarketingActivityId());
                    if (virtualData != null) {
                        fsData.mergeData(virtualData);
                        bindAppUserInfoDAO.incMarketingActivityEmployeeByAddEntity(fsData);
                        virtualEmployeeMap.remove(fsData.getMarketingActivityId());
                        needDeleteIds.add(virtualData.getId());
                    }
                }
                if (MapUtils.isNotEmpty(virtualEmployeeMap)) {
                    for (Map.Entry<String, MarketingActivityEmployeeStatisticEntity> entry : virtualEmployeeMap.entrySet()) {
                        MarketingActivityEmployeeStatisticEntity virtualData = entry.getValue();
                        bindAppUserInfoDAO.updateMarketingActivityEmployeeStatisticById(fsUserId, virtualData.getId());
                    }
                }
                // 需要删除数据
                if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                    bindAppUserInfoDAO.deleteMarketingActivityEmployeeStatisticData(needDeleteIds);
                }
            }
        }
    }
    
    @Transactional
    public void handleMarketingEventEmployeeAmountStatistic(String ea, Integer virtualUserId, Integer fsUserId) {
        // 查询纷享身份
        List<MarketingEventEmployeeAmountStatisticEntity> fsEmployeeData = bindAppUserInfoDAO.getMarketingEventEmployeeAmountStatisticByEaUserId(ea, fsUserId);
        // 查询虚拟身份
        List<MarketingEventEmployeeAmountStatisticEntity> virtualEmployeeData = bindAppUserInfoDAO.getMarketingEventEmployeeAmountStatisticByEaUserId(ea, virtualUserId);
        if (CollectionUtils.isNotEmpty(virtualEmployeeData)) {
            // 需要删除数据
            List<String> needDeleteIds = Lists.newArrayList();
            if (CollectionUtils.isEmpty(fsEmployeeData)) {
                bindAppUserInfoDAO.updateMarketingEventEmployeeAmountStatisticUser(ea, fsUserId, virtualUserId);
            } else {
                Map<String, MarketingEventEmployeeAmountStatisticEntity> virtualEmployeeMap = virtualEmployeeData.stream()
                    .collect(Collectors.toMap(MarketingEventEmployeeAmountStatisticEntity::getMarketingEventId, data -> data, (v1, v2) -> v1));
                for (MarketingEventEmployeeAmountStatisticEntity fsData : fsEmployeeData) {
                    MarketingEventEmployeeAmountStatisticEntity virtualData = virtualEmployeeMap.get(fsData.getMarketingEventId());
                    if (virtualData != null) {
                        fsData.mergeData(virtualData);
                        bindAppUserInfoDAO.incMarketingEventEmployeeAmountByAddEntity(fsData);
                        virtualEmployeeMap.remove(fsData.getMarketingEventId());
                        needDeleteIds.add(virtualData.getId());
                    }
                }
                if (MapUtils.isNotEmpty(virtualEmployeeMap)) {
                    for (Map.Entry<String, MarketingEventEmployeeAmountStatisticEntity> entry : virtualEmployeeMap.entrySet()) {
                        MarketingEventEmployeeAmountStatisticEntity virtualData = entry.getValue();
                        bindAppUserInfoDAO.updateMarketingEventEmployeeAmountStatisticById(fsUserId, virtualData.getId());
                    }
                }
                // 需要删除数据
                if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                    bindAppUserInfoDAO.deleteMarketingEventEmployeeAmountStatisticData(needDeleteIds);
                }
            }
        }
    }

    @Transactional
    public void handleMarketingActivityExternalConfig(String ea, Integer virtualUserId, Integer fsUserId) {
        // 查询全员推广数据
        List<MarketingActivityExternalConfigEntity> marketingActivityExternalConfigEntityList = bindAppUserInfoDAO.getMarketingActivityExternalConfigByEa(ea);
        if (CollectionUtils.isNotEmpty(marketingActivityExternalConfigEntityList)) {
            for (MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity : marketingActivityExternalConfigEntityList) {
                if (marketingActivityExternalConfigEntity.getExternalConfig() != null
                    && marketingActivityExternalConfigEntity.getExternalConfig().getMarketingActivityNoticeSendVO() != null
                    && marketingActivityExternalConfigEntity.getExternalConfig().getMarketingActivityNoticeSendVO().getNoticeVisibilityArg() != null
                    && CollectionUtils.isNotEmpty(marketingActivityExternalConfigEntity.getExternalConfig().getMarketingActivityNoticeSendVO().getNoticeVisibilityArg().getUserIds())) {
                    List<Integer> userIds = marketingActivityExternalConfigEntity.getExternalConfig().getMarketingActivityNoticeSendVO().getNoticeVisibilityArg().getUserIds();
                    // 若包含虚拟身份
                    if (userIds.contains(virtualUserId)) {
                        userIds = userIds.stream().filter(data -> !data.equals(virtualUserId)).collect(Collectors.toList());
                        userIds.add(fsUserId);
                        marketingActivityExternalConfigEntity.getExternalConfig().getMarketingActivityNoticeSendVO().getNoticeVisibilityArg().setUserIds(userIds);
                        // 更新数据
                        bindAppUserInfoDAO.updateMarketingActivityExternalConfigData(marketingActivityExternalConfigEntity);
                    }
                }
            }
        }
    }

    @Transactional
    public void handleMarketingActivitySpreadRecord(String ea, Integer virtualUserId, Integer fsUserId) {
        // 查询纷享身份数据
        List<MarketingActivitySpreadRecordEntity> fsEmployeeData = bindAppUserInfoDAO.getMarketingActivitySpreadRecordByEaAndUserId(ea, fsUserId);
        // 查询虚拟身份数据
        List<MarketingActivitySpreadRecordEntity> virtualEmployeeData = bindAppUserInfoDAO.getMarketingActivitySpreadRecordByEaAndUserId(ea, virtualUserId);
        if (CollectionUtils.isNotEmpty(virtualEmployeeData)) {
            // 需要删除数据
            List<String> needDeleteIds = Lists.newArrayList();
            if (CollectionUtils.isEmpty(fsEmployeeData)) {
                bindAppUserInfoDAO.updateMarketingActivitySpreadRecordUser(ea, fsUserId, virtualUserId);
            } else {
                Map<String, MarketingActivitySpreadRecordEntity> virtualEmployeeMap = virtualEmployeeData.stream()
                    .collect(Collectors.toMap(MarketingActivitySpreadRecordEntity::getMarketingActivityId, data -> data, (v1, v2) -> v1));
                for (MarketingActivitySpreadRecordEntity fsData : fsEmployeeData) {
                    MarketingActivitySpreadRecordEntity virtualData = virtualEmployeeMap.get(fsData.getMarketingActivityId());
                    if (virtualData != null) {
                        // 比较时间，若虚拟身份时间较晚则更新纷享身份时间
                        if (virtualData.getUpdateTime().getTime() > fsData.getUpdateTime().getTime()) {
                            bindAppUserInfoDAO.updateMarketingActivitySpreadRecordUpdateTime(fsData.getId(), virtualData.getCreateTime(), virtualData.getUpdateTime());
                            virtualEmployeeMap.remove(fsData.getMarketingActivityId());
                            needDeleteIds.add(virtualData.getId());
                        }
                    }
                }
                if (MapUtils.isNotEmpty(virtualEmployeeMap)) {
                    for (Map.Entry<String, MarketingActivitySpreadRecordEntity> entry : virtualEmployeeMap.entrySet()) {
                        MarketingActivitySpreadRecordEntity virtualData = entry.getValue();
                        bindAppUserInfoDAO.updateMarketingActivitySpreadRecordById(fsUserId, virtualData.getId());
                    }
                }
                // 需要删除的数据
                if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                    bindAppUserInfoDAO.deleteMarketingActivitySpreadRecordData(needDeleteIds);
                }
            }
        }

    }

    @Transactional
    public void handleMarketingEventEmployeeStatistic(String ea, Integer virtualUserId, Integer fsUserId) {
        // 查询纷享身份数据
        List<MarketingEventEmployeeStatisticEntity> fsEmployeeData = bindAppUserInfoDAO.getMarketingEventEmployeeStatisticByEaAndUserId(ea, fsUserId);
        // 查询虚拟身份数据
        List<MarketingEventEmployeeStatisticEntity> virtualEmployeeData = bindAppUserInfoDAO.getMarketingEventEmployeeStatisticByEaAndUserId(ea, virtualUserId);
        if (CollectionUtils.isNotEmpty(virtualEmployeeData)) {
            // 需要删除数据
            List<String> needDeleteIds = Lists.newArrayList();
            if (CollectionUtils.isEmpty(fsEmployeeData)) {
                bindAppUserInfoDAO.updateMarketingEventEmployeeStatisticUser(ea, fsUserId, virtualUserId);
            } else {
                Map<String, MarketingEventEmployeeStatisticEntity> virtualEmployeeMap = virtualEmployeeData.stream()
                    .collect(Collectors.toMap(data -> data.getMarketingEventId() + "#" + data.getDate().toString(), data -> data, (v1, v2) -> v1));
                // 合并同一天市场活动数据
                for (MarketingEventEmployeeStatisticEntity fsData : fsEmployeeData) {
                    MarketingEventEmployeeStatisticEntity virtualData = virtualEmployeeMap.get(fsData.getMarketingEventId() + "#" + fsData.getDate().toString());
                    if (virtualData != null) {
                        fsData.mergeData(virtualData);
                        bindAppUserInfoDAO.incrementMarketingEventEmployeeStatistic(fsData);
                        virtualEmployeeMap.remove(fsData.getMarketingEventId() + "#" + fsData.getDate().toString());
                        needDeleteIds.add(virtualData.getId());
                    }
                }
                if (MapUtils.isNotEmpty(virtualEmployeeMap)) {
                    for (Map.Entry<String, MarketingEventEmployeeStatisticEntity> entry : virtualEmployeeMap.entrySet()) {
                        MarketingEventEmployeeStatisticEntity virtualData = entry.getValue();
                        bindAppUserInfoDAO.updateMarketingEventEmployeeStatisticUserById(fsUserId, virtualData.getId());
                    }
                }
                // 需要删除的数据
                if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                    bindAppUserInfoDAO.deleteMarketingEventEmployeeStatisticData(needDeleteIds);
                }
            }
        }
    }

    @Transactional
    public void handleMarketingObjectEmployeeStatistic(String ea, Integer virtualUserId, Integer fsUserId) {
        // 查询纷享身份
        List<MarketingObjectEmployeeStatisticEntity> fsEmployeeData = bindAppUserInfoDAO.getMarketingObjectEmployeeStatisticByEaAndUserId(ea, fsUserId);
        // 查询虚拟身份
        List<MarketingObjectEmployeeStatisticEntity> virtualEmployeeData = bindAppUserInfoDAO.getMarketingObjectEmployeeStatisticByEaAndUserId(ea, virtualUserId);
        if (CollectionUtils.isNotEmpty(virtualEmployeeData)) {
            // 需要删除数据
            List<String> needDeleteIds = Lists.newArrayList();
            if (CollectionUtils.isEmpty(fsEmployeeData)) {
                bindAppUserInfoDAO.updateMarketingObjectEmployeeStatisticUser(ea, fsUserId, virtualUserId);
            } else {
                Map<String, MarketingObjectEmployeeStatisticEntity> virtualEmployeeMap = virtualEmployeeData.stream()
                    .collect(Collectors.toMap(data -> data.getObjectId() + "#" + data.getDate().toString(), data -> data, (v1, v2) -> v1));
                for (MarketingObjectEmployeeStatisticEntity fsData : fsEmployeeData) {
                    MarketingObjectEmployeeStatisticEntity virtualData = virtualEmployeeMap.get(fsData.getObjectId() + "#" + fsData.getDate().toString());
                    if (virtualData != null) {
                        fsData.mergeData(virtualData);
                        bindAppUserInfoDAO.incrementeMarketingObjectEmployeeStatisc(fsData);
                        virtualEmployeeMap.remove(fsData.getObjectId() + "#" + fsData.getDate().toString());
                        needDeleteIds.add(virtualData.getId());
                    }
                }
                if (MapUtils.isNotEmpty(virtualEmployeeMap)) {
                    for (Map.Entry<String, MarketingObjectEmployeeStatisticEntity> entry : virtualEmployeeMap.entrySet()) {
                        MarketingObjectEmployeeStatisticEntity virtualData = entry.getValue();
                        bindAppUserInfoDAO.updateMarketingObjectEmployeeUserById(fsUserId, virtualData.getId());
                    }
                }
                // 需要删除的数据
                if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                    bindAppUserInfoDAO.deleteMarketingObjectEmployeeData(needDeleteIds);
                }
            }
        }
    }

    @Transactional
    public void handleNotice(String ea, Integer virtualUserId, Integer fsUserId) {
        // 查询企业推广数据
        List<NoticeEntity> noticeList = bindAppUserInfoDAO.getNoticeByEa(ea);
        if (CollectionUtils.isNotEmpty(noticeList)) {
            for (NoticeEntity noticeEntity : noticeList) {
                NoticeSendArg.NoticeVisibilityVO vo = GsonUtil.getGson().fromJson(noticeEntity.getSendScope(), NoticeSendArg.NoticeVisibilityVO.class);
                if (vo != null && CollectionUtils.isNotEmpty(vo.getUserIds())) {
                    List<Integer> userIds = vo.getUserIds();
                    if (userIds.contains(virtualUserId)) {
                        userIds = userIds.stream().filter(data -> !data.equals(virtualUserId)).collect(Collectors.toList());
                        userIds.add(fsUserId);
                        vo.setUserIds(userIds);
                        // 更新发送范围
                        bindAppUserInfoDAO.updateNoticeSendScope(GsonUtil.getGson().toJson(vo), noticeEntity.getId());
                    }
                }
            }
        }
    }

    @Transactional
    public void handleSpreadStat(String ea, Integer virtualUserId, Integer fsUserId) {
        bindAppUserInfoDAO.updateSpreadStat1(ea, fsUserId, virtualUserId);
        bindAppUserInfoDAO.updateSpreadStat2(ea, fsUserId, virtualUserId);
        bindAppUserInfoDAO.updateSpreadStat3(ea, fsUserId, virtualUserId);
        bindAppUserInfoDAO.updateSpreadStat4(ea, fsUserId, virtualUserId);
        bindAppUserInfoDAO.updateSpreadStat5(ea, fsUserId, virtualUserId);
        bindAppUserInfoDAO.updateSpreadStat6(ea, fsUserId, virtualUserId);
        bindAppUserInfoDAO.updateSpreadStat7(ea, fsUserId, virtualUserId);
        bindAppUserInfoDAO.updateSpreadStat8(ea, fsUserId, virtualUserId);
        bindAppUserInfoDAO.updateSpreadStat9(ea, fsUserId, virtualUserId);
        bindAppUserInfoDAO.updateSpreadStat10(ea, fsUserId, virtualUserId);
    }

    @Transactional
    public void handleSpreadTask(String ea, Integer virtualUserId, Integer fsUserId) {
        // 查询纷享身份数据
        List<SpreadTaskEntity> fsEmployeeData = bindAppUserInfoDAO.getSpreadTaskByEaAndUserId(ea, fsUserId);
        // 查询虚拟身份数据
        List<SpreadTaskEntity> virtualEmployeeData = bindAppUserInfoDAO.getSpreadTaskByEaAndUserId(ea, virtualUserId);
        if (CollectionUtils.isNotEmpty(virtualEmployeeData)) {
            // 需要删除数据
            List<String> needDeleteIds = Lists.newArrayList();
            if (CollectionUtils.isEmpty(fsEmployeeData)) {
                bindAppUserInfoDAO.updateSpreadTaskByEaAndUser(ea, fsUserId, virtualUserId);
            } else {
                Map<String, SpreadTaskEntity> virtualEmployeeMap = virtualEmployeeData.stream()
                    .collect(Collectors.toMap(SpreadTaskEntity::getMarketingActivityId, data -> data, (v1, v2) -> v1));
                for (SpreadTaskEntity fsData : fsEmployeeData) {
                    SpreadTaskEntity virtualData = virtualEmployeeMap.get(fsData.getMarketingActivityId());
                    if (virtualData != null) {
                        virtualEmployeeMap.remove(fsData.getMarketingActivityId());
                        needDeleteIds.add(virtualData.getId());
                    }
                }
                if (MapUtils.isNotEmpty(virtualEmployeeMap)) {
                    for (Map.Entry<String, SpreadTaskEntity> entry : virtualEmployeeMap.entrySet()) {
                        SpreadTaskEntity virtualData = entry.getValue();
                        bindAppUserInfoDAO.updateSpreadTaskUser(fsUserId, virtualData.getId());
                    }
                }
                // 需要删除的数据
                if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                    bindAppUserInfoDAO.deleteSpreadTaskData(needDeleteIds);
                }
            }
        }

    }

    public Map<String, ChannelData> handleChannelData(ChannelDataList fsChannelData, ChannelDataList virtualChannelData) {
        Map<String, ChannelData> fsUserChannelMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(fsChannelData)) {
            fsUserChannelMap = fsChannelData.stream().collect(Collectors.toMap(ChannelData::getChannelKey, data -> data, (v1, v2) -> v1));
        }
        for (ChannelData channelData : virtualChannelData) {
            fsUserChannelMap.merge(channelData.getChannelKey(), channelData, (fsData, virtualData) -> {
                fsData.add(virtualData);
                return fsData;
            });
        }
        return fsUserChannelMap;
    }

}
