package com.facishare.marketing.provider.manager;

import com.facishare.mankeep.api.result.BaseUserInfoResult;
import com.facishare.mankeep.api.service.UserService;
import com.facishare.mankeep.common.result.ModelResult;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class KmUserManager {
    @Autowired
    private UserService userService;

    public Map<String, BaseUserInfoResult> batchGetBaseUserInfo(List<String> uids) {
        try {
            ModelResult<Map<String, BaseUserInfoResult>> basicInfoMapResult = userService.batchGetBaseUserInfo(uids);
            return basicInfoMapResult.getData() == null ? new HashMap<>(0) : basicInfoMapResult.getData();
        } catch (Exception e) {
            log.warn("kmUserService error", e);
            return new HashMap<>(0);
        }
    }
}
