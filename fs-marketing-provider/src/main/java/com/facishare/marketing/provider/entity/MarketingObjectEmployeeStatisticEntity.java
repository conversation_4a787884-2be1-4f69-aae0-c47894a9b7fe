package com.facishare.marketing.provider.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * Created  By zhoux 2020/05/21
 **/
@Data
public class MarketingObjectEmployeeStatisticEntity implements Serializable {

    private String id;
    private String ea;
    private String objectId;
    private Integer objectType;
    private Integer fsUserId;
    private Date date;
    private Integer spreadCount;
    private Integer forwardCount;
    private Integer lookUpCount;
    private Integer forwardUserCount;
    private Integer lookUpUserCount;
    private Date createTime;
    private Date updateTime;

    public void mergeData(MarketingObjectEmployeeStatisticEntity mergeData) {
        this.setSpreadCount(getIntValue(this.getSpreadCount()) + getIntValue(mergeData.getSpreadCount()));
        this.setForwardCount(getIntValue(this.getForwardCount()) + getIntValue(mergeData.getForwardCount()));
        this.setLookUpCount(getIntValue(this.getLookUpCount()) + getIntValue(mergeData.getLookUpCount()));
        this.setLookUpUserCount(getIntValue(this.getLookUpUserCount()) + getIntValue(mergeData.getLookUpUserCount()));
        this.setForwardUserCount(getIntValue(this.getForwardUserCount()) + getIntValue(mergeData.getForwardUserCount()));
    }

    private int getIntValue(Integer value){
        return value == null ? 0 : value.intValue();
    }
}
