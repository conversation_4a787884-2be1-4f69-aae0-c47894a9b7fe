package com.facishare.marketing.provider.entity;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;

import com.facishare.marketing.common.enums.CustomizeFormDataUserSourceTypeEnum;
import com.facishare.marketing.common.model.SmsParamObject;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormBindOtherCrmObject;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2019/04/09
 **/
@Data
@ToString(exclude = {"submitContent"})
public class CustomizeFormDataUserEntity implements Serializable , SmsParamObject {

    private String ea;

    private String id;

    private String uid;

    private String formId;

    private String objectId;

    private Integer objectType;

    private String parentObjectId;

    private Integer parentObjectType;

    private CustomizeFormDataEnroll submitContent;

    private String marketingActivityId;

    private Integer saveCrmStatus;

    private String saveCrmErrorMessage;

    private Date createTime;

    private Integer spreadFsUid;

    private String leadId;

    private String openId;

    private String wxAppId;

    private Integer enrollUserFsUid;

    private String enrollUserEa;

    private String marketingEventId;

    /**
     * 来源类型
     * {@link CustomizeFormDataUserSourceTypeEnum}
     */
    private Integer sourceType;

    private String fingerPrint;

    private Integer formUsage;

    private String payOrderId;

    private String  extraDataId;

    private CustomizeFormBindOtherCrmObject otherCrmObjectBind;

    // 参会人员关联id
    private String campaignId;

    // 渠道value
    private String channelValue;
    private String outTenantId;

    private String userAgent;

    private String ipAddr;

    private boolean partner;
    private String outUid;

    // 落地页的ID
    private String landingObjId;

    private String spreadUserIdentifyId;

    private String fromUserMarketingId;

    public boolean haveWxAppIdAndOpenId() {
        if (StringUtils.isNotEmpty(wxAppId) && StringUtils.isNotEmpty(openId)) {
            return true;
        }
        return false;
    }

    public boolean openIdNotNull() {
        return StringUtils.isNotEmpty(openId);
    }

    @Override
    public Map<String, String> getParamDescMap() {
        Map<String, String> paramDescMap = new HashMap<>();
        paramDescMap.put("enroll.name", I18nUtil.get(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_70));
        paramDescMap.put("enroll.phone", I18nUtil.get(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_72));
//        paramDescMap.put("enroll.email", "邮箱");
        return paramDescMap;
    }

    @Override
    public Map<String, String> getParamValueMap() {
        Map<String, String> paramValueMap = new HashMap<>();
        paramValueMap.put("enroll.name", submitContent.getName());
        paramValueMap.put("enroll.phone", submitContent.getPhone());
//        paramValueMap.put("enroll.email", submitContent.getEmail());
        return paramValueMap;
    }

}