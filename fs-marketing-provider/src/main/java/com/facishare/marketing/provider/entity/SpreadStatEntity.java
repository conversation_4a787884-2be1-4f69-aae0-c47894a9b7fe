package com.facishare.marketing.provider.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * Created  By zhoux 2019/03/25
 **/
@Data
public class SpreadStatEntity implements Serializable {

    /**
     * 主键uuid
     */
    private String id;

    /**
     * 物料的唯一性标识
     */
    private String objectId;

    /**
     * 物料的类型
     */
    private Integer objectType;

    /**
     * 物料所属人的uid
     */
    private String targetUid;

    /**
     * 转发人的uid
     */
    private String sourceUid;

    /**
     * 父节点动态id，为空表示根节点
     */
    private String parentFeedKey;

    /**
     * 物料的动态id
     */
    private String feedKey;

    /**
     * 节点在树的层次
     */
    private Integer level;

    /**
     * 阅读数
     */
    private Integer readCnt;
    /**
     * 转发数
     */
    private Integer forwardCnt;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 人脉度，根据根节点动态取值
     */
    private Integer deLevel;

    /**
     * 该节点所有上级节点列表
     */
    private List<String> feedKeys;

    /**
     * 公司纷享账号
     */
    private String ea;

    /**
     * 公司阅读数
     */
    private Integer readEaCnt;
    /**
     * 公司转发数
     */
    private Integer forwardEaCnt;

    /**
     * 线索数
     */
    private Integer clueNum;

}
