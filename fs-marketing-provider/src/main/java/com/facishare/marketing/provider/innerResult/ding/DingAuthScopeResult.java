package com.facishare.marketing.provider.innerResult.ding;

import com.facishare.marketing.api.result.dingding.DingBaseResult;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/1 11:28
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DingAuthScopeResult extends DingBaseResult {

    @SerializedName("auth_user_field")
    private List<String> authUserField;

    @SerializedName("auth_org_scopes")
    private AuthOrgScopes authOrgScopes;

    @Data
    public static class AuthOrgScopes implements Serializable {

        @SerializedName("authed_user")
        private List<String> authedUser;

        @SerializedName("authed_dept")
        private List<Integer> authedDept;
    }
}
