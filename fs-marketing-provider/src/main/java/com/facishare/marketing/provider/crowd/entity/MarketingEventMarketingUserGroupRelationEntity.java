package com.facishare.marketing.provider.crowd.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MarketingEventMarketingUserGroupRelationEntity implements Serializable {

    private String id;
    private String ea;
    private String parentMarketingEventId;
    private String marketingEventId;
    private String marketingUserGroupId;
    private Date createTime;
    private Integer createBy;
    private Date updateTime;
    private Integer updateBy;

}
