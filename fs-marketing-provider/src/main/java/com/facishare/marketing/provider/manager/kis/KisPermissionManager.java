package com.facishare.marketing.provider.manager.kis;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.arg.GetSubordinateEmployeesDtoArg;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.model.employee.result.GetSubordinateEmployeesDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2019/02/26
 **/
@Service
@Slf4j
public class KisPermissionManager {
    @ReloadableProperty("marketing_appid")
    private String appId;
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private OpenAppAdminService openAppAdminService;
    @Autowired
    private EIEAConverter converter;
    /**
     * 当前用户是否为leader
     */
    public boolean isLeader(Integer userId, Integer ei) {
        boolean leader = false;
        // 判断当前人是否为leader(若有直属下属则为leader)
        GetSubordinateEmployeesDtoArg getSubordinateEmployeesDtoArg = new GetSubordinateEmployeesDtoArg();
        getSubordinateEmployeesDtoArg.setEmployeeId(userId);
        getSubordinateEmployeesDtoArg.setEnterpriseId(ei);
        GetSubordinateEmployeesDtoResult getSubordinateEmployeesDtoResult = employeeProviderService.getSubordinateEmployees(getSubordinateEmployeesDtoArg);
        if (getSubordinateEmployeesDtoResult != null && CollectionUtils.isNotEmpty(getSubordinateEmployeesDtoResult.getEmployeeDtos())) {
            leader = true;
        }
        return leader;
    }

    /**
     * 当前人员是否为营销通管理员
     */
    public boolean isAppAdmin(String ea, Integer userId) {
        boolean appAdmin = false;
        String fsUserId = "E." + ea + "." + userId;
        BaseResult<Boolean> baseResult = openAppAdminService.isAppAdmin(fsUserId, appId);
        if (!baseResult.isSuccess()) {
            log.error("KisPermissionManager.isAppAdmin failed, ea={}, userId={}", ea, userId);
        }
        if (BooleanUtils.isTrue(baseResult.getResult())) {
            appAdmin = true;
        }
        return appAdmin;
    }

    /*
     * @Description 获取营销通应用管理员名字
     * <AUTHOR>
     * @Date 11:21 AM 2019/3/8
     --------------------------
     * @Param [ea]
     * @return java.lang.String
     *
     **/
    public String lastestAppAdminName(String ea, Integer ei) {
        BaseResult<List<Integer>> baseResult = openAppAdminService.getAppAdminIds(ea, appId);
        if (!baseResult.isSuccess()) {
            log.warn("KisPermissionManager.lastestAppAdminName failed, ea={}", ea);
            return null;
        }

        List<Integer> adminIds = baseResult.getResult();
        if (CollectionUtils.isEmpty(adminIds)) {
            log.warn("KisPermissionManager.lastestAppAdminName adminIds is null, ea={}", ea);
            return null;
        }

        GetEmployeeDtoArg getEmployeeDtoArg = new GetEmployeeDtoArg();
        getEmployeeDtoArg.setEnterpriseId(converter.enterpriseAccountToId(ea));
        getEmployeeDtoArg.setEmployeeId(adminIds.get(0));
        GetEmployeeDtoResult getEmployeeDtoResult = employeeProviderService.getEmployeeDto(getEmployeeDtoArg);
        if (null == getEmployeeDtoResult) {
            log.warn("KisPermissionManager.lastestAppAdminName getEmployeeDto failed, arg={}", getEmployeeDtoArg);
            return null;
        }

        EmployeeDto employeeDto = getEmployeeDtoResult.getEmployeeDto();
        if (null == employeeDto) {
            log.warn("KisPermissionManager.lastestAppAdminName getEmployeeDto is null, arg={}", getEmployeeDtoArg);
            return null;
        }

        return employeeDto.getName();
    }
}
