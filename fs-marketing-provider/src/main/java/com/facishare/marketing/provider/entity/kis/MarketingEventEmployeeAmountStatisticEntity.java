package com.facishare.marketing.provider.entity.kis;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by ranluch on 2019/2/25.
 */
@Data
@Entity
public class MarketingEventEmployeeAmountStatisticEntity implements Serializable {
    private String id;
    private String ea;
    private String marketingEventId;
    private Integer objectType;
    private String objectId;
    private Integer spreadCount;
    private Integer forwardCount;
    private Integer lookUpCount;
    private Date createTime;
    private Date updateTime;

    public void mergeData(MarketingEventEmployeeAmountStatisticEntity mergeData) {
        this.setSpreadCount(getIntValue(this.getSpreadCount()) + getIntValue(mergeData.getSpreadCount()));
        this.setForwardCount(getIntValue(this.getForwardCount()) + getIntValue(mergeData.getForwardCount()));
        this.setLookUpCount(getIntValue(this.getLookUpCount()) + getIntValue(mergeData.getLookUpCount()));
    }

    private int getIntValue(Integer value){
        return value == null ? 0 : value.intValue();
    }
}
