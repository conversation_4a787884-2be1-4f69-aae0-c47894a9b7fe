package com.facishare.marketing.provider.dao.baidu;

import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import com.facishare.marketing.provider.entity.baidu.AdObjectFieldMappingEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created by zhengh on 2021/2/2.
 */
public interface AdObjectFieldMappingDAO {
    @Insert("INSERT INTO ad_crm_object_field_mapping(id, ea, create_by, crm_data_mapping, crm_api_name, crm_record_type, create_time,\n"
            + "update_time) VALUES(#{entity.id}, #{entity.ea}, #{entity.createBy}, #{entity.crmDataMapping}, #{entity.crmApiName}, #{entity.crmRecordType}, now(), now())")
    int insert(@Param("entity")AdObjectFieldMappingEntity entity);

    @Update("UPDATE ad_crm_object_field_mapping SET create_by=#{createBy}, crm_data_mapping=#{fieldMapping} WHERE ea=#{ea} AND crm_api_name=#{crmApiName}")
    int updateByApiName(@Param("ea")String ea, @Param("crmApiName")String crmApiName, @Param("fieldMapping")FieldMappings fieldMapping,
                        @Param("createBy")Integer createBy);

    @Select("SELECT * FROM ad_crm_object_field_mapping WHERE ea=#{ea} AND crm_api_name=#{crmApiName}")
    AdObjectFieldMappingEntity getByApiName(@Param("ea")String ea, @Param("crmApiName")String crmApiName);

    @Update("UPDATE ad_crm_object_field_mapping SET enable=#{status}, update_time=now() WHERE id=#{id}")
    void updateStatusById(@Param("id")String id, @Param("status")Integer status);
}
