package com.facishare.marketing.provider.innerData.live.polyv;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 保利威直播场次
 */
@Data
public class PolyvLiveLikeData implements Serializable{
    @SerializedName("code")
    private Integer code;

    @SerializedName("status")
    private String status;

    @SerializedName("message")
    private String message;

    @SerializedName("data")
    private LikeData data;

    @Data
    @ToString
    public static class LikeData implements Serializable {
        @SerializedName("pageSize")
        private Integer pageSize;               //每页数据大小，默认20条数据

        @SerializedName("pageNumber")
        private Integer pageNumber;            //当前页数

        @SerializedName("totalPages")
        private Long totalPages;           //总的条数

        @SerializedName("totalItems")
        private Long totalItems;           //总的条数

        @SerializedName("contents")
        private List<Contents> contents;       //频道详细信息列表
    }

    @Data
    @ToString
    public static class Contents implements Serializable {

        @SerializedName("userId")
        private String userid;

        @SerializedName("count")
        private Integer count;            //频道号
    }

}
