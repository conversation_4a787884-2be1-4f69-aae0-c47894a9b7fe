package com.facishare.marketing.provider.entity.conference;

import com.facishare.marketing.common.typehandlers.value.ButtonStyle;
import com.facishare.marketing.common.typehandlers.value.FieldValueList;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created  By zhoux 2019/07/16
 **/
@Data
public class ConferenceInvitationEntity implements Serializable {

    private String id;

    private String ea;

    private String activityId;

    private String name;

    private FieldValueList invitationDetails;

    private ButtonStyle invitationButton;

    private Integer status;

    private Integer createBy;

    private Date createTime;

    private Integer updateBy;

    private Date updateTime;

}
