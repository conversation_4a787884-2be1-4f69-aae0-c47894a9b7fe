package com.facishare.marketing.provider.manager.mail.result;

import com.google.gson.annotations.SerializedName;
import java.util.List;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhengh on 2020/6/5.
 */
@Data
public class CheckMailDomainResp implements Serializable{

    List<CheckMailResult> dataList;

    @Data
    public class CheckMailResult implements Serializable {
        private String name;

        private Integer type;

        @SerializedName("isConfigSucess")
        private Boolean isConfigSuccess;

        private String message;

        private ConfigInfo configInfo;
    }

    @Data
    public class ConfigInfo implements Serializable {
        @SerializedName("MX")
        private Boolean mx;	     //MX是否配置
        @SerializedName("SPF")
        private Boolean spf;     //SPF是否配置
        @SerializedName("DKIM")
        private Boolean dkim;     //DKIM是否配置
        @SerializedName("DMARC")
        private Boolean dmarc;     //DMARC是否配置
    }
}
