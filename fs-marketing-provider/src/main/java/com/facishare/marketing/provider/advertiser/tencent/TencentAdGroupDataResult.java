package com.facishare.marketing.provider.advertiser.tencent;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TencentAdGroupDataResult implements Serializable {
    private List<TencentAdGroupData> list;
    private PageInfo page_info;

    @Data
    public static class TencentAdGroupData implements Serializable{
        /**
         * 	广告主帐号 id，有操作权限的帐号 id，不支持代理商 id。仅支持腾讯广告账户
         */
        private Long account_id;

        /**
         * 推广计划id
         */
        private Long campaign_id;

        /**
         * 广告组id
         */
        private Long adgroup_id;

        /**
         * 日期，日期格式：YYYY-MM-DD。仅支持腾讯广告账户
         */
        private String date;

        /**
         * 曝光量。广告被展现给用户的次数。仅支持腾讯广告账户
         */
        private Integer view_count;

        /**
         * 千次展现均价。广告平均每一千次展现所付出的费用，计算公式是：花费/曝光量*1000。仅支持腾讯广告账户
         */
        private Double thousand_display_price;

        /**
         * 点击量。经过平台反作弊判断后有效且被计费的点击次数。仅支持腾讯广告账户
         */
        private Integer valid_click_count;

        /**
         * 点击均价。广告主为每次点击付出的费用成本，计算公式是：花费/点击量。仅支持腾讯广告账户
         */
        private Double cpc;

        /**
         * 花费。广告主为广告投放总共付出的费用成本，实际花费请以财务记录为准。仅支持腾讯广告账户
         */
        private Double cost;
    }
}
