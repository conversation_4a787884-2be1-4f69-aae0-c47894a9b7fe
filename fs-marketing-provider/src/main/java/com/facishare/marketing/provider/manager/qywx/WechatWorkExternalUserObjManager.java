package com.facishare.marketing.provider.manager.qywx;

import com.facishare.converter.EIEAConverter;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.marketing.api.result.usermarketingaccount.UserMarketingAccountIdResult;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.common.contstant.*;
import com.facishare.marketing.common.enums.CrmWechatWorkExternalUserFieldEnum;
import com.facishare.marketing.common.enums.PaasAndCrmOperatorEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.qywx.AppScopeEnum;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.UserRoleDao;
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeDAO;
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeRelationDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCustomerAppInfoDAO;
import com.facishare.marketing.provider.entity.UserRoleEntity;
import com.facishare.marketing.provider.entity.qywx.QywxAddFanQrCodeEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity;
import com.facishare.marketing.provider.innerArg.qywx.ExternalContactEvent;
import com.facishare.marketing.provider.innerData.qywx.ChangeExternalContactEventMsg;
import com.facishare.marketing.provider.innerResult.qywx.GetExternalContactDetailResult;
import com.facishare.marketing.provider.innerResult.qywx.QywxEventDelayMqArg;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.UserRoleManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatFriendsRecordObjDescribeManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatGroupUserObjDescribeManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.mq.handler.CrmQywxDataSyncMessageHandler;
import com.facishare.marketing.provider.mq.sender.DelayQueueSender;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.arg.*;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.MetadataTagResult;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.GetTeamMemberResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.limit.GuavaLimiter;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WechatWorkExternalUserObjManager {
    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Resource
    private NFileStorageService nFileStorageService;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private QywxAddFanQrCodeDAO qywxAddFanQrCodeDAO;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private MetadataTagManager metadataTagManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private QywxAddFanQrCodeRelationDAO qywxAddFanQrCodeRelationDAO;
    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;
    @Autowired
    private QywxContactManager qywxContactManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private UserMarketingAccountService userMarketingAccountService;
    @Autowired
    private UserRoleDao userRoleDao;
    @Autowired
    private WechatFriendsRecordObjDescribeManager wechatFriendsRecordObjDescribeManager;

    @Autowired
    private QyweixinAccountBindManager qyweixinAccountBindManager;

    @Autowired
    private DelayQueueSender delayQueueSender;

    @Autowired
    private WechatGroupUserObjDescribeManager wechatGroupUserObjDescribeManager;
    @Autowired
    private UserRoleManager userRoleManager;

    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;

    @ReloadableProperty("qywx.crm.appid")
    private String qywxCrmAppId;

    @Autowired
    private ConfigService configService;

    @Autowired
    private AppVersionManager appVersionManager;

    public final static String APP_SCOPE_FIELD = "app_scope";

    public static final String ADD_EXTERNAL_USER_LOCK_KEY = "add_wx_work_external_user_%s_%s";

    public static final String QYWX_INIT_RATE_LIMIT_KEY = "limit-yxt-qywx-init";

    public int initEnterpriseData(String ea){
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            return -1;
        }

        List<QywxCustomerAppInfoEntity> qywxCustomerAppInfoEntities =
                qywxCustomerAppInfoDAO.selectByCorpIdAndEa(agentConfig.getCorpid(), agentConfig.getEa());

        //不是老用户且还没授权不查
        if (agentConfig.getSecret() == null && qywxCustomerAppInfoEntities.size()==0) {
            return -1;
        }
        String key = "MARKETING_SYNC_QYWX_CUSTOMER_" + ea;
        if (redisManager.get(key) != null){
            log.info("initEnterpriseData task reject task is running ea:{}", ea);
            return 0;
        }

        int addCount = 0;
        try {
            redisManager.set(key,  3600 * 8, key);
            String accessToken = qywxManager.getAccessToken(ea);
            List<String> enterpriseUserIds = qywxManager.getFollowUserList(accessToken);
            Set<String> enterpriseCustomerIds = new HashSet<>(enterpriseUserIds.size() * 5);
            log.info("[initEnterpriseData] query enterpriseUser ea:{} enterpriseUser count:{}", ea, enterpriseUserIds.size());
            Iterator<List<String>> enterpriseUserIdListIterator = Iterables.partition(enterpriseUserIds, 100).iterator();
            while (enterpriseUserIdListIterator.hasNext()){
                List<String> enterpriseCustomerIdList = enterpriseUserIdListIterator.next();
                Set<String> externalConcactIdSet = qywxManager.batchGetQywxCustomerExternalUserId(ea, enterpriseCustomerIdList);
                if (CollectionUtils.isNotEmpty(externalConcactIdSet)){
                    enterpriseCustomerIds.addAll(externalConcactIdSet);
                }
            }

            log.info("[initEnterpriseData] start sync customers ea:{} customer count:{}", ea, enterpriseCustomerIds.size());
            if (!enterpriseCustomerIds.isEmpty()) {
                Iterator<List<String>> enterpriseCustomerIdListIterator = Iterables.partition(enterpriseCustomerIds, 1).iterator();
                while (enterpriseCustomerIdListIterator.hasNext()) {
                    List<String> enterpriseCustomerIdList = enterpriseCustomerIdListIterator.next();
                    try {
                        // 限流 不能写太快了
                        GuavaLimiter.acquire(QYWX_INIT_RATE_LIMIT_KEY, ea);
                        addCount += mergeWxWorkExternalUserListToCrmLimited(ea, enterpriseCustomerIdList, true, null);
                        log.info("[initEnterpriseData] current sync customers ea:{} customer count:{}", ea, addCount);
                    } catch (Exception e) {
                        log.warn("Exception, enterpriseCustomerIdList:{}", enterpriseCustomerIdList, e);
                    }
                }
            }
        }finally {
            redisManager.delete(key);
        }
        return addCount;
    }

    /**
     * 脚本调用, 初次全量拉取企微客户数据使用
     *
     * @param ea
     * @return
     */
    @Deprecated
    public int initEnterpriseDataV2(String ea) {
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            return -1;
        }

        List<QywxCustomerAppInfoEntity> qywxCustomerAppInfoEntities =
                qywxCustomerAppInfoDAO.selectByCorpIdAndEa(agentConfig.getCorpid(), agentConfig.getEa());

        //不是老用户且还没授权不查
        if (agentConfig.getSecret() == null && qywxCustomerAppInfoEntities.size() == 0) {
            return -1;
        }
        String key = "MARKETING_SYNC_QYWX_CUSTOMER_" + ea;
        if (redisManager.get(key) != null) {
            log.info("initEnterpriseDataV2 task reject task is running ea:{}", ea);
            return 0;
        }

        AtomicInteger addCount = new AtomicInteger();
        AtomicInteger handleCount = new AtomicInteger();
        try {
            redisManager.set(key, 3600 * 8, key);
            String accessToken = qywxManager.getAccessToken(ea);
            List<String> enterpriseUserIds = qywxManager.getFollowUserList(accessToken);
            log.info("[initEnterpriseDataV2] query enterpriseUser ea:{} enterpriseUser count:{}", ea, enterpriseUserIds.size());
            for (List<String> enterpriseCustomerIdList : Iterables.partition(enterpriseUserIds, 100)) {
                qywxManager.batchGetQywxCustomerExternalUserIdV2(ea, enterpriseCustomerIdList, (enterpriseCustomerIds) -> {
                    if (!enterpriseCustomerIds.isEmpty()) {
                        ThreadPoolUtils.submit(() -> {
                            for (List<String> partEnterpriseCustomerIdList : Iterables.partition(enterpriseCustomerIds, 1000)) {
                                try {
                                    // 限流 不能写太快了
                                    GuavaLimiter.acquire(QYWX_INIT_RATE_LIMIT_KEY, ea);
                                    addCount.addAndGet(mergeWxWorkExternalUserListToCrmLimited(ea, partEnterpriseCustomerIdList, false, null));
                                    log.info("[initEnterpriseDataV2] current sync customers ea:{} customer addCount:{}", ea, addCount);
                                    handleCount.addAndGet(1);
                                    log.info("[initEnterpriseDataV2] current sync customers ea:{} customer handleCount:{}", ea, handleCount);
                                } catch (Exception e) {
                                    log.warn("Exception, enterpriseCustomerIdList:{}", partEnterpriseCustomerIdList, e);
                                }
                            }
                        }, ThreadPoolUtils.ThreadPoolTypeEnums.QYWX_DATA_INIT);
                    }
                });
            }
        } catch (Exception e) {
            log.warn("Exception, initEnterpriseDataV2", e);
        } finally {
            redisManager.delete(key);
        }
        return addCount.get();
    }

    public int initQywxUserIdData(String ea, List<String> qywxUserIds) {
        if (CollectionUtils.isEmpty(qywxUserIds)) {
            return -1;
        }
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            return -1;
        }
        List<QywxCustomerAppInfoEntity> qywxCustomerAppInfoEntities =
                qywxCustomerAppInfoDAO.selectByCorpIdAndEa(agentConfig.getCorpid(), agentConfig.getEa());
        //不是老用户且还没授权不查
        if (agentConfig.getSecret() == null && qywxCustomerAppInfoEntities.size() == 0) {
            return -1;
        }
        int addCount = 0;
        String accessToken = qywxManager.getAccessToken(ea);
        List<String> enterpriseUserIds = Lists.newArrayList(qywxUserIds);
        Set<String> enterpriseCustomerIds = qywxManager.batchGetQywxCustomerExternalUserId(ea, enterpriseUserIds);
        log.info("[initQywxUserIdData] start sync customers ea:{} customer count:{}", ea, enterpriseCustomerIds.size());
        if (!enterpriseCustomerIds.isEmpty()) {
            Iterator<List<String>> enterpriseCustomerIdListIterator = Iterables.partition(enterpriseCustomerIds, 1).iterator();
            while (enterpriseCustomerIdListIterator.hasNext()) {
                List<String> enterpriseCustomerIdList = enterpriseCustomerIdListIterator.next();
                try {
                    // 限流 不能写太快了
                    GuavaLimiter.acquire(QYWX_INIT_RATE_LIMIT_KEY, ea);
                    addCount += mergeWxWorkExternalUserListToCrmLimited(ea, enterpriseCustomerIdList, true, null);
                    log.info("[initQywxUserIdData] current sync customers ea:{} customer count:{}", ea, addCount);
                } catch (Exception e) {
                    log.warn("Exception, enterpriseCustomerIdList:{}", enterpriseCustomerIdList, e);
                }
            }
        }
        return addCount;
    }

    public void pullQywxDataByEnterpriseUserIds(String ea, List<String> enterpriseUserIds) {
        String accessToken = qywxManager.getAccessToken(ea);
        Set<String> enterpriseCustomerIds = new HashSet<>(enterpriseUserIds.size() * 5);

        Iterator<List<String>> enterpriseUserIdListIterator = Iterables.partition(enterpriseUserIds, 100).iterator();
        while (enterpriseUserIdListIterator.hasNext()) {
            List<String> enterpriseCustomerIdList = enterpriseUserIdListIterator.next();
            Set<String> externalConcactIdSet = qywxManager.batchGetQywxCustomerExternalUserId(ea, enterpriseCustomerIdList);
            if (CollectionUtils.isNotEmpty(externalConcactIdSet)) {
                enterpriseCustomerIds.addAll(externalConcactIdSet);
            }
        }

        log.info("Sync all enterprise customer:{} for ea:{}", enterpriseCustomerIds, ea);
        if (!enterpriseCustomerIds.isEmpty()) {
            Iterator<List<String>> enterpriseCustomerIdListIterator = Iterables.partition(enterpriseCustomerIds, 100).iterator();
            while (enterpriseCustomerIdListIterator.hasNext()) {
                List<String> enterpriseCustomerIdList = enterpriseCustomerIdListIterator.next();
                try {
                    mergeWxWorkExternalUserListToCrmLimited(ea, enterpriseCustomerIdList, true, null);
                } catch (Exception e) {
                    log.warn("Exception, enterpriseCustomerIdList:{}", enterpriseCustomerIdList, e);
                }
            }
        }
    }

    public Map<String, ObjectData> getObjectDataMap(String ea, Collection<String> externalUserIdToDisplay) {
        Map<String, ObjectData> crmWxWorkExternalDataMap = new HashMap<>(externalUserIdToDisplay.size());
        Iterator<List<String>> ite = Iterables.partition(externalUserIdToDisplay, 100).iterator();
        ite.forEachRemaining(userIds -> {
            com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> r = this.listObjectDataByIdsLimited(eieaConverter.enterpriseAccountToId(ea), userIds);
            if(r.getData() != null && r.getData().getDataList() != null){
                r.getData().getDataList().forEach(objectData -> {
                    crmWxWorkExternalDataMap.put(objectData.getString("external_user_id"), objectData);
                });
            }
        });
        return crmWxWorkExternalDataMap;
    }

    /**
     * 创建或者更新企业微信用户对象到CRM, 不存在则创建，存在则更新，更新时会连团队成员一起更新。
     * @param ea
     * @param externalUserIds
     * @return
     */
    public int mergeWxWorkExternalUserListToCrmLimited(String ea, List<String> externalUserIds, boolean triggerUpdate, ChangeExternalContactEventMsg eventMsg){
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            return 0;
        }
        Integer ei = eieaConverter.enterpriseAccountToId(ea);

        Result<Page<ObjectData>> searchResult = listObjectDataByIdsLimited(ei, externalUserIds);
        Set<String> existedExternalUserIdSet = searchResult.getData() == null ? new HashSet<>(0) : searchResult.getData().getDataList().stream().map(od -> od.getString("external_user_id")).collect(Collectors.toSet());
        int incrementCount = 0;
        for (String externalUserId : externalUserIds) {
            if (!existedExternalUserIdSet.contains(externalUserId)){
                try {
                    String accessToken = qywxManager.getAccessToken(ea);
                    GetExternalContactDetailResult externalContactDetailResult = doGetExternalContactDetailFromWechat(ea, accessToken, externalUserId);
                    incrementCount += doAddWxWorkExternalUserToCrm(ei, externalUserId, AppScopeEnum.MARKETING, externalContactDetailResult, eventMsg) ? 1 : 0;
                } catch(Exception e){
                    log.warn("Exception, externalUserId:{}", externalUserId, e);
                }
            }
        }
        if(triggerUpdate && searchResult.getData() != null){
            for (ObjectData objectData : searchResult.getData().getDataList()) {
                try {
                    String externalUserId = objectData.getString("external_user_id");
                    String accessToken = qywxManager.getAccessToken(eieaConverter.enterpriseIdToAccount(ei));
                    GetExternalContactDetailResult externalContactDetailResult = doGetExternalContactDetailFromWechat(ea, accessToken, externalUserId);
                    incrementCount += doUpdateWxWorkExternalUserToCrm(ei, AppScopeEnum.MARKETING, objectData, externalContactDetailResult, eventMsg) ? 1 : 0;
                }catch (Exception e){
                    log.warn("Exception, objectData:{}", objectData, e);
                }
            }
        }
        return incrementCount;
    }

    public Result<Page<ObjectData>> listObjectDataByIdsLimited(Integer ei, List<String> externalUserIds) {
        Result<Page<ObjectData>> result = new Result<>();
        Page<ObjectData> page = new Page<>();
        page.setDataList(Lists.newArrayList());
        result.setData(page);
        if (CollectionUtils.isEmpty(externalUserIds)) {
            result.setCode(-1);
            page.setTotal(0);
            return result;
        }
        int pageSize = 200;
        for (List<String> partition : Lists.partition(externalUserIds, pageSize)) {
            HeaderObj searchHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
            Filter filter = new Filter();
            filter.setFieldName("external_user_id");
            filter.setOperator(Filter.OperatorContants.IN);
            filter.setFieldValues(partition);
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.setLimit(pageSize);
            searchQuery.setFilters(Collections.singletonList(filter));
            ControllerListArg controllerListArg = new ControllerListArg();
            controllerListArg.setIncludeDescribe(false);
            controllerListArg.setSearchQuery(searchQuery);
            Result<Page<ObjectData>> tempResult = metadataControllerService.list(searchHeader, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), controllerListArg);
            if (tempResult == null || tempResult.getData() == null || tempResult.getData().getDataList() == null) {
                continue;
            }
            result.getData().getDataList().addAll(tempResult.getData().getDataList());
        }
        result.setCode(0);
        page.setTotal(result.getData().getDataList().size());
        return result;
    }

    public List<String> filterDataByScop(Integer ei, List<String> externalUserIds) {
        List<String> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(externalUserIds)) {
            return result;
        }
        int pageSize = 1000;
        for (List<String> partition : Lists.partition(externalUserIds, pageSize)) {
            HeaderObj searchHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.setLimit(pageSize);
            searchQuery.addFilter("external_user_id", partition, Filter.OperatorContants.IN);
            searchQuery.addFilter(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, Collections.singletonList(AppScopeEnum.MARKETING.getValue()), Filter.OperatorContants.IN);
            ControllerListArg controllerListArg = new ControllerListArg();
            controllerListArg.setIncludeDescribe(false);
            controllerListArg.setSearchQuery(searchQuery);
            Result<Page<ObjectData>> tempResult = metadataControllerService.list(searchHeader, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), controllerListArg);
            if (tempResult == null || tempResult.getData() == null || tempResult.getData().getDataList() == null) {
                continue;
            }
            result.addAll(tempResult.getData().getDataList().stream().map(od -> od.getString("external_user_id")).collect(Collectors.toList()));
        }
        return result;
    }

    public int countAllExternalUser(String ea) {
        HeaderObj searchHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(1);
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setIncludeDescribe(false);
        controllerListArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> result = metadataControllerService.list(searchHeader, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), controllerListArg);
        if (result.isSuccess() && result.getData() != null){
            return result.getData().getTotal();
        }
        return 0;
    }

    public boolean doUpdateWxWorkExternalUserToCrm(Integer ei, AppScopeEnum appScopeEnum, ObjectData objectData, GetExternalContactDetailResult externalContactDetailResult, ChangeExternalContactEventMsg eventMsg){
        String externalUserId = Optional.ofNullable(objectData.get("external_user_id"))
                .map(Object::toString).orElse(null);
        String lockKey = String.format(ADD_EXTERNAL_USER_LOCK_KEY, ei+"", externalUserId);
        String lockValue = appScopeEnum.getValue() + "-" + RandomStringUtils.randomAscii(10);
        try {
            boolean lockSuccess = redisManager.lock(lockKey, lockValue, 60);
            log.info("doUpdateWxWorkExternalUserToCrm ei: {} externalObjId: {} lock: {} appScope: {}", ei, externalUserId, lockSuccess, appScopeEnum);
            if(!lockSuccess){
                // 如果获取不到锁 并且获取到锁的应用不是本次应用的通知 延迟消费
                String existLockValue = redisManager.get(lockKey);
                if (StringUtils.isNotBlank(existLockValue) && !existLockValue.contains(appScopeEnum.getValue())) {
                    QywxEventDelayMqArg qywxEventDelayMqArg = new QywxEventDelayMqArg();
                    qywxEventDelayMqArg.setEi(ei);
                    qywxEventDelayMqArg.setExternalUserId(objectData.getString("external_user_id"));
                    qywxEventDelayMqArg.setEvent(QywxEventDelayMqArg.UPDATE_EVENT);
                    qywxEventDelayMqArg.setChangeObjectType(QywxEventDelayMqArg.EXTERNAL_OBJECT_TYPE);
                    qywxEventDelayMqArg.setExternalContactDetailResult(externalContactDetailResult);
                    qywxEventDelayMqArg.setAppScopeEnum(appScopeEnum);
                    qywxEventDelayMqArg.setExternalContactEventMsg(eventMsg);
                    delayQueueSender.sendByObj(eieaConverter.enterpriseIdToAccount(ei), qywxEventDelayMqArg, DelayQueueTagConstants.QYWX_CHANGE_EVENT, RocketMqDelayLevelConstants.TEN_SECOND);
                }
                return false;
            }
            HeaderObj systemHeader = new HeaderObj(ei, -10000);
            String ea = eieaConverter.enterpriseIdToAccount(ei);
            if (externalContactDetailResult == null || externalContactDetailResult.getExternalContact() == null || !externalContactDetailResult.getExternalContact().checkParam()) {
                log.warn("doUpdateWxWorkExternalUserToCrm externalContactDetailResult is null, ea: {} appScope: {} data: {}", ea, appScopeEnum, objectData);
                return false;
            }
            boolean dataUpdated = false;

            Integer ownerId = null;
            Map<String, Integer> outAccountToFsAccountMap = Maps.newHashMap();
            if (AppScopeEnum.MARKETING == appScopeEnum) {
                outAccountToFsAccountMap = doChangeOutAccountToFsAccount(ei, externalContactDetailResult.getFollowUserList());
                String ownerOutAccount = externalContactDetailResult.getOwnerUserId();
                ownerId = outAccountToFsAccountMap.get(ownerOutAccount);
            } else if (AppScopeEnum.CRM == appScopeEnum) {
                ownerId = externalContactDetailResult.getOwnerFsUserId();
            }

            if (ownerId != null){
                objectData.setOwner(ownerId);
            }
            ObjectData currentObjectData = extractExternalContactDetailToMergeObjectDataFields(ei, externalContactDetailResult, false, appScopeEnum);

            if (appScopeEnum == AppScopeEnum.MARKETING) {
                String wxWorkAdder = objectData.getString("wx_work_adder");
                if (StringUtils.isBlank(wxWorkAdder)) {
                    String name = qywxManager.getStaffNameByUserId(ea, externalContactDetailResult.getOwnerUserId());
                    if (StringUtils.isNotBlank(name)) {
                        objectData.put("wx_work_adder", name);
                    }
                }
            }

            for (Map.Entry<String, Object> currentKeyValueEntry : currentObjectData.entrySet()) {
                if(currentKeyValueEntry.getKey() != null && !Objects.equals(currentKeyValueEntry.getValue(), objectData.get(currentKeyValueEntry.getKey()))){
                    objectData.put(currentKeyValueEntry.getKey(), currentKeyValueEntry.getValue());
                    dataUpdated = true;
                }
            }
            boolean appScopeChange = fillAppScope(objectData, appScopeEnum);
            if(dataUpdated || appScopeChange){
                ActionEditArg actionEditArg = new ActionEditArg();
                actionEditArg.setObjectData(objectData);
                dataUpdated = metadataActionService.edit(systemHeader, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), true, true, actionEditArg).isSuccess();
                log.info("doUpdateWxWorkExternalUserToCrm ei: {} actionEditArg:{} result: {}", ei, actionEditArg, dataUpdated);
            }
            // 如果一条数据没有任何跟进人，那么它在这里已经被标记为删除，不需要后续的处理了
            if(!externalContactDetailResult.isAnyoneFollow()){
                return dataUpdated;
            }

            if(ownerId != null && !ownerId.equals(objectData.getOwner())){
                ActionChangeOwnerArg actionChangeOwnerArg = new ActionChangeOwnerArg(objectData.getId(), ownerId);
                actionChangeOwnerArg.setOldOwnerStrategy("1");
                metadataActionService.changeOwner(systemHeader, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), actionChangeOwnerArg);
                crmV2Manager.editObjectDataOwnOrganization(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), objectData.getId(), ownerId);
            }

            GetTeamMemberArg getTeamMemberArg = new GetTeamMemberArg();
            getTeamMemberArg.setObjectDescribeApiName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
            getTeamMemberArg.setDataID(objectData.getId());
            Result<GetTeamMemberResult> getTeamMemberResult = metadataControllerService.getTeamMember(systemHeader, getTeamMemberArg);
            log.info("doUpdateWxWorkExternalUserToCrm ea: {} objectId: {} getTeamMemberResult: {}", ea, objectData.getId(), getTeamMemberResult);
            // 这里是负责增加相关团队，不在负责删除相关团队，删除相关团队在删除外部联系人时处理，原因在于营销助手和SCRM代开发应用可见范围不一样时会有问题
            if (getTeamMemberResult.isSuccess() && getTeamMemberResult.getData() != null) {
                Set<Integer> crmExistedTeamMemberSet = getTeamMemberResult.getData().getAllTeamMember();
                Set<Integer> wechatFollowFsUserIds = Sets.newHashSet();
                if (appScopeEnum == AppScopeEnum.MARKETING) {
                    wechatFollowFsUserIds = new HashSet<>(outAccountToFsAccountMap.values());
                } else if (appScopeEnum == AppScopeEnum.CRM) {
                    if (CollectionUtils.isNotEmpty(externalContactDetailResult.getFollowUserList())) {
                        wechatFollowFsUserIds = externalContactDetailResult.getFollowUserList().stream().map(GetExternalContactDetailResult.FollowUser::getFsUserId).filter(Objects::nonNull).collect(Collectors.toSet());
                    }
                }
                if(ownerId == null && objectData.getOwner() != null && objectData.getOwner() == -10000){
                    wechatFollowFsUserIds.add(-10000);
                }
                wechatFollowFsUserIds.removeAll(crmExistedTeamMemberSet);
                if(!wechatFollowFsUserIds.isEmpty()){
                    crmV2Manager.doAddTeamMemberToCrm(new HeaderObj(ei, -10000), wechatFollowFsUserIds, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), objectData.getId());
                    dataUpdated = true;
                }
            }
            if (appScopeEnum == AppScopeEnum.MARKETING) {
                Set<TagName> tagNames = externalContactDetailResult.getAllEnterpriseTagNames();
//                metadataTagManager.updateTagsToObjectDatas(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), ImmutableList.of(objectData.getId()), new ArrayList<>(tagNames));
                updateUserMarketingTagByExternalUserObjectId(ea, objectData.getId(), tagNames);
            }
            wechatFriendsRecordObjDescribeManager.batchSaveByFollowUserList(ea, objectData.getId(), externalContactDetailResult.getFollowUserList(), appScopeEnum);
            return dataUpdated;
        } catch (Exception e) {
            log.error("doUpdateWxWorkExternalUserToCrm error ei: {} app: {} data: {}", ei, appScopeEnum, objectData, e);
            return false;
        } finally {
            redisManager.unLock(lockKey, lockValue);
        }

    }

    /**
     * 通过企微联系人对象id给营销用户关联的所有对象打标签,会进行全量替换标签
     * @param ea 企业账号
     * @param externalUserObjectId 企业微信客户对象id
     * @param latestTagNames 企微那边的最新标签
     */
    public void updateUserMarketingTagByExternalUserObjectId(String ea, String externalUserObjectId, Set<TagName> latestTagNames) {
        try {
            int tenantId = eieaConverter.enterpriseAccountToId(ea);
            User user = new User(String.valueOf(tenantId), String.valueOf(SuperUserConstants.USER_ID));
            String key = "open_marketing_tag_objects";
            String configValue = configService.findTenantConfig(user, key);
            List<TagName> tagNameList = Lists.newArrayList(latestTagNames);
            if (StringUtils.isBlank(configValue)) {
                // 没有开通营销通标签组件的，只更新企微客户对象
                metadataTagManager.updateTagsToObjectDatas(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), ImmutableList.of(externalUserObjectId), tagNameList);
                return;
            }
            com.facishare.marketing.common.result.Result<UserMarketingAccountIdResult> userMarketingAccountIdResult =  userMarketingAccountService.getUserMarketingAccountByCrmExternalUserId(ea, SuperUserConstants.USER_ID, externalUserObjectId);
            if (!userMarketingAccountIdResult.isSuccess()) {
                metadataTagManager.updateTagsToObjectDatas(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), ImmutableList.of(externalUserObjectId), tagNameList);
                log.info("获取营销用户失败, ea: {} externalUserObjectId: {}", ea, externalUserObjectId);
                return;
            }
            FindAllTagByBulkDataIdArg findAllTagByBulkDataIdArg = new FindAllTagByBulkDataIdArg(tenantId, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), Lists.newArrayList(externalUserObjectId));
            MetadataTagResult<List<DataIdAndMetadataTagData>> result = metadataTagManager.doFindAllTagByBulkDataId(findAllTagByBulkDataIdArg);
            if (CollectionUtils.isEmpty(result.getResult()) && CollectionUtils.isEmpty(latestTagNames)) {
                log.info("企微的标签和对象的标签都为空， ea: {} externalUserObjectId: {}", ea, externalUserObjectId);
                return;
            }
            userMarketingAccountManager.updateTagToObjectByUserMarketingId(ea, userMarketingAccountIdResult.getData().getUserMarketingAccountId(), tagNameList);
            log.info("updateUserMarketingTagByExternalUserObjectId ea: {} externalUserObjectId: {}", ea, externalUserObjectId);
        } catch (Exception e) {
           log.error("updateUserMarketingTagByExternalUserObjectId error, ea: {} externalUserObjectId: {} tag: {}", ea, externalUserObjectId, latestTagNames, e);
        }
    }

    /**
     * 通过企微联系人对象id给营销用户关联的所有对象追加打标签 注意：是追加打
     * @param ea 企业账号
     * @param externalUserObjectId 企业微信客户对象id
     * @param adaptorTagNames 要追加的标签
     */
    public void appendUserMarketingTagByExternalUserObjectId(String ea, String externalUserObjectId, Set<TagName> adaptorTagNames) {
        if (CollectionUtils.isEmpty(adaptorTagNames)) {
            return;
        }
        int tenantId = eieaConverter.enterpriseAccountToId(ea);
        User user = new User(String.valueOf(tenantId), String.valueOf(SuperUserConstants.USER_ID));
        String key = "open_marketing_tag_objects";
        String configValue = configService.findTenantConfig(user, key);
        List<TagName> tagNameList = Lists.newArrayList(adaptorTagNames);
        if (StringUtils.isBlank(configValue)) {
            // 没有开通营销通标签组件的，只更新企微客户对象
            metadataTagManager.addTagsToObjectDatas(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), ImmutableList.of(externalUserObjectId), tagNameList);
            return;
        }
        com.facishare.marketing.common.result.Result<UserMarketingAccountIdResult> userMarketingAccountIdResult =  userMarketingAccountService.getUserMarketingAccountByCrmExternalUserId(ea, SuperUserConstants.USER_ID, externalUserObjectId);
        if (!userMarketingAccountIdResult.isSuccess()) {
            metadataTagManager.addTagsToObjectDatas(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), Lists.newArrayList(externalUserObjectId), tagNameList);
            log.info("获取营销用户失败, ea: {} externalUserObjectId: {}", ea, externalUserObjectId);
            return;
        }
        // 管它三七二十一， 先保证企微对象的同步过去先
        metadataTagManager.addTagsToObjectDatas(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), ImmutableList.of(externalUserObjectId), tagNameList);
        userMarketingAccountManager.appendTagToObjectByUserMarketingId(ea, userMarketingAccountIdResult.getData().getUserMarketingAccountId(), tagNameList);
        log.info("appendUserMarketingTagByExternalUserObjectId ea: {} externalUserObjectId: {}, tagNameList: {}", ea, externalUserObjectId, tagNameList);
    }

    public boolean doAddWxWorkExternalUserToCrm(Integer ei, String externalUserId, AppScopeEnum appScopeEnum, GetExternalContactDetailResult externalContactDetailResult, ChangeExternalContactEventMsg eventMsg){
        String lockKey = String.format(ADD_EXTERNAL_USER_LOCK_KEY, ei+"", externalUserId);
        String lockValue = appScopeEnum.getValue() + "-" + RandomStringUtils.randomAscii(10);
        try {
            boolean lockSuccess = redisManager.lock(lockKey, lockValue, 60);
            log.info("doAddWxWorkExternalUserToCrm ei: {} externalUserId: {} lock: {} appScope: {}", ei, externalUserId, lockSuccess, appScopeEnum);
            if(!lockSuccess){
                // 如果获取不到锁 并且获取到锁的应用不是本次应用的通知 延迟消费
                String existLockValue = redisManager.get(lockKey);
                if (StringUtils.isNotBlank(existLockValue) && !existLockValue.contains(appScopeEnum.getValue())) {
                    QywxEventDelayMqArg qywxEventDelayMqArg = new QywxEventDelayMqArg();
                    qywxEventDelayMqArg.setEi(ei);
                    qywxEventDelayMqArg.setEvent(QywxEventDelayMqArg.ADD_EVENT);
                    qywxEventDelayMqArg.setExternalUserId(externalUserId);
                    qywxEventDelayMqArg.setChangeObjectType(QywxEventDelayMqArg.EXTERNAL_OBJECT_TYPE);
                    qywxEventDelayMqArg.setExternalContactDetailResult(externalContactDetailResult);
                    qywxEventDelayMqArg.setAppScopeEnum(appScopeEnum);
                    qywxEventDelayMqArg.setExternalContactEventMsg(eventMsg);
                    delayQueueSender.sendByObj(eieaConverter.enterpriseIdToAccount(ei), qywxEventDelayMqArg, DelayQueueTagConstants.QYWX_CHANGE_EVENT, RocketMqDelayLevelConstants.TEN_SECOND);
                }
                return false;
            }
            Result<Page<ObjectData>> searchResult = listObjectDataByIdsLimited(ei, ImmutableList.of(externalUserId));
            if(searchResult.isSuccess() && searchResult.getData().getDataList() != null && !searchResult.getData().getDataList().isEmpty()){
                log.warn("doAddWxWorkExternalUserToCrm ei: {} externalUserId: {} already exist, please check", ei, externalUserId);
                return false;
            }
            if (externalContactDetailResult == null || externalContactDetailResult.getExternalContact() == null || !externalContactDetailResult.getExternalContact().checkParam()) {
                log.warn("doAddWxWorkExternalUserToCrm externalContactDetailResult is null, ei: {} appScope: {} externalUserId: {}", ei, appScopeEnum, externalUserId);
                return false;
            }
            HeaderObj addHeader = new HeaderObj(ei, -10000);
            ObjectData objectData = extractExternalContactDetailToMergeObjectDataFields(ei, externalContactDetailResult, true, appScopeEnum);
            fillAppScope(objectData, appScopeEnum);
            log.info("doAddWxWorkExternalUserToCrm ei: {} objectData:{}", ei, objectData);
            addAvatar(ei, externalContactDetailResult.getExternalContact().getAvatar(), objectData);
            Map<String, Integer> outAccountToFsAccountMap = new HashMap<>();
            if (appScopeEnum == AppScopeEnum.MARKETING) {
                outAccountToFsAccountMap = doChangeOutAccountToFsAccount(ei, externalContactDetailResult.getFollowUserList());
            }
            List<Integer> fsUserToTeamMember = getExternalUserObjTeamMember(appScopeEnum, externalContactDetailResult, outAccountToFsAccountMap, objectData);
            if (CollectionUtils.isNotEmpty(externalContactDetailResult.getFollowUserList())) {
                Integer owner = outAccountToFsAccountMap.get(externalContactDetailResult.getFollowUserList().get(0).getUserId());
                if (owner != null) {
                    objectData.setOwner(owner);
                }
            }
            String ea = eieaConverter.enterpriseIdToAccount(ei);
            if (null == objectData.getOwner()) {
                objectData.setOwner(this.queryFsUserIdByRoles(ea));
            }
            ActionAddArg actionAddArg = new ActionAddArg();
            actionAddArg.setObjectData(objectData);
            Result<ActionAddResult> addResult = metadataActionService.add(addHeader, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), false, actionAddArg);
            log.info("doAddWxWorkExternalUserToCrm add crm, ea: {} result: {} objectData: {}", ea, addResult, objectData);
            if(addResult.isSuccess() && addResult.getData() != null && addResult.getData().getObjectData() != null){
                String objectId = addResult.getData().getObjectData().getId();
                if (CollectionUtils.isNotEmpty(fsUserToTeamMember)) {
                    crmV2Manager.doAddTeamMemberToCrm(addHeader, fsUserToTeamMember, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), objectId);
                    log.info("doAddWxWorkExternalUserToCrm add teamMember, objectId:{}, teamMember:{}", objectId, fsUserToTeamMember);
                }
                if (appScopeEnum == AppScopeEnum.MARKETING) {
                    // 同步标签
                    Set<TagName> tagNames = externalContactDetailResult.getAllEnterpriseTagNames();
                    if (!tagNames.isEmpty()){
                        log.info("doAddWxWorkExternalUserToCrm add tags, objectId:{}, tagNames:{}", objectId, tagNames);
                        updateUserMarketingTagByExternalUserObjectId(ea, objectId, tagNames);
                    }
                }
                // 同步企微好友
                wechatFriendsRecordObjDescribeManager.batchSaveByFollowUserList(ea, objectId, externalContactDetailResult.getFollowUserList(), appScopeEnum);
            }
            return addResult.isSuccess();
        } finally {
            redisManager.unLock(lockKey, lockValue);
        }
    }

    private static List<Integer> getExternalUserObjTeamMember(AppScopeEnum appScopeEnum, GetExternalContactDetailResult externalContactDetailResult, Map<String, Integer> outAccountToFsAccountMap, ObjectData objectData) {
        List<Integer> fsUserToTeamMember = new LinkedList<>();
        for (int i = 0; i < externalContactDetailResult.getFollowUserList().size(); i++) {
            Integer fsUserId = null;
            GetExternalContactDetailResult.FollowUser followUser = externalContactDetailResult.getFollowUserList().get(i);
            if (appScopeEnum == AppScopeEnum.CRM) {
                fsUserId = followUser.getFsUserId();
            } else if (appScopeEnum == AppScopeEnum.MARKETING) {
                fsUserId = outAccountToFsAccountMap.get(followUser.getUserId());
            }
            if(fsUserId != null){
                if(i == 0){
                    objectData.setOwner(fsUserId);
                }else{
                    fsUserToTeamMember.add(fsUserId);
                }
            }
        }
        return fsUserToTeamMember;
    }

    private void addAvatar(Integer ei, String avatar, ObjectData objectData) {
        if(StringUtils.isNotBlank(avatar) && avatar.startsWith("http")){
            try {
                List<Map<String, Object>> headImage = new ArrayList<>(1);
                String path = doUploadTempNFile(eieaConverter.enterpriseIdToAccount(ei), avatar);
                if (StringUtils.isNotBlank(path)) {
                    Map<String, Object> fileMap = new HashMap<>();
                    fileMap.put("ext", "jpg");
                    fileMap.put("path", path);
                    headImage.add(fileMap);
                    objectData.put("avatar", headImage);
                }
            } catch (Exception e) {
                log.warn("upload head exception headImage={},ei={}", avatar, ei, e);
            }
        }
    }

    /**
     * 将externalContactDetailResult中的字段提取到ObjectData中，这里只提取那些在更新阶段需要更新的字段，像头像等不需要更新的字段就不在这里处理。
     */
    private ObjectData extractExternalContactDetailToMergeObjectDataFields(Integer ei, GetExternalContactDetailResult externalContactDetailResult, boolean isFirstAdd, AppScopeEnum appScopeEnum) {
        GetExternalContactDetailResult.ExternalContactDetail externalContactDetail = externalContactDetailResult.getExternalContact();
        ObjectData objectData = new ObjectData();
        objectData.setTenantId(ei);
        objectData.put("name", externalContactDetail.getName());
        // 删除状态不在这里处理，在好友记录那里处理
        objectData.put("add_status", "normal");
        objectData.put("enterprise_name", externalContactDetailResult.getCorpName());
        objectData.put("enterprise_full_name", externalContactDetail.getCorpFullName());
        objectData.put("position", externalContactDetail.getPosition());
        objectData.put("type", externalContactDetail.getType());
        objectData.put("sex", externalContactDetail.transferGender());
        objectData.put("external_user_id", externalContactDetail.getExternalUserId());
        objectData.put("remark_name", externalContactDetailResult.getOwnerRemarkName());
        objectData.put("remark", externalContactDetailResult.getOwnerDescription());
        objectData.put("phone", externalContactDetailResult.getOwnerRemarkMobile());
        objectData.put("wx_union_id", externalContactDetail.getUnionId());
        if (isFirstAdd) {
            objectData.put("wechat_work_create_time", externalContactDetailResult.getOwnerCreateTime());
            if (appScopeEnum == AppScopeEnum.MARKETING) {
                objectData.put("wx_work_adder", qywxManager.getStaffNameByUserId(eieaConverter.enterpriseIdToAccount(ei), externalContactDetailResult.getOwnerUserId()));
            }
            // 处理企微客户的添加来源
            fillAddSource(ei, externalContactDetailResult, objectData);
        }
        return objectData;
    }

    private void fillAddSource(Integer ei, GetExternalContactDetailResult externalContactDetailResult, ObjectData objectData) {
        if (CollectionUtils.isEmpty(externalContactDetailResult.getFollowUserList())) {
            objectData.put("add_source", "employee_add");
            return;
        }
        Optional<GetExternalContactDetailResult.FollowUser> firstFollowUser = externalContactDetailResult.getFollowUserList().stream().
                filter(e -> e.getAddWay() != null).min((o1, o2) -> {
            if (o1.getCreateTime() != null && o2.getCreateTime() != null) {
                return o1.getCreateTime() > o2.getCreateTime() ? 1 : 0;
            } else if (o1.getCreateTime() == null) {
                return 0;
            }
            return 1;
        });
        if (firstFollowUser.isPresent()) {
            int addWay = firstFollowUser.get().getAddWay();
            objectData.put("add_source", String.valueOf(addWay));
            // 扫描二维码的 把二维码id放进去
            if (addWay == 1 && StringUtils.isNotBlank(firstFollowUser.get().getState()) && externalContactDetailResult.getOwnerAddState() != null) {
                QywxAddFanQrCodeEntity entity = qywxContactManager.getQywxQrCodeEntityByState(eieaConverter.enterpriseIdToAccount(ei), externalContactDetailResult.getOwnerAddState());
                if (entity != null) {
                    objectData.put("add_qr_code_name", entity.getQrCodeName());
                    objectData.put("add_qr_code_id", entity.getId());
                }
            }
        } else {
            objectData.put("add_source", "employee_add");
        }
    }

    private String doUploadTempNFile(String enterpriseAccount, String filePath) {
        byte[] data = httpManager.getBytesByUrl(filePath);
        if (data == null) {
            return null;
        }
        log.debug("uploadTempNFile filePath={}, fileSize={}", filePath, data.length);
        NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
        arg.setBusiness("fs-marketing-provider");
        arg.setData(data);
        arg.setEa(enterpriseAccount);
        arg.setNeedThumbnail(true);
        arg.setSourceUser("E.100000");
        NTempFileUpload.Result result = nFileStorageService.nTempFileUpload(arg, enterpriseAccount);
        return result.getTempFileName();
    }

    private GetExternalContactDetailResult doGetExternalContactDetailFromWechat(String ea, String accessToken, String externalUserId) {
        List<GetExternalContactDetailResult> getExternalContactDetailResults = qywxManager.getExternalContactDetail(ea, accessToken, Collections.singletonList(externalUserId));
        if (getExternalContactDetailResults == null || getExternalContactDetailResults.isEmpty()){
            return null;
        }
        return getExternalContactDetailResults.get(getExternalContactDetailResults.size() - 1);
    }

    private Map<String, Integer> doChangeOutAccountToFsAccount(Integer ei, List<GetExternalContactDetailResult.FollowUser> followUserList) {
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        if(followUserList == null || followUserList.isEmpty() || StringUtils.isBlank(ea)){
            return new HashMap<>(0);
        }
        List<String> wxWorkEmployeeIds = followUserList.stream().map(GetExternalContactDetailResult.FollowUser::getUserId).collect(Collectors.toList());
        Map<String, Integer> resultMap = qywxUserManager.getFsUserIdByQyWxInfo(ea, wxWorkEmployeeIds, true, false);
        if(MapUtils.isEmpty(resultMap)){
            log.warn("Error at change account, result:{}", resultMap);
            return new HashMap<>(0);
        }
        resultMap = Maps.filterValues(resultMap, data -> !QywxUserConstants.isVirtualUserId(data));
        return resultMap;
    }

    public Result<ControllerGetDescribeResult> getWechatWorkObjectDataById(Integer tenantId, String objectId, MetadataTagManager metadataTagManager) {
        ControllerDetailArg wechatWorkExternalUserIdObjectArg = new ControllerDetailArg();
        wechatWorkExternalUserIdObjectArg.setObjectDescribeApiName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        wechatWorkExternalUserIdObjectArg.setObjectDataId(objectId);
        return metadataControllerService.detail(new HeaderObj(tenantId, -10000), CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), wechatWorkExternalUserIdObjectArg);
    }

    public ObjectData getExternalUserObjDetail(Integer ei,String externalUserId) {
        HeaderObj searchHeader = new HeaderObj(ei, -10000);
        Filter filter = new Filter();
        filter.setFieldName("external_user_id");
        filter.setOperator(Filter.OperatorContants.IN);
        filter.setFieldValues(ImmutableList.of(externalUserId));
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(100);
        searchQuery.setFilters(Collections.singletonList(filter));
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setIncludeDescribe(false);
        controllerListArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> pageResult = metadataControllerService.list(searchHeader, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), controllerListArg);
        if (pageResult.isSuccess() && pageResult.getData() != null) {
            List<ObjectData> dataList = pageResult.getData().getDataList();
            if (CollectionUtils.isNotEmpty(dataList)) {
                return dataList.get(0);
            }
        }
        return null;
    }

    public void updateUnionIdExternalUserObj(String finalEa, String externalUserId,String unionId) {
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> pageResult = this.listObjectDataByIdsLimited(eieaConverter.enterpriseAccountToId(finalEa), ImmutableList.of(externalUserId));
        if (pageResult.isSuccess() && pageResult.getData() != null && CollectionUtils.isNotEmpty(pageResult.getData().getDataList())) {
            ObjectData objectData = pageResult.getData().getDataList().get(0);
            String id = objectData.getId();
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("_id",id);
            dataMap.put(CrmWechatWorkExternalUserFieldEnum.WX_UNION_ID.getFieldName(),unionId);
            crmV2Manager.editWechatExternalUserObj(finalEa,dataMap,-10000);
        }
    }

    public void syncUserAccountRelationWxData(String ea) {
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        filterArg.setSelectFields(Lists.newArrayList("_id"));
        PaasQueryArg query = new PaasQueryArg(0,1000);
        filterArg.setQuery(query);
        filterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        InnerPage<ObjectData> innerPage = crmV2Manager.listCrmObjectByFilterV3(ea, -10000, filterArg, 10000);
        if (innerPage != null && CollectionUtils.isNotEmpty(innerPage.getDataList())) {
            List<List<ObjectData>> objectDataPartition = Lists.partition(innerPage.getDataList(), 500);
            objectDataPartition.forEach( dataPartition -> {
                ThreadPoolUtils.execute(() ->{
                    dataPartition.forEach(objectData -> {
                        userMarketingAccountService.getUserMarketingAccountByCrmExternalUserId(ea, -10000, objectData.getId());
                    });
                }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            });
        }
    }

    public Integer queryFsUserIdByRoles(String ea) {
        Integer defaultFsUserId = -10000;
        Integer wechatWorkOperationEmployeeId = userRoleManager.getActivityEmployeeIdByRole(ea, RoleConstant.WECHAT_WORK_OPERATION);
        if (wechatWorkOperationEmployeeId != null) {
            return wechatWorkOperationEmployeeId;
        }
        Integer superAdministratorEmployeeId = userRoleManager.getActivityEmployeeIdByRole(ea, RoleConstant.SYSTEM_ADMIN_ROLE_ID);
        if (superAdministratorEmployeeId != null) {
            return superAdministratorEmployeeId;
        }

        return defaultFsUserId;
    }

    public Integer queryFsUserIdByRoles2(String ea) {
        Integer defaultFsUserId = -10000;
        List<UserRoleEntity> userRoleEntities = userRoleDao.listByEa(ea);
        if (CollectionUtils.isNotEmpty(userRoleEntities)) {
            for (UserRoleEntity userRole : userRoleEntities) {
                if ("employee-spread-market-planning".equals(userRole.getRoleId())) {
                    return userRole.getEmployeeId();
                }
            }
            for (UserRoleEntity userRole : userRoleEntities) {
                if ("super-administrator".equals(userRole.getRoleId())) {
                    return userRole.getEmployeeId();
                }
            }
        }
        return defaultFsUserId;
    }

    public Integer queryFsUserIdByRoles(String ea, String qyUserId) {
        Integer fsUserId = qywxUserManager.getFsUserIdByQyWxInfo(ea, qyUserId, true, true);
        return QywxUserConstants.isFsUserId(fsUserId) ? fsUserId : this.queryFsUserIdByRoles(ea);
    }

    // 刷取企微客户的添加来源, 客户有要求在刷
    public void refreshAddSource(String ea) {
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            return ;
        }

        String accessToken = qywxManager.getAccessToken(ea);
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1000);
        queryFilterArg.setQuery(paasQueryArg);
        String sourceFieldName = "add_source";
        String externalUserFieldName = "external_user_id";
        List<String> selectFields = Lists.newArrayList("_id", externalUserFieldName, sourceFieldName);
        queryFilterArg.setSelectFields(selectFields);
        int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
        if (totalCount <= 0) {
            log.info("wechat group initTeamMember totalCount is zero, ea: {}", ea);
            return;
        }

        int pageSize = 500;
        int totalPage = totalCount % pageSize == 0 ? totalCount / pageSize : totalCount / pageSize + 1;
        int ei = eieaConverter.enterpriseAccountToId(ea);

        HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
        String qywxUserIdFieldName = "qywx_user_id";
        String addFriendRecordSourceFieldName = "resource";
        for(int i = 1; i <= totalPage; i++) {
            InnerPage<ObjectData> externalUserObjPage = crmV2Manager.listCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg, i, pageSize);
            if (externalUserObjPage == null || CollectionUtils.isEmpty(externalUserObjPage.getDataList())) {
                break;
            }

            PaasQueryFilterArg addFriendRecordQueryFilterArg = new PaasQueryFilterArg();
            List<String> externalUserObjIdList = externalUserObjPage.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
            addFriendRecordQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
            PaasQueryArg addFriendPaasQueryArg = new PaasQueryArg(0, externalUserObjIdList.size());
            addFriendPaasQueryArg.addFilter(externalUserFieldName, PaasAndCrmOperatorEnum.IN.getCrmOperator(), externalUserObjIdList);
            addFriendRecordQueryFilterArg.setQuery(addFriendPaasQueryArg);
            List<String> addFriendSelectFields = Lists.newArrayList("_id", qywxUserIdFieldName, externalUserFieldName, addFriendRecordSourceFieldName);
            addFriendRecordQueryFilterArg.setSelectFields(addFriendSelectFields);

            int addFriendTotalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, addFriendRecordQueryFilterArg);
            InnerPage<ObjectData> addFriendRecordPage = crmV2Manager.listCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, addFriendRecordQueryFilterArg, 0, addFriendTotalCount);


            Map<String, List<ObjectData>> externalUserObjIdToAddFriendRecordMap = Maps.newHashMap();
            if (addFriendRecordPage != null && CollectionUtils.isNotEmpty(addFriendRecordPage.getDataList())) {
                for (ObjectData objectData : addFriendRecordPage.getDataList()) {
                    Object externalObjId = objectData.get(externalUserFieldName);
                    if (externalObjId != null) {
                        List<ObjectData> objectDataList = externalUserObjIdToAddFriendRecordMap.computeIfAbsent(externalObjId.toString(), k -> new ArrayList<>());
                        objectDataList.add(objectData);
                    }
                }
            }


            Map<String, ObjectData> externalUsrIdToObjMap = externalUserObjPage.getDataList().stream()
                    .filter(e -> e.get(externalUserFieldName) != null).collect(Collectors.toMap(e -> e.getString(externalUserFieldName), e -> e, (v1, v2) -> v1));

            List<GetExternalContactDetailResult> externalContactDetailResultList = qywxManager.getExternalContactDetail(ea, accessToken, Lists.newArrayList(externalUsrIdToObjMap.keySet()));
            for (GetExternalContactDetailResult getExternalContactDetailResult : externalContactDetailResultList) {
                try {
                    String externalUserId = getExternalContactDetailResult.getExternalContact().getExternalUserId();
                    ObjectData originExternalUserObj = externalUsrIdToObjMap.get(externalUserId);
                    if (originExternalUserObj != null) {
                        ObjectData objectData = new ObjectData();
                        objectData.put("_id", originExternalUserObj.getId());
                        fillAddSource(ei, getExternalContactDetailResult, objectData);
                        ActionEditArg actionEditArg = new ActionEditArg();
                        actionEditArg.setObjectData(objectData);
                        Result<ActionEditResult> editResult = metadataActionService.edit(systemHeader, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), true, true, actionEditArg);
                        log.info("更新企微客户来源, result: {} ea: {} id: {} oldSource: {} newSource: {}", editResult, ea, originExternalUserObj.getId(), originExternalUserObj.get(sourceFieldName), objectData.get(sourceFieldName));

                        List<ObjectData> addFriendList = externalUserObjIdToAddFriendRecordMap.getOrDefault(originExternalUserObj.getId(), Lists.newArrayList());
                        for (ObjectData addFriendRecord : addFriendList) {
                            Object qywxUserIdObj = addFriendRecord.get(qywxUserIdFieldName);
                            if (qywxUserIdObj != null && CollectionUtils.isNotEmpty(getExternalContactDetailResult.getFollowUserList())) {
                                Optional<GetExternalContactDetailResult.FollowUser> followUser  = getExternalContactDetailResult.getFollowUserList().stream()
                                        .filter(e -> qywxUserIdObj.toString().equals(e.getUserId())).findFirst();
                                followUser.ifPresent(e -> {
                                    ObjectData addFriendRecordForUpdate = new ObjectData();
                                    addFriendRecordForUpdate.put("_id", addFriendRecord.getId());
                                    addFriendRecordForUpdate.put("resource", e.getAddWay() == null ? "other" : String.valueOf(e.getAddWay()));
                                    if (1 == e.getAddWay()) {
                                        addFriendRecordForUpdate.put("qr_code_id", objectData.get("add_qr_code_id"));
                                        addFriendRecordForUpdate.put("qr_code_name", objectData.get("add_qr_code_name"));
                                    }

                                    ActionEditArg addFriendActionEditArg = new ActionEditArg();
                                    addFriendActionEditArg.setObjectData(addFriendRecordForUpdate);
                                    Result<ActionEditResult> addFriendEditResult = metadataActionService.edit(systemHeader, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), true, true, addFriendActionEditArg);
                                    log.info("更新企微客户好友记录来源, result: {} ea: {} id: {} oldSource: {} newSource: {}", addFriendEditResult, ea, addFriendRecord.getId(),
                                            addFriendRecord.get(addFriendRecordSourceFieldName), addFriendRecordForUpdate.get(addFriendRecordSourceFieldName));
                                });
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("刷取微信客户来源失败，data: {}", getExternalContactDetailResult, e);
                }
            }
        }
    }

    public void handleQywxDeleteEvent(String ea, String externalUserId, String qywxUserId, String changeType, String appId, AppScopeEnum appScopeEnum, String qywxCorpId) {
        String lockKey = String.format("delete_external_user_%s_%s_%s", ea, externalUserId, qywxUserId);
        String lockValue = appScopeEnum.getValue() + "-" + RandomStringUtils.randomAscii(10);
        try {
            boolean lockSuccess = redisManager.lock(lockKey, lockValue, 60);
            log.info("企微删除事件， ea: {} externalUserId: {} qywxUserId: {} changeType: {} appScope: {} lock: {}", ea, externalUserId, qywxUserId, changeType, appScopeEnum, lockSuccess);
            int ei = eieaConverter.enterpriseAccountToId(ea);
            if(!lockSuccess) {
                // 如果获取不到锁 并且获取到锁的应用不是本次应用的通知 延迟消费一分钟
                String existLockValue = redisManager.get(lockKey);
                if (StringUtils.isNotBlank(existLockValue) && !existLockValue.contains(appScopeEnum.getValue())) {
                    QywxEventDelayMqArg qywxEventDelayMqArg = new QywxEventDelayMqArg();
                    qywxEventDelayMqArg.setEi(ei);
                    qywxEventDelayMqArg.setExternalUserId(externalUserId);
                    qywxEventDelayMqArg.setQywxOriginChangeType(changeType);
                    qywxEventDelayMqArg.setQywxUserId(qywxUserId);
                    qywxEventDelayMqArg.setEvent(QywxEventDelayMqArg.DELETE_EVENT);
                    qywxEventDelayMqArg.setChangeObjectType(QywxEventDelayMqArg.EXTERNAL_OBJECT_TYPE);
                    qywxEventDelayMqArg.setAppScopeEnum(appScopeEnum);
                    qywxEventDelayMqArg.setAppId(appId);
                    qywxEventDelayMqArg.setQywxCorpId(qywxCorpId);
                    delayQueueSender.sendByObj(eieaConverter.enterpriseIdToAccount(ei), qywxEventDelayMqArg, DelayQueueTagConstants.QYWX_CHANGE_EVENT, RocketMqDelayLevelConstants.TEN_SECOND);
                }
                return ;
            }
            Result<Page<ObjectData>> pageResult = listObjectDataByIdsLimited(ei, Lists.newArrayList(externalUserId));
            if (pageResult == null || !pageResult.isSuccess() || pageResult.getData() == null || CollectionUtils.isEmpty(pageResult.getData().getDataList())) {
                log.warn("找不到外部联系人，ea: {} externalUserId: {}", ea, externalUserId);
                return;
            }
            ObjectData externalUserObject = pageResult.getData().getDataList().get(0);
            List<ObjectData> wechatFriendsRecordObjList = wechatFriendsRecordObjDescribeManager.queryWechatFriendsRecordObj(ea, qywxUserId, externalUserObject.getId(), null);
            if (CollectionUtils.isNotEmpty(wechatFriendsRecordObjList)) {
                ObjectData wechatFriendsRecordObj = wechatFriendsRecordObjList.get(0);
                Map<String, Object> updateWechatFrindRecordMap = Maps.newHashMap();
                updateWechatFrindRecordMap.put("_id", wechatFriendsRecordObj.getId());
                Object appScopeObj = wechatFriendsRecordObj.get(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD);
                List<String> appScopeList = appScopeObj != null ? (List<String>) appScopeObj : Lists.newArrayList();
                if (!appScopeList.contains(appScopeEnum.getValue()) && wechatFriendsRecordObj.get("friend_status") != null && !"0".equals(wechatFriendsRecordObj.get("friend_status").toString())) {
                    // 如果之前就已经被一方删除了，这时候直接是互删
                    updateWechatFrindRecordMap.put("friend_status", "3");
                } else if ("del_external_contact".equals(changeType)) {
                    updateWechatFrindRecordMap.put("delete_time", System.currentTimeMillis());
                    updateWechatFrindRecordMap.put("friend_status", "1");
                } else if ("del_follow_user".equals(changeType)) {
                    updateWechatFrindRecordMap.put("delete_time", System.currentTimeMillis());
                    updateWechatFrindRecordMap.put("friend_status", "2");
                }
                if (CollectionUtils.isNotEmpty(appScopeList) && appScopeList.remove(appScopeEnum.getValue())) {
                    updateWechatFrindRecordMap.put(APP_SCOPE_FIELD, appScopeList);
                }
                GuavaLimiter.acquire(QYWX_INIT_RATE_LIMIT_KEY, ea);
                Result<ActionEditResult> wechatFriendUpdateResult = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), updateWechatFrindRecordMap);
                log.info("修改好友记录， ea: {} externalUserId: {} qywxUserId: {} updateData: {} result: {}", ea, externalUserId, qywxUserId, updateWechatFrindRecordMap, wechatFriendUpdateResult);
            }
            updateAddStatusAndRemoveAppScopeByFriendRecord(ea, externalUserId);
            // 删除相关团队
            GetTeamMemberArg getTeamMemberArg = new GetTeamMemberArg();
            getTeamMemberArg.setObjectDescribeApiName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
            getTeamMemberArg.setDataID(externalUserObject.getId());
            HeaderObj systemHeader = new HeaderObj(ei, -10000);
            Result<GetTeamMemberResult> getTeamMemberResult = metadataControllerService.getTeamMember(systemHeader, getTeamMemberArg);
            log.info("handleQywxDeleteEvent ea: {} objectId: {} getTeamMemberResult: {}", ea, externalUserObject.getId(), getTeamMemberResult);
            if (getTeamMemberResult.isSuccess() && getTeamMemberResult.getData() != null) {
                Set<Integer> crmExistedTeamMemberSet = getTeamMemberResult.getData().getAllTeamMember();
                Map<String, Integer> qywxUserIdToFsUserIdMap = Maps.newHashMap();
                if (appScopeEnum == AppScopeEnum.MARKETING) {
                    Map<String, Integer> result = qywxUserManager.getFsUserIdByQyWxInfo(ea, Lists.newArrayList(qywxUserId), false, false);
                    qywxUserIdToFsUserIdMap.putAll(result);
                } else {
                    Map<String, Integer> result = qyweixinAccountBindManager.batchGetOutEmployeeBindFsUser(ea, qywxCorpId, appId, Lists.newArrayList(qywxUserId));
                    qywxUserIdToFsUserIdMap.putAll(result);
                }
                Integer fsUserId = qywxUserIdToFsUserIdMap.get(qywxUserId);
                if (fsUserId != null && crmExistedTeamMemberSet.contains(fsUserId)) {
                    crmV2Manager.doRemoveTeamMemberToCrm(systemHeader, Lists.newArrayList(fsUserId), CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), externalUserObject.getId());
                }
            }
        } catch (Exception e) {
            log.error("handleQywxDeleteEvent ea: {} externalUserId: {} qywxUserId: {} changeType: {}", ea, externalUserId, qywxUserId, changeType, e);
        } finally {
            redisManager.unLock(lockKey, lockValue);
        }
    }

    // 处理 crm 的企微增量数据
    public void handleScrmExternalUserChangeEvent(ExternalContactEvent externalContactEvent) {
        String userId = externalContactEvent.getUserId();
        // 目前只有分享云做这个判断，平台那边不能全网
        if (appVersionManager.isFxCloud() && (MapUtils.isEmpty(externalContactEvent.getUserIdMap()) || externalContactEvent.getUserIdMap().get(userId) == null)) {
            log.info("trigger qywx employee is not bind to fs employee，msg: {}", externalContactEvent);
            return;
        }
        String ea = externalContactEvent.getFsEa();
        GetExternalContactDetailResult getExternalContactDetailResult = JsonUtil.fromJson(externalContactEvent.getExternalContactDetail(), GetExternalContactDetailResult.class);
        List<GetExternalContactDetailResult.FollowUser> followUserList = getExternalContactDetailResult.getFollowUserList();
        if (CollectionUtils.isEmpty(followUserList)) {
            return ;
        }
        List<String> qywxUserIdList = followUserList.stream().map(GetExternalContactDetailResult.FollowUser::getUserId).collect(Collectors.toList());
        Map<String,Integer> result = qyweixinAccountBindManager.batchGetOutEmployeeBindFsUser(externalContactEvent.getFsEa(), externalContactEvent.getCorpId(), qywxCrmAppId, qywxUserIdList);
        followUserList.forEach(e -> e.setFsUserId(result.get(e.getUserId())));
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        String externalUserId = externalContactEvent.getExternalUserId();
        Result<Page<ObjectData>> searchResult = listObjectDataByIdsLimited(ei, Lists.newArrayList(externalUserId));
        if (searchResult != null && searchResult.getData() != null && CollectionUtils.isNotEmpty(searchResult.getData().getDataList())) {
            ObjectData existObjectData = searchResult.getData().getDataList().get(0);
            doUpdateWxWorkExternalUserToCrm(ei, AppScopeEnum.CRM, existObjectData, getExternalContactDetailResult, null);
        } else {
            doAddWxWorkExternalUserToCrm(ei, externalUserId, AppScopeEnum.CRM, getExternalContactDetailResult, null);
        }
    }

    public void handleScrmDeleteExternalUserEvent(ExternalContactEvent externalContactEvent) {
        String userId = externalContactEvent.getUserId();
        // 目前只有分享云做这个判断，平台那边不能全网
        if (appVersionManager.isFxCloud() && (MapUtils.isEmpty(externalContactEvent.getUserIdMap()) || externalContactEvent.getUserIdMap().get(userId) == null)) {
            log.info("trigger qywx employee is not bind to fs employee，msg: {}", externalContactEvent);
            return;
        }
        String ea = externalContactEvent.getFsEa();
        String externalUserId = externalContactEvent.getExternalUserId();
        String qywxUserId = externalContactEvent.getUserId();
        handleQywxDeleteEvent(ea, externalUserId, qywxUserId, externalContactEvent.getChangeType(), externalContactEvent.getAppId(), AppScopeEnum.CRM, externalContactEvent.getCorpId());
    }
    // 根据好友记录来更新addStatus字段和移除应用范围
    public void updateAddStatusAndRemoveAppScopeByFriendRecord(String ea, String externalUserId) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        Result<Page<ObjectData>> pageResult = listObjectDataByIdsLimited(ei, Lists.newArrayList(externalUserId));
        if (pageResult == null || !pageResult.isSuccess() || pageResult.getData() == null || CollectionUtils.isEmpty(pageResult.getData().getDataList())) {
            log.warn("找不到外部联系人，event: {}", externalUserId);
            return;
        }
        ObjectData externalUserObject = pageResult.getData().getDataList().get(0);

        PaasQueryArg query = new PaasQueryArg(0, 1);
        query.addFilter("external_user_id", OperatorConstants.EQ, Lists.newArrayList(externalUserObject.getId()));
        List<String> selectFieldList = Lists.newArrayList("_id", "external_user_id", "qywx_user_id", "friend_status", APP_SCOPE_FIELD);
        List<ObjectData> wechatFriendRecordObjectList =  crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), selectFieldList, query);
        if (CollectionUtils.isEmpty(wechatFriendRecordObjectList)) {
            Result<ActionEditResult> updatResult = updateAddStatusToDeleted(ea, externalUserObject.getId());
            log.info("好友记录都不存在，将外部联系人对象标记为删除， ea: {} externalUserId: {} result: {}", ea, externalUserId, updatResult);
            return;
        }
        // 状态为正常的好友记录
        List<ObjectData> normalStatusFriendRecordList = wechatFriendRecordObjectList.stream().filter(e -> e.get("friend_status") != null && "0".equals(e.get("friend_status").toString())).collect(Collectors.toList());
        // 该企微客户最终的应用范围
        Set<String> finalAppScopeSet = Sets.newHashSet();
        Map<String, Object> updateMap = Maps.newHashMap();
        updateMap.put("_id", externalUserObject.getId());
        if (CollectionUtils.isEmpty(normalStatusFriendRecordList)) {
            updateMap.put("add_status", "deleted");
            updateMap.put("wechat_work_delete_time", System.currentTimeMillis());
        } else {
            updateMap.put("add_status", "normal");
            updateMap.put("wechat_work_delete_time", "");
            normalStatusFriendRecordList.stream().map(e -> e.get(APP_SCOPE_FIELD)).filter(Objects::nonNull).map(e -> (List<String>) e).forEach(finalAppScopeSet::addAll);
        }
        updateMap.put(APP_SCOPE_FIELD, Lists.newArrayList(finalAppScopeSet));
        Result<ActionEditResult> result = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), updateMap);
        log.info("外部联系人更新addStatus和应用范围， ea: {} externalUserId: {} updateMap: {} result: {}", ea, externalUserId, updateMap, result);
    }

    public Result<ActionEditResult> updateAddStatusToDeleted(String ea, String id) {
        Map<String, Object> updateMap = Maps.newHashMap();
        updateMap.put("_id", id);
        updateMap.put("add_status", "deleted");
        updateMap.put("wechat_work_delete_time", System.currentTimeMillis());
        return crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), updateMap);
    }

    // 处理 crm 的企微存量数据
    public void handleScrmStockExternalUserData(String ea, List<CrmQywxDataSyncMessageHandler.QywxExternalContact> externalContactInfo) {
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(externalContactInfo)) {
            log.warn("handleCrmStockExternalUserData data is empty , ea:{} data: {}", ea, externalContactInfo);
            return;
        }
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        List<String> externalUserIdList = externalContactInfo.stream().map(CrmQywxDataSyncMessageHandler.QywxExternalContact::getExternalContact).map(CrmQywxDataSyncMessageHandler.ExternalContact::getExternalUserId).distinct().collect(Collectors.toList());
        Result<Page<ObjectData>> searchResult = listObjectDataByIdsLimited(ei, externalUserIdList);
        Map<String, ObjectData> existObjectDataMap = Maps.newHashMap();
        if (searchResult != null && searchResult.getData() != null && CollectionUtils.isNotEmpty(searchResult.getData().getDataList())) {
            searchResult.getData().getDataList().forEach(e -> existObjectDataMap.put(String.valueOf(e.get("external_user_id")), e));
        }
        for (CrmQywxDataSyncMessageHandler.QywxExternalContact externalContact : externalContactInfo) {
            String externalUserId = externalContact.getExternalContact().getExternalUserId();
            GetExternalContactDetailResult getExternalContactDetailResult = transferScrmExternalEventToMarketingEntity(externalContact);
            ObjectData existObjectData = existObjectDataMap.get(externalUserId);
            if (existObjectData != null) {
                doUpdateWxWorkExternalUserToCrm(ei, AppScopeEnum.CRM, existObjectData, getExternalContactDetailResult, null);
            } else {
                doAddWxWorkExternalUserToCrm(ei, externalUserId, AppScopeEnum.CRM, getExternalContactDetailResult, null);
            }
        }
    }


    public GetExternalContactDetailResult transferScrmExternalEventToMarketingEntity(CrmQywxDataSyncMessageHandler.QywxExternalContact crmExternalContact) {
        GetExternalContactDetailResult result = new GetExternalContactDetailResult();
        result.setErrCode(0);
        result.setErrMsg("success");
        result.setExternalContact(transferScrmExternalContactToMarketingEntity(crmExternalContact));
        List<GetExternalContactDetailResult.FollowUser> followUserList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(crmExternalContact.getFollowUserList())) {
            crmExternalContact.getFollowUserList().forEach(e -> followUserList.add(transferScrmQywxEventToMarketingEntity(e)));
        }
        result.setFollowUserList(followUserList);
        return result;
    }

    public GetExternalContactDetailResult.ExternalContactDetail transferScrmExternalContactToMarketingEntity(CrmQywxDataSyncMessageHandler.QywxExternalContact crmExternalContact) {
        GetExternalContactDetailResult.ExternalContactDetail result = new GetExternalContactDetailResult.ExternalContactDetail();
        result.setExternalUserId(crmExternalContact.getExternalContact().getExternalUserId());
        result.setName(crmExternalContact.getExternalContact().getName());
        result.setType(crmExternalContact.getExternalContact().getType());
        result.setGender(crmExternalContact.getExternalContact().getGender());
        result.setUnionId(crmExternalContact.getExternalContact().getUnionId());
        result.setPosition(crmExternalContact.getExternalContact().getPosition());
        result.setCorpName(crmExternalContact.getExternalContact().getCorpName());
        result.setCorpFullName(crmExternalContact.getExternalContact().getCorpFullName());
        result.setAvatar(crmExternalContact.getExternalContact().getAvatar());
        return result;
    }

    public GetExternalContactDetailResult.FollowUser transferScrmQywxEventToMarketingEntity(CrmQywxDataSyncMessageHandler.FollowUser crmFollower) {
        GetExternalContactDetailResult.FollowUser followUser = new GetExternalContactDetailResult.FollowUser();
        followUser.setFsUserId(StringUtils.isBlank(crmFollower.getFxUserId()) ? null : Integer.parseInt(crmFollower.getFxUserId()));
        followUser.setUserId(crmFollower.getUserId());
        followUser.setRemark(crmFollower.getRemark());
        followUser.setRemarkMobiles(crmFollower.getRemarkMobiles());
        followUser.setRemarkCorpName(crmFollower.getRemarkCorpName());
        followUser.setDescription(crmFollower.getDescription());
        followUser.setCreateTime(crmFollower.getCreateTime());
        followUser.setAddWay(crmFollower.getAddWay());
        followUser.setState(null);
        followUser.setTags(null);
        return followUser;
    }

    public static boolean fillAppScope(ObjectData objectData, AppScopeEnum appScopeEnum) {
        Object appScopeObject = objectData.get(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD);
        List<String> appScopeValueList;
        if (appScopeObject == null) {
            appScopeValueList =  Lists.newArrayList();
        } else {
            appScopeValueList = (List<String>) appScopeObject;
        }
        if (!appScopeValueList.contains(appScopeEnum.getValue())) {
            appScopeValueList.add(appScopeEnum.getValue());
            objectData.put(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, appScopeValueList);
            return true;
        }
        return false;
    }

    // 补偿数据 只用一次 记得删掉
    public void fixAppScopeData(String ea) {
        fixExternalUserAppScopeData(ea);
        fixWechatGroupAppScopeData(ea);
    }
    // 补偿数据 只用一次 记得删掉
    public void fixExternalUserAppScopeData(String ea) {
        try {
            long beginTime = 1695402000000L;
            long endTime = 1697212800000L;
            // 查询该应用的所有好友记录
            String createTimeField = "create_time";
            PaasQueryArg query = new PaasQueryArg(0, 1);
            query.addFilter(createTimeField, PaasAndCrmOperatorEnum.GTE.getCrmOperator(), Lists.newArrayList(String.valueOf(beginTime)));
            query.addFilter(createTimeField, PaasAndCrmOperatorEnum.LTE.getCrmOperator(), Lists.newArrayList(String.valueOf(endTime)));
            query.addFilter("add_status", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList("normal"));
            query.addFilter(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, OperatorConstants.NIN, Lists.newArrayList(AppScopeEnum.MARKETING.getValue()));
            List<String> selectFieldList = Lists.newArrayList("_id", "name", "external_user_id",WechatWorkExternalUserObjManager.APP_SCOPE_FIELD);
            List<ObjectData> externalDataList =  crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), selectFieldList, query);
            if (CollectionUtils.isEmpty(externalDataList)) {
                log.info("fixAppScopeData data is empty ea: {}", ea);
                return;
            }
            log.info("fixAppScopeData data ea: {} size: {}", ea, externalDataList.size());
            for (ObjectData objectData : externalDataList) {
                Object appScopeObj = objectData.get(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD);
                log.info("fixAppScopeData data ea: {} data: {}", ea, objectData);
                List<String> appScopeList;
                if (appScopeObj == null) {
                    log.info("fixAppScopeData data ea: {} appScope is null: {}", ea, objectData);
                    appScopeList = Lists.newArrayList();
                } else {
                    appScopeList = (List<String>) appScopeObj;
                }
                if (!appScopeList.contains(AppScopeEnum.MARKETING.getValue())) {
                    appScopeList.add(AppScopeEnum.MARKETING.getValue());
                    Map<String, Object> dataMap = Maps.newHashMap();
                    dataMap.put("_id", objectData.getId());
                    dataMap.put(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, appScopeList);
                    Result<ActionEditResult> result = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), dataMap);
                    log.info("fixAppScopeData edit external user ea: {} data: {} result: {}", ea, objectData, result);
                }
                String externalUserId = objectData.getString("external_user_id");
                if (StringUtils.isBlank(externalUserId)) {
                    log.info("fixAppScopeData data ea: {} external_user_id is null: {}", ea, objectData);
                    continue;
                }
                List<ObjectData> friendRecordList = wechatFriendsRecordObjDescribeManager.queryWechatFriendsRecordObj(ea, null, objectData.getId(), 0);
                if (CollectionUtils.isEmpty(friendRecordList)) {
                    log.info("fixAppScopeData data ea: {} friendRecordList is null,externalUserId: {}", ea, externalUserId);
                    continue;
                }
                for (ObjectData friendData : friendRecordList) {
                    Object friendAppScopeObj = friendData.get(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD);
                    log.info("fixAppScopeData friendRecord data ea: {} data: {}", ea, friendData);
                    List<String> friendAppScopeList;
                    if (friendAppScopeObj == null) {
                        log.info("fixAppScopeData friendRecord data ea: {} appScope is null: {}", ea, objectData);
                        friendAppScopeList = Lists.newArrayList();
                    } else {
                        friendAppScopeList = (List<String>) friendAppScopeObj;
                    }
                    if (!friendAppScopeList.contains(AppScopeEnum.MARKETING.getValue())) {
                        friendAppScopeList.add(AppScopeEnum.MARKETING.getValue());
                        Map<String, Object> dataMap = Maps.newHashMap();
                        dataMap.put("_id", friendData.getId());
                        dataMap.put(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, friendAppScopeList);
                        Result<ActionEditResult> result = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), dataMap);
                        log.info("fixAppScopeData edit friend record ea: {} data: {} result: {}", ea, friendData, result);
                    }
                }
            }

        } catch (Exception e) {
            log.error("fixAppScopeData fixExternalUserAppScopeData error, ea: {}", ea, e);
        }
    }

    public void fixWechatGroupAppScopeData(String ea) {
        try {
            long beginTime = 1695402000000L;
            long endTime = 1697212800000L;
            String createTimeField = "create_time";
            PaasQueryArg query = new PaasQueryArg(0, 1);
            query.addFilter(createTimeField, PaasAndCrmOperatorEnum.GTE.getCrmOperator(), Lists.newArrayList(String.valueOf(beginTime)));
            query.addFilter(createTimeField, PaasAndCrmOperatorEnum.LTE.getCrmOperator(), Lists.newArrayList(String.valueOf(endTime)));
            query.addFilter("status", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList("0"));
            query.addFilter(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, OperatorConstants.NIN, Lists.newArrayList(AppScopeEnum.MARKETING.getValue()));
            List<String> selectFieldList = Lists.newArrayList("_id", "name", "chat_id",WechatWorkExternalUserObjManager.APP_SCOPE_FIELD);
            List<ObjectData> wechatGroupDataList =  crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), selectFieldList, query);
            if (CollectionUtils.isEmpty(wechatGroupDataList)) {
                log.info("fixAppScopeData wechatGroupDataList is empty ea: {}", ea);
                return;
            }
            for (ObjectData wechatGroupData : wechatGroupDataList) {
                Object appScopeObj = wechatGroupData.get(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD);
                log.info("fixAppScopeData wechatGroupData ea: {} data: {}", ea, wechatGroupData);
                List<String> appScopeList;
                if (appScopeObj == null) {
                    log.info("fixAppScopeData wechatGroupData ea: {} appScope is null: {}", ea, wechatGroupData);
                    appScopeList = Lists.newArrayList();
                } else {
                    appScopeList = (List<String>) appScopeObj;
                }
                if (!appScopeList.contains(AppScopeEnum.MARKETING.getValue())) {
                    appScopeList.add(AppScopeEnum.MARKETING.getValue());
                    Map<String, Object> dataMap = Maps.newHashMap();
                    dataMap.put("_id", wechatGroupData.getId());
                    dataMap.put(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, appScopeList);
                    Result<ActionEditResult> result = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), dataMap);
                    log.info("fixAppScopeData edit wechatGroupData ea: {} data: {} result: {}", ea, wechatGroupData, result);
                }
                List<ObjectData> groupUserDataList = wechatGroupUserObjDescribeManager.queryGroupUserByGroupId(ea, wechatGroupData.getId(), null);
                if (CollectionUtils.isEmpty(groupUserDataList)) {
                    log.info("fixAppScopeData groupUserDataList is null ea: {} groupId: {}", ea, wechatGroupData.getId());
                    return;
                }
                for (ObjectData groupUserData : groupUserDataList) {
                    String status = groupUserData.getString("status");
                    if (StringUtils.isBlank(status) || !status.equals("0")) {
                        log.info("fixAppScopeData group user status is not normal status: {} data: {}", status, groupUserData);
                        continue;
                    }
                    Object groupUserAppScopeObj = groupUserData.get(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD);
                    log.info("fixAppScopeData wechatGroupUserData ea: {} data: {}", ea, groupUserData);
                    List<String> groupUserAppScopeList;
                    if (groupUserAppScopeObj == null) {
                        log.info("fixAppScopeData wechatGroupUserData ea: {} appScope is null: {}", ea, groupUserData);
                        groupUserAppScopeList = Lists.newArrayList();
                    } else {
                        groupUserAppScopeList = (List<String>) groupUserAppScopeObj;
                    }
                    if (!groupUserAppScopeList.contains(AppScopeEnum.MARKETING.getValue())) {
                        groupUserAppScopeList.add(AppScopeEnum.MARKETING.getValue());
                        Map<String, Object> dataMap = Maps.newHashMap();
                        dataMap.put("_id", groupUserData.getId());
                        dataMap.put(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, groupUserAppScopeList);
                        Result<ActionEditResult> result = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), dataMap);
                        log.info("fixAppScopeData edit wechatGroupUserData ea: {} data: {} result: {}", ea, groupUserData, result);
                    }
                }
            }
        } catch (Exception e) {
            log.error("fixAppScopeData fixWechatGroupAppScopeData ea: {}", ea, e);
        }
    }

    public void fixRepeatExternalUserData(String ea) {
        try {
            long beginTime = 1697210640000L;
            long endTime = System.currentTimeMillis();
            // 查询该应用的所有好友记录
            String createTimeField = "create_time";
            PaasQueryArg query = new PaasQueryArg(0, 1);
            query.addFilter(createTimeField, PaasAndCrmOperatorEnum.GTE.getCrmOperator(), Lists.newArrayList(String.valueOf(beginTime)));
            query.addFilter(createTimeField, PaasAndCrmOperatorEnum.LTE.getCrmOperator(), Lists.newArrayList(String.valueOf(endTime)));
            query.addFilter("add_status", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList("normal"));
            List<String> selectFieldList = Lists.newArrayList("_id", "name", "external_user_id",WechatWorkExternalUserObjManager.APP_SCOPE_FIELD);
            List<ObjectData> externalDataList =  crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), selectFieldList, query);
            if (CollectionUtils.isEmpty(externalDataList)) {
                log.info("fixRepeatExternalUserData data is empty ea: {}", ea);
                return;
            }
            log.info("fixRepeatExternalUserData data ea: {} size: {}", ea, externalDataList.size());
            Map<String, List<ObjectData>> externalUserIsToListMap = externalDataList.stream().filter(e -> e.getString("external_user_id") != null).collect(Collectors.groupingBy(e -> e.getString("external_user_id")));
            log.info("fixRepeatExternalUserData data ea: {} repeat size: {}", ea, externalUserIsToListMap.size());

            externalUserIsToListMap.forEach((externalUserId, objectDataList) -> {
                if (objectDataList.size() > 1) {
                    log.info("fixRepeatExternalUserData data ea: {} externalUserId: {} repeat data: {}", ea, externalUserId, objectDataList);


                    Set<String> finalAppScopeSet = Sets.newHashSet();
                    objectDataList.stream().filter(e -> e.get(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD) != null).map(e -> (List<String>) e.get(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD)).forEach(finalAppScopeSet::addAll);
                    String marketingDataId = null;
                    for (ObjectData objectData : objectDataList) {
                        Object appScopeObj = objectData.get(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD);
                        if (appScopeObj == null) {
                            continue;
                        }
                        List<String> appScopeList = (List<String>) appScopeObj;
                        if (appScopeList.contains(AppScopeEnum.MARKETING.getValue())) {
                            marketingDataId = objectData.getId();
                            break;
                        }
                    }
                    if (marketingDataId == null) {
                        for (ObjectData objectData : objectDataList) {
                            Object appScopeObj = objectData.get(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD);
                            if (appScopeObj == null) {
                                continue;
                            }
                            List<String> appScopeList = (List<String>) appScopeObj;
                            if (appScopeList.contains(AppScopeEnum.CRM.getValue())) {
                                marketingDataId = objectData.getId();
                                break;
                            }
                        }
                    }
                    log.info("fixRepeatExternalUserData ea: {} 重复的属于营销通的id: {} appScope: {}", ea, marketingDataId, finalAppScopeSet);
                    if (marketingDataId != null) {
                        Map<String, Object> dataMap = Maps.newHashMap();
                        dataMap.put("_id", marketingDataId);
                        dataMap.put(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, Lists.newArrayList(finalAppScopeSet));
                        Result<ActionEditResult> result = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), dataMap);
                        log.info("fixRepeatExternalUserData edit external user ea: {} id: {} appScope:{} result: {}", ea, marketingDataId, finalAppScopeSet, result);

                        List<ObjectData> friendRecordList = wechatFriendsRecordObjDescribeManager.queryWechatFriendsRecordObj(ea, null, marketingDataId, 0);
                        if (CollectionUtils.isNotEmpty(friendRecordList)) {
                            for (ObjectData objectData : friendRecordList) {
                                Map<String, Object> friendData = Maps.newHashMap();
                                friendData.put("_id", objectData.getId());
                                friendData.put(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, Lists.newArrayList(finalAppScopeSet));
                                Result<ActionEditResult> result2 = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), friendData);
                                log.info("fixRepeatExternalUserData edit friend record ea: {} data: {} result: {}", ea, friendData, result2);
                            }
                        }

                        for (ObjectData objectData : objectDataList) {
                            String id = objectData.getId();
                            if (marketingDataId.equals(id)) {
                                continue;
                            }
                            List<ObjectData> invalidFriendRecordList = wechatFriendsRecordObjDescribeManager.queryWechatFriendsRecordObj(ea, null, id, 0);
                            if (CollectionUtils.isEmpty(invalidFriendRecordList)) {
                                log.info("fixRepeatExternalUserData 要作废的好友记录为空， ea: {} id: {}", ea, id);
                                continue;
                            }
                            List<String> idList = invalidFriendRecordList.stream().map(ObjectData::getId).collect(Collectors.toList());
                            try {
                                crmV2Manager.bulkInvalid(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), idList);
                                log.info("fixRepeatExternalUserData friend record bulkInvalid ea: {} id: {} success idList: {}", ea, id, idList);
                            } catch (Exception e) {
                                log.info("fixRepeatExternalUserData friend record bulkInvalid ea: {} id: {} error idList: {}", ea, id, idList, e);
                            }

                            try {
                                crmV2Manager.bulkInvalid(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), Lists.newArrayList(id));
                                log.info("fixRepeatExternalUserData external user bulkInvalid ea: {} id: {} success idList: {}", ea, id, idList);
                            } catch (Exception e) {
                                log.info("fixRepeatExternalUserData external user bulkInvalid ea: {} id: {} error idList: {}", ea, id, idList, e);
                            }
                        }
                    }
                }
            });
        } catch (Exception e) {
            log.error("fixRepeatExternalUserData fixExternalUserAppScopeData error, ea: {}", ea, e);
        }
    }
}
