package com.facishare.marketing.provider.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.common.enums.VideoTargetTypeEnum;
import com.facishare.marketing.api.arg.ListArticleArg;
import com.facishare.marketing.api.arg.ListProductArg;
import com.facishare.marketing.api.arg.SearchMaterialVO;
import com.facishare.marketing.api.arg.appMenu.GetShowAppMenuTemplateArg;
import com.facishare.marketing.api.arg.appMenu.MenuDetailArg;
import com.facishare.marketing.api.arg.fileLibrary.ListFileByGroupArg;
import com.facishare.marketing.api.arg.hexagon.SimpleHexagonListArg;
import com.facishare.marketing.api.arg.photoLibrary.ListPhotoByGroupArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.arg.video.ListVideosArg;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.fileLibrary.ListFileByGroupResult;
import com.facishare.marketing.api.result.kis.KisArticleListResult;
import com.facishare.marketing.api.result.kis.ListArticleResult;
import com.facishare.marketing.api.result.photoLibrary.ListPhotoByGroupResult;
import com.facishare.marketing.api.result.qr.QueryQRPosterByEaListUnitResult;
import com.facishare.marketing.api.result.video.QueryVideoResult;
import com.facishare.marketing.api.service.MaterialSearchService;
import com.facishare.marketing.api.service.ProductService;
import com.facishare.marketing.api.service.VideoService;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.api.service.fileLibrary.FileLibraryService;
import com.facishare.marketing.api.service.hexagon.HexagonService;
import com.facishare.marketing.api.service.kis.ArticleService;
import com.facishare.marketing.api.service.open.material.MaterialShowSettingService;
import com.facishare.marketing.api.service.photoLibrary.PhotoLibraryService;
import com.facishare.marketing.api.service.qr.QRPosterService;
import com.facishare.marketing.api.vo.SimpleHexagonVO;
import com.facishare.marketing.api.vo.appMenu.AppMenuDetailVO;
import com.facishare.marketing.api.vo.appMenu.AppMenuTemplateDetailVO;
import com.facishare.marketing.common.enums.ArticleStatusEnum;
import com.facishare.marketing.common.enums.MarketingPluginTypeEnum;
import com.facishare.marketing.common.enums.ProductStatusEnum;
import com.facishare.marketing.common.enums.RequestFromTypeEnum;
import com.facishare.marketing.common.enums.material.MaterialTypeEnum;
import com.facishare.marketing.common.enums.qr.QRPosterTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.threadpool.NamedThreadPool;
import com.facishare.marketing.provider.dao.ServiceKnowledgeScenarySettingDao;
import com.facishare.marketing.provider.entity.ServiceKnowledgeScenarySettingEntity;
import com.facishare.marketing.provider.manager.MarketingPluginConfigManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.ServiceKnowledgeObjManager;
import com.facishare.rest.core.util.JsonUtil;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonArray;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/4/6
 **/
@Slf4j
@Service("materialSearchService")
public class MaterialSearchServiceImpl implements MaterialSearchService {

    private static final Set<Integer> NEED_SEARCH_MATERIAL = Sets.newHashSet(MaterialTypeEnum.ARTICLE.getType(), MaterialTypeEnum.PRODUCT.getType(), MaterialTypeEnum.POSTER.getType(),
            MaterialTypeEnum.FILE.getType(), MaterialTypeEnum.IMAGE.getType(), MaterialTypeEnum.VIDEO.getType(), MaterialTypeEnum.HEXAGON_SITE.getType(), MaterialTypeEnum.KNOWLEDGE.getType());

    @Autowired
    private ArticleService articleService;

    @Autowired
    private com.facishare.marketing.api.service.kis.ProductService kisProductService;

    @Autowired
    private ProductService productService;

    @Autowired
    private QRPosterService qrPosterService;

    @Autowired
    private FileLibraryService fileLibraryService;

    @Autowired
    private VideoService videoService;

    @Autowired
    private PhotoLibraryService photoLibraryService;

    @Autowired
    private MaterialShowSettingService materialShowSettingService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private ServiceKnowledgeObjManager serviceKnowledgeObjManager;
    @Autowired
    private MarketingPluginConfigManager marketingPluginConfigManager;
    @Autowired
    private ServiceKnowledgeScenarySettingDao serviceKnowledgeScenarySettingDao;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    @Autowired
    private HexagonService hexagonService;

    @Override
    public Result<List<SearchMaterialResult>> search(SearchMaterialVO vo) {
        String ea = vo.getEa();
        Integer fsUserId = vo.getFsUserId();
        Integer pageNum = 1;// 默认第一页
        Integer pageSize = 3;// 只查3条
        List<SearchMaterialResult> resultList = new CopyOnWriteArrayList<>();
        String filter = vo.getKeyword();
        GetShowAppMenuTemplateArg getShowAppMenuTemplateArg = new GetShowAppMenuTemplateArg();
        getShowAppMenuTemplateArg.setFsUserId(fsUserId);
        getShowAppMenuTemplateArg.setEa(ea);
        Result<AppMenuTemplateDetailVO> showAppMenuTemplateResult = appMenuTemplateService.getShowAppMenuTemplate(getShowAppMenuTemplateArg);
        if (!showAppMenuTemplateResult.isSuccess()) {
            return Result.newSuccess(resultList);
        }

        AppMenuTemplateDetailVO appMenuTemplateDetailVO = showAppMenuTemplateResult.getData();
        List<AppMenuDetailVO> menuList = appMenuTemplateDetailVO.getMenuList();
        if (CollectionUtils.isEmpty(menuList)) {
            return Result.newSuccess(resultList);
        }
        menuList = menuList.stream().filter(e -> NEED_SEARCH_MATERIAL.contains(e.getTargetMaterialType())).collect(Collectors.toList());
        int searchSize = menuList.size();
        CountDownLatch countDownLatch = new CountDownLatch(searchSize);
        TraceContext context = TraceContext.get();
        long beginTime = System.currentTimeMillis();

        ExecutorService executorService = NamedThreadPool.newFixedThreadPool(searchSize, "marketing_object_search");

        for (AppMenuDetailVO appMenuDetailVO : menuList) {
            String menuId = appMenuDetailVO.getId();
            int type = appMenuDetailVO.getTargetMaterialType();
            // 文章
            if (Objects.equals(type, MaterialTypeEnum.ARTICLE.getType())) {
                executorService.execute(()->{
                    try {
                        if (context != null ) {
                            TraceContext._set(context);
                        }
                        ListArticleArg listArticleArg = new ListArticleArg();
                        listArticleArg.setPageNum(pageNum);
                        listArticleArg.setPageSize(pageSize);
                        listArticleArg.setTitle(filter);
                        listArticleArg.setGroupId("-1");
                        listArticleArg.setStatus(ArticleStatusEnum.START_USING.getStatus());
                        if (vo.getMaterialTagFilter() != null) {
                            listArticleArg.setMaterialTagFilter(BeanUtil.copy(vo.getMaterialTagFilter(), MaterialTagFilterArg.class));
                        }
                        listArticleArg.setMenuId(menuId);
                        Result<KisArticleListResult> serviceResult = articleService.listArticles(ea, fsUserId, eieaConverter.enterpriseAccountToId(ea), listArticleArg);
                        if (serviceResult.isSuccess() && serviceResult.getData() != null && CollectionUtils.isNotEmpty(serviceResult.getData().getArticleDetailResults())) {
                            JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(serviceResult.getData().getArticleDetailResults()));
                            SearchMaterialResult result = new SearchMaterialResult();
                            result.setMenuId(menuId);
                            result.setMenuName(appMenuDetailVO.getName());
                            result.setTargetMaterialType(type);
                            result.setObjectDataList(jsonArray);
                            resultList.add(result);
                        }
                    } catch (Exception e) {
                        log.error("search article error, arg: {}", vo, e);
                    } finally {
                        countDownLatch.countDown();
                        if (context != null ) {
                            TraceContext.remove();
                        }
                    }
                });
            }
            // 产品
            if (Objects.equals(type, MaterialTypeEnum.PRODUCT.getType())) {
                executorService.execute(()->{
                    try {
                        if (context != null ) {
                            TraceContext._set(context);
                        }
                        ListProductArg listProductArg = new ListProductArg();
                        listProductArg.setPageNum(pageNum);
                        listProductArg.setPageSize(pageSize);
                        listProductArg.setTitle(filter);
                        listProductArg.setGroupId("-1");
                        listProductArg.setStatus(ProductStatusEnum.NORMAL.getStatus());
                        if (vo.getMaterialTagFilter() != null) {
                            listProductArg.setMaterialTagFilter(BeanUtil.copy(vo.getMaterialTagFilter(), MaterialTagFilterArg.class));
                        }
                        listProductArg.setMenuId(menuId);
                        Result<ProductListResult> serviceResult;
                        if (vo.getRequestFromType() == RequestFromTypeEnum.APP.getValue()) {
                            serviceResult = kisProductService.listProducts(ea, fsUserId, eieaConverter.enterpriseAccountToId(ea), listProductArg);
                        } else {
                            listProductArg.setConnectMarketingActivity(true);
                            serviceResult = productService.listEnterpriseProducts(ea, fsUserId, listProductArg);
                        }
                        if (serviceResult != null && serviceResult.isSuccess() && serviceResult.getData() != null && CollectionUtils.isNotEmpty(serviceResult.getData().getProductDetailResultList())) {
                            JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(serviceResult.getData().getProductDetailResultList()));
                            SearchMaterialResult result = new SearchMaterialResult();
                            result.setMenuId(menuId);
                            result.setMenuName(appMenuDetailVO.getName());
                            result.setTargetMaterialType(type);
                            result.setObjectDataList(jsonArray);
                            resultList.add(result);
                        }
                    } catch (Exception e) {
                        log.error("search product error, arg: {}", vo, e);
                    } finally {
                        countDownLatch.countDown();
                        if (context != null ) {
                            TraceContext.remove();
                        }
                    }
                });
            }
            // 海报
            if (Objects.equals(type, MaterialTypeEnum.POSTER.getType())) {
                executorService.execute(() -> {
                    try {
                        if (context != null ) {
                            TraceContext._set(context);
                        }
                        if (StringUtils.isNotBlank(vo.getKeyword())) {
                            Result<PageResult<QueryQRPosterByEaListUnitResult>> serviceResult = qrPosterService.queryListByEa(ea, fsUserId, pageSize, pageNum, null, null, filter, false, QRPosterTypeEnum.NORMAL.getType(), "-1", true);
                            if (serviceResult.isSuccess() && serviceResult.getData() != null && CollectionUtils.isNotEmpty(serviceResult.getData().getResult())) {
                                JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(serviceResult.getData().getResult()));
                                SearchMaterialResult result = new SearchMaterialResult();
                                result.setMenuId(menuId);
                                result.setMenuName(appMenuDetailVO.getName());
                                result.setTargetMaterialType(type);
                                result.setObjectDataList(jsonArray);
                                resultList.add(result);
                            }
                        }
                    } catch (Exception e) {
                        log.error("search poster error, arg: {}", vo, e);
                    } finally {
                        countDownLatch.countDown();
                        if (context != null ) {
                            TraceContext.remove();
                        }
                    }
                });
            }
            // 文件
            if (Objects.equals(type, MaterialTypeEnum.FILE.getType())) {
                executorService.execute(() -> {
                    try {
                        log.info("文件查询开始启动");
                        if (context != null ) {
                            TraceContext._set(context);
                        }
                        ListFileByGroupArg listFileByGroupArg = new ListFileByGroupArg();
                        listFileByGroupArg.setGroupId("-1");
                        listFileByGroupArg.setKeyword(filter);
                        listFileByGroupArg.setPageNum(pageNum);
                        listFileByGroupArg.setPageSize(pageSize);
                        if (vo.getMaterialTagFilter() != null) {
                            listFileByGroupArg.setMaterialTagFilter(BeanUtil.copy(vo.getMaterialTagFilter(), MaterialTagFilterArg.class));
                        }
                        listFileByGroupArg.setMenuId(menuId);
                        long t1 = System.currentTimeMillis();
                        Result<PageResult<ListFileByGroupResult>> serviceResult = fileLibraryService.listFileByGroup(ea, fsUserId, listFileByGroupArg);
                        log.info("查询文件耗时: {}", System.currentTimeMillis() - t1);
                        if (serviceResult.isSuccess() && serviceResult.getData() != null && CollectionUtils.isNotEmpty(serviceResult.getData().getResult())) {
                            JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(serviceResult.getData().getResult()));
                            SearchMaterialResult result = new SearchMaterialResult();
                            result.setMenuId(menuId);
                            result.setMenuName(appMenuDetailVO.getName());
                            result.setTargetMaterialType(type);
                            result.setObjectDataList(jsonArray);
                            resultList.add(result);
                        }
                    } catch (Exception e) {
                        log.error("search file error, arg: {}", vo, e);
                    } finally {
                        countDownLatch.countDown();
                        if (context != null ) {
                            TraceContext.remove();
                        }
                    }
                });
            }
            // 视频
            if (Objects.equals(type, MaterialTypeEnum.VIDEO.getType())) {
                executorService.execute(() -> {
                    try {
                        if (context != null ) {
                            TraceContext._set(context);
                        }
                        ListVideosArg queryVideoArg = new ListVideosArg();
                        queryVideoArg.setType(VideoTargetTypeEnum.MARKETING_VIDEO.getType());
                        queryVideoArg.setEa(ea);
                        queryVideoArg.setPageNum(pageNum);
                        queryVideoArg.setPageSize(pageSize);
                        queryVideoArg.setStatus(1);
                        queryVideoArg.setKeyword(filter);
                        queryVideoArg.setGroupId("-1");
                        queryVideoArg.setUserId(fsUserId);
                        if (vo.getMaterialTagFilter() != null) {
                            queryVideoArg.setMaterialTagFilter(BeanUtil.copy(vo.getMaterialTagFilter(), MaterialTagFilterArg.class));
                        }
                        queryVideoArg.setMenuId(menuId);
                        Result<PageResult<QueryVideoResult>> serviceResult = videoService.queryList(queryVideoArg);
                        if (serviceResult.isSuccess() && serviceResult.getData() != null && CollectionUtils.isNotEmpty(serviceResult.getData().getResult())) {
                            JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(serviceResult.getData().getResult()));
                            SearchMaterialResult result = new SearchMaterialResult();
                            result.setMenuId(menuId);
                            result.setMenuName(appMenuDetailVO.getName());
                            result.setTargetMaterialType(type);
                            result.setObjectDataList(jsonArray);
                            resultList.add(result);
                        }
                    } catch (Exception e) {
                        log.error("search video error, arg: {}", vo, e);
                    } finally {
                        countDownLatch.countDown();
                        if (context != null ) {
                            TraceContext.remove();
                        }
                    }
                });
            }
            // 图片
            if (Objects.equals(type, MaterialTypeEnum.IMAGE.getType())) {
                executorService.execute(() -> {
                    try {
                        if (context != null ) {
                            TraceContext._set(context);
                        }
                        ListPhotoByGroupArg arg = new ListPhotoByGroupArg();
                        arg.setGroupId("-1");
                        arg.setPageNum(pageNum);
                        arg.setPageSize(pageSize);
                        arg.setKeyword(filter);
                        arg.setSource(1);
                        if (vo.getMaterialTagFilter() != null) {
                            arg.setMaterialTagFilter(BeanUtil.copy(vo.getMaterialTagFilter(), MaterialTagFilterArg.class));
                        }
                        arg.setMenuId(menuId);
                        Result<PageResult<ListPhotoByGroupResult>> serviceResult = photoLibraryService.listPhotoByGroup(ea, fsUserId, arg);
                        if (serviceResult.isSuccess() && serviceResult.getData() != null && CollectionUtils.isNotEmpty(serviceResult.getData().getResult())) {
                            JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(serviceResult.getData().getResult()));
                            SearchMaterialResult result = new SearchMaterialResult();
                            result.setMenuId(menuId);
                            result.setMenuName(appMenuDetailVO.getName());
                            result.setTargetMaterialType(type);
                            result.setObjectDataList(jsonArray);
                            resultList.add(result);
                        }
                    } catch (Exception e) {
                        log.error("search photo error, arg: {}", vo,  e);
                    } finally {
                        countDownLatch.countDown();
                        if (context != null ) {
                            TraceContext.remove();
                        }
                    }
                });
            }
            if (Objects.equals(type, MaterialTypeEnum.HEXAGON_SITE.getType())) {
                executorService.execute(() -> {
                    try {
                        if (context != null ) {
                            TraceContext._set(context);
                        }
                        SimpleHexagonListArg arg = new SimpleHexagonListArg();
                        arg.setGroupId("-1");
                        arg.setPageNum(pageNum);
                        arg.setPageSize(pageSize);
                        arg.setKeyword(filter);
                        if (vo.getMaterialTagFilter() != null) {
                            arg.setMaterialTagFilter(BeanUtil.copy(vo.getMaterialTagFilter(), MaterialTagFilterArg.class));
                        }
                        arg.setMenuId(menuId);
                        arg.setFsUserId(fsUserId);
                        arg.setEa(ea);
                        Result<PageResult<SimpleHexagonVO>> serviceResult = hexagonService.simpleHexagonList(arg);
                        if (serviceResult.isSuccess() && serviceResult.getData() != null && CollectionUtils.isNotEmpty(serviceResult.getData().getResult())) {
                            JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(serviceResult.getData().getResult()));
                            SearchMaterialResult result = new SearchMaterialResult();
                            result.setMenuId(menuId);
                            result.setMenuName(appMenuDetailVO.getName());
                            result.setTargetMaterialType(type);
                            result.setObjectDataList(jsonArray);
                            resultList.add(result);
                        }
                    } catch (Exception e) {
                        log.error("search hexagon error, arg: {}", vo,  e);
                    } finally {
                        countDownLatch.countDown();
                        if (context != null ) {
                            TraceContext.remove();
                        }
                    }
                });
            }
            if (Objects.equals(type, MaterialTypeEnum.KNOWLEDGE.getType())) {
                executorService.execute(() -> {
                    try {
                        if (context != null ) {
                            TraceContext._set(context);
                        }
                        MenuDetailArg menuDetailArg = new MenuDetailArg();
                        menuDetailArg.setId(appMenuDetailVO.getId());
                        menuDetailArg.setEa(ea);
                        Result<AppMenuDetailVO> menuDetailResult = appMenuTemplateService.menuDetail(menuDetailArg);
                        if (menuDetailResult.isSuccess()) {
                            String scene = menuDetailResult.getData().getKnowledgeScene();
                            List<SearchKnowledgeResult> searchKnowledgeResults = serviceKnowledgeObjManager.searchKnowledge(ea, scene, filter);
                            if(CollectionUtils.isNotEmpty(searchKnowledgeResults)){
                                List<SearchKnowledgeResult> searchKnowledgeResult = searchKnowledgeResults.subList(0, Math.min(searchKnowledgeResults.size(), pageSize));
                                JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(searchKnowledgeResult));
                                SearchMaterialResult result = new SearchMaterialResult();
                                result.setTargetMaterialType(MaterialTypeEnum.KNOWLEDGE.getType());
                                result.setMenuName(appMenuDetailVO.getName());
                                result.setObjectDataList(jsonArray);
                                resultList.add(result);
                            }
                        }

                    } catch (Exception e) {
                        log.error("search Knowledge error, arg: {}", vo,  e);
                    } finally {
                        countDownLatch.countDown();
                        if (context != null ) {
                            TraceContext.remove();
                        }
                    }
                });
            }
        }

        try {
            countDownLatch.await(10L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("MaterialSearchServiceImpl -> search error arg: {}", vo, e);
        }
        log.info("SearchMaterialResult arg: {} 耗时： {}", vo, System.currentTimeMillis() - beginTime);
        if (!executorService.isShutdown()) {
            executorService.shutdownNow();
        }
        Map<String, SearchMaterialResult> menuIdToResult = resultList.stream().collect(Collectors.toMap(SearchMaterialResult::getMenuId, v -> v, (v1, v2) -> v1));
        List<SearchMaterialResult> sortList = Lists.newArrayList();
        for (AppMenuDetailVO menuDetailVO : menuList) {
            SearchMaterialResult result = menuIdToResult.get(menuDetailVO.getId());
            if (result != null) {
                sortList.add(result);
            }
        }
        return Result.newSuccess(sortList);
    }

    @Override
    public Result<List<SearchKnowledgeResult>> knowledgeSearch(SearchMaterialVO vo) {
        ServiceKnowledgeScenarySettingEntity entity = serviceKnowledgeScenarySettingDao.getDetailByEa(vo.getEa());
        if(entity==null || StringUtils.isBlank(entity.getScenary())){
            return Result.newSuccess();
        }
        List<SearchKnowledgeResult> knowledgeResults = serviceKnowledgeObjManager.searchKnowledge(vo.getEa(), entity.getScenary(), vo.getKeyword());
        return Result.newSuccess(knowledgeResults);
    }

    /**
     * 获取需要搜索的物料
     * @param ea
     * @param fsUserId
     * @return
     */
    private List<MaterialShowResult> getNeedSearchMaterial(String ea, Integer fsUserId){
        // 查询内容展示设置，根据设置展示搜索内容
        List<MaterialShowResult> result = Lists.newArrayList();
        Result<List<MaterialShowResult>> listResult = materialShowSettingService.queryMaterialShowList(ea, fsUserId);
        if (!listResult.isSuccess()) {
            return result;
        }
        List<MaterialShowResult> showResultList = listResult.getData();
        if (CollectionUtils.isNotEmpty(showResultList)) {
            for (MaterialShowResult materialShowResult : showResultList) {
                if (NEED_SEARCH_MATERIAL.contains(materialShowResult.getType()) && materialShowResult.getShowStatus()) {
                    result.add(materialShowResult);
                }
            }
        }
        log.info("need search material result:{}", result);
        return result;
    }
}
