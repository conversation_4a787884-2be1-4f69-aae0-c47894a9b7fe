package com.facishare.marketing.provider.entity.marketingplugin;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/29 20:38
 */
@Data
@Entity
public class SendDealerCouponRecordEntity implements Serializable {

    private String id;  //主键id

    private String couponId; //优惠券id

    private String ea;  //上游发券企业

    private String downStreamEa; //下游经销商

    private String downStreamTenantId; //下游的企业互联id

    private String downStreamName; //下游经销商名称

    private Integer dealerCount; //下发数量

    private Date createTime;

    private Integer addCouponActivity; //参与状态  0:未参与 1:已参与
}
