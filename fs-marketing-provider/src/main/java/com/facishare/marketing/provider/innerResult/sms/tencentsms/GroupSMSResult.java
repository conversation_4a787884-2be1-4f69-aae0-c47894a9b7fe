package com.facishare.marketing.provider.innerResult.sms.tencentsms;

import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * @创建人 zhengliy
 * @创建时间 2018/12/20 11:14
 * @描述
 */

@Data
@ToString
public class GroupSMSResult implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * [result]  错误码，0 表示成功（计费依据），非 0 表示失败
     * [msg] 错误消息，result 非 0 时的具体错误信息
     * [ext] 用户的 session 内容，腾讯 server 回包中会原样返回
     * [detail] 结果详细
     *      [result] 错误码，0 表示成功（计费依据），非 0 表示失败
     *      [errmsg] 错误消息，result 非 0 时的具体错误信息
     *      [fee]  短信计费的条数
     *      [mobile]  手机号码
     *      [nationcode]  国家码
     *      [sid]  本次发送标识 id，标识一次短信下发记录
     */
    private String result;

    private String msg;

    private String ext;

    private List<ResultData> detail;

    @ToString
    @Data
    static public class ResultData implements Serializable{
        private static final long serialVersionUID = 1L;

        private String result;
        private String errmsg;
        private String fee;
        private String mobile;
        private String nationcode;
        private String sid;
        public boolean isSuccess(){
            return StringUtils.isNotBlank(result) && "0".equals(result);
        }

    }

    public boolean isSuccess() {
        return StringUtils.isNotBlank(result) && "0".equals(result);
    }

      public ResultData getOneSMSStatus(){
            if(isSuccess()){
                return detail.get(0);
            }
            return null;
      }
      public boolean oneSMSSuccess(){
          if(isSuccess()){
              if(detail.get(0).isSuccess()) {
                  return true;
              }else{
                  return false;
              }
          }
          return false;
      }
}
