package com.facishare.marketing.provider.entity.wxthirdplatform;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Unique(ea, thirdPlatform)
 */
@Data
public class EaWechatAccountBindEntity implements Serializable {
    /**
     * 企业帐号
     */
    private String ea;

    /**
     * 公众号ID
     */
    private String wxAppId;
    /**
     * 第三方平台id, 营销通为:YXT
     */
    private String thirdPlatformId;

    private Date createTime;

    private Date updateTime;
}
