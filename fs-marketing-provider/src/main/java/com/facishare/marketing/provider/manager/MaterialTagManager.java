package com.facishare.marketing.provider.manager;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.result.MaterialTagDTO;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.provider.dao.MaterialRelationDao;
import com.facishare.marketing.provider.dao.MaterialTagRelationDao;
import com.facishare.marketing.provider.dao.TagModelUserTagRelationDao;
import com.facishare.marketing.provider.dao.UserTagDao;
import com.facishare.marketing.provider.entity.MaterialTagRelationEntity;
import com.facishare.marketing.provider.entity.TagModelUserTagRelationEntity;
import com.facishare.marketing.provider.entity.UserTagEntity;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MaterialTagManager {

    @Autowired
    private MaterialTagRelationDao  materialTagRelationDao;

    @Autowired
    private TagModelUserTagRelationDao tagModelUserTagRelationDao;

    @Autowired
    private UserTagDao userTagDao;

    public Map<String, List<String>> buildTagName(List<String> objectIds, Integer objectType){
        if (CollectionUtils.isEmpty(objectIds)) {
            return Maps.newHashMap();
        }
        List<MaterialTagDTO> materialTagDTOS = materialTagRelationDao.queryMaterialRelation(objectIds, objectType);
        if (CollectionUtils.isNotEmpty(materialTagDTOS)) {
            return materialTagDTOS.stream().collect(Collectors.toMap(MaterialTagDTO::getObjectId, MaterialTagDTO::getTagNames));
        }
        return Maps.newHashMap();
    }

    /**
     * 校验模型能否删除
     * @param ea
     * @param tagModelId
     * @return
     */
    public Result checkDeleteTagModel(String ea, String tagModelId){
        // 1 检查模型下有无标签
        List<TagModelUserTagRelationEntity> tagModelUserTagRelationEntities = tagModelUserTagRelationDao.queryTagModelUserTagRelationByTagMode(ea, tagModelId);
        if (CollectionUtils.isNotEmpty(tagModelUserTagRelationEntities)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnum.MARK_MANAGER_MATERIALTAGMANAGER_57));
        }
        return Result.newSuccess();
    }

    /**
     * 校验标签能否删除
     * @param ea
     * @param tagId
     * @return
     */
    public Result checkDeleteTag(String ea, String tagId){
        UserTagEntity tagEntity = userTagDao.getTagById(ea, tagId);
        if (tagEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        if(tagEntity.isFirstLevelTag()){
            // 一级标签
            // 1 检查标签是否关联了物料
            List<MaterialTagRelationEntity> materialTagRelationEntities = materialTagRelationDao.queryByTagIds(ea, Lists.newArrayList(tagId));
            if (CollectionUtils.isNotEmpty(materialTagRelationEntities)) {
                return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnum.MARK_MANAGER_MATERIALTAGMANAGER_78));
            }
            // 2 检查是否有二级标签
            List<UserTagEntity> subUserTagEntities = userTagDao.listByParentTagId(ea, tagId);
            if (CollectionUtils.isNotEmpty(subUserTagEntities)) {
                return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnum.MARK_MANAGER_MATERIALTAGMANAGER_83));
            }
        } else {
            // 二级标签
            // 1 检查标签是否关联了物料
            List<MaterialTagRelationEntity> materialTagRelationEntities = materialTagRelationDao.queryByTagIds(ea, Lists.newArrayList(tagId));
            if (CollectionUtils.isNotEmpty(materialTagRelationEntities)) {
                return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnum.MARK_MANAGER_MATERIALTAGMANAGER_78));
            }
        }
        return Result.newSuccess();
    }

    /**
     * 删除标签
     * @param ea
     * @param tagId
     */
    public void deleteTag(String ea, String tagId){
        UserTagEntity tagEntity = userTagDao.getTagById(ea, tagId);
        if (tagEntity == null) {
            return;
        }
        if(tagEntity.isFirstLevelTag()){
            // 处理一级标签
            userTagDao.deleteByTagId(ea, tagId);
            materialTagRelationDao.deleteByTagId(ea, tagId);
            // 处理二级标签
            List<UserTagEntity> subUserTagEntities = userTagDao.listByParentTagId(ea, tagId);
            if (CollectionUtils.isNotEmpty(subUserTagEntities)) {
                List<String> subTagIds = subUserTagEntities.stream().map(UserTagEntity::getId).collect(Collectors.toList());
                for (String subTagId : subTagIds) {
                    userTagDao.deleteByTagId(ea, subTagId);
                    materialTagRelationDao.deleteByTagId(ea, subTagId);
                }
            }
        } else {
            // 处理二级标签
            userTagDao.deleteByTagId(ea, tagId);
            materialTagRelationDao.deleteByTagId(ea, tagId);
        }
    }

    /**
     * 校验标签能否创建
     * @param ea
     * @param tagName
     * @param tagId
     * @return
     */
    public Result checkCreateTag(String ea, String tagName, String tagId){
        // 1 检查名称是否存在
        /*List<UserTagEntity> tagEntities = userTagDao.getByTagName4Material(ea, tagName, tagId);
        if (CollectionUtils.isNotEmpty(tagEntities)) {
            return Result.newError(SHErrorCode.TAG_DUPLICATION_OF_NAME);
        }*/
        return Result.newSuccess();
    }
}