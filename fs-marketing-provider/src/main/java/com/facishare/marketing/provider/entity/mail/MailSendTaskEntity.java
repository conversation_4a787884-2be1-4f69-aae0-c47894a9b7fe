package com.facishare.marketing.provider.entity.mail;

import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.ToString;

/**
 * Created by zhengh on 2020/6/4.
 */
@Data
@ToString(exclude = {"html"})
public class MailSendTaskEntity implements Serializable{
    private String id;                      //任务id
    private String ea;                      //企业账号
    private String subject;                   //邮件标题
    private Integer fsUserId;               //发件人的userId;
    private String marketingGroupUserIds;   //营销用户目标人群
    private String marketingEventId;        //市场活动id
    private String toUser;                      //邮件地址
    private String html;                    //邮件的内容,格式为text/html
    private Integer sendRange;              //发送对象类型 MailSendRangeEnum
    private Long fixTime;                   //定时发送时间
    private String contentSummary;          //邮件摘要
    private String attachments;             //邮件附件地址，JSON格式
    private String templateInvokeName;      //模板调用名称
    private Integer sendStatus;             //发送状态  见MailSendStatusEnum
    private Integer totalSendCount;             //待发送总人数
    private Integer scheduleType;               //调度类型 MailScheduleTypeEnum
    private Integer mailType;                   //邮件类型 0：触发  1：批量
    private Integer labelId;                 // 标签id
    private String senderIds;            //发件人id列表 json
    private String replyIds;               //回复人id列表 json
    private Date createTime;                //创建时间
    private Date updateTime;                //更新时间
    private Integer filterNDaySentUser;     // 过滤近几天用户
    private Integer statusCode;             //邮件发送返回状态码
    private TagNameList sendObjectTags;        //通过对象标签筛选发送的邮箱
    private TagNameList excludeObjectTags;   //通过对象标签筛选，需要在sendObjectTags基础上过滤掉的标签
    private String tagOperator;              //标签操作类型， 同时包含（INHASANYOF）还是任意（IN)
}
