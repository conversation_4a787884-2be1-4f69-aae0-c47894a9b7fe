package com.facishare.marketing.provider.innerData.aizhan;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
@Data
public class SiteRankingData implements Serializable {


    @SerializedName("date")
    private String date;

    @SerializedName("alexa_1days_value")
    private String alexa1DaysValue;

    @SerializedName("alexa_7days_value")
    private String alexa7DaysValue;

    @SerializedName("alexa_1months_value")
    private String alexa1MonthsValue;

    @SerializedName("alexa_3months_value")
    private String alexa3MonthsValue;

    @SerializedName("alexa_avg_ip")
    private String alexaAvgIp;

    @SerializedName("alexa_avg_pv")
    private String alexaAvgPv;

}
