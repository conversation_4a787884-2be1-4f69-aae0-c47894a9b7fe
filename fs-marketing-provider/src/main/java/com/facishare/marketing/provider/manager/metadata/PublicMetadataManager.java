package com.facishare.marketing.provider.manager.metadata;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.publicdata.PublicDataRangeArg;
import com.facishare.marketing.api.arg.publicdata.UpdatePublicArg;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.remote.metadata.*;
import com.facishare.marketing.provider.remote.udobjrest.ObjectDescribeResult;
import com.facishare.marketing.provider.remote.udobjrest.PaasUdobjRestService;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
@Slf4j
public class PublicMetadataManager{

    @Autowired
    private PublicMetadataService publicMetadataService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private PaasUdobjRestService paasUdobjRestService;

    @Autowired
    private PublicConvertService publicConvertService;

    @Autowired
    private PublicPurgeService publicPurgeService;

    Set<String> PUBLIC_OBJECT_VISIBLE_SCOPE = Sets.newHashSet("public", "public_big");

    /**
     * 根据对象id, 查询公共对象可见范围
     * @param arg
     * @return
     */
    public Map<String, Set<String>> findPublicDataRange(PublicDataRangeArg arg){
        Map<String, Set<String>> map = Maps.newHashMap();
        String ea = arg.getEa();
        int ei = eieaConverter.enterpriseAccountToId(ea);
        PublicHeaderObj headObj = PublicHeaderObj.newInstance(ei,TraceContext.get().getTraceId());
        QueryPublicRangeArg queryArg = new QueryPublicRangeArg();
        queryArg.setTenantId(String.valueOf(ei));
        queryArg.setDescribeApiName(arg.getDescribeApiName());
        queryArg.setIds(arg.getIds());
        BaseMetaDataResult<Map<String,Set<String>>> publicDataResult = publicMetadataService.findPublicDataTenants(headObj, queryArg);
        if (publicDataResult.isSuccess() && publicDataResult.getResult() != null) {
            map = publicDataResult.getResult();
        }
        return map;
    }

    /**
     * 更新公共对象可见范围
     * @param arg
     */
    public void updatePublicDataRange(UpdatePublicArg arg){
        String ea = arg.getEa();
        int ei = eieaConverter.enterpriseAccountToId(ea);
        PublicHeaderObj headObj = PublicHeaderObj.newInstance(ei,TraceContext.get().getTraceId());
        UpdatePublicRangeArg updateArg = BeanUtil.copy(arg,UpdatePublicRangeArg.class);
        updateArg.setTenantId(String.valueOf(ei));
        BaseMetaDataResult<Void> updateResult = publicMetadataService.updatePublicDataRangeTenant(headObj, updateArg);
        if (!updateResult.isSuccess()) {
            log.warn("updatePublicDataRange is error code:{},errorMsg:{}",updateResult.getErrCode(),updateResult.getErrMessage());
        }
    }

    /**
     * 判断企业的对象是否为公共对象
     * @param ea
     * @param describeApiName
     * @return
     */
    public boolean isPublicObject(String ea,String describeApiName) {
        boolean publicObject = false;
        int ei = eieaConverter.enterpriseAccountToId(ea);
        PublicHeaderObj headObj = PublicHeaderObj.newInstance(ei,TraceContext.get().getTraceId());
        Result<ObjectDescribeResult> result = paasUdobjRestService.findDescribe(describeApiName, headObj);
        if (result.isSuccess() && result.getData() != null) {
            ObjectDescribeResult.Describe describe = result.getData().getDescribe();
            if (PUBLIC_OBJECT_VISIBLE_SCOPE.contains(describe.getVisibleScope()) && ObjectUtils.isNotEmpty(describe.getUpstreamTenantId())) {
                publicObject = true;
            }
        }
        return publicObject;
    }

    /**
     * 查询公共对象
     * @param ea
     * @return
     */
    public ConvertPublicArg findPublicDescribeNames(String ea) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        PublicHeaderObj headObj = PublicHeaderObj.newInstance(ei,TraceContext.get().getTraceId());
        ConvertPublicArg arg = new ConvertPublicArg();
        arg.setObjectApiNames(Lists.newArrayList("CouponObj","CouponDistributionObj"));
        BaseMetaDataResult<ConvertPublicArg> publicDescribeApiNamesResult = publicConvertService.findPublicDescribeApiNames(headObj, arg);
        if (publicDescribeApiNamesResult.isSuccess()) {
            return publicDescribeApiNamesResult.getResult();
        }
        return null;
    }

    public void convertPublic(String ea) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        PublicHeaderObj headObj = PublicHeaderObj.newInstance(ei,TraceContext.get().getTraceId());
        ConvertPublicArg arg = new ConvertPublicArg();
        arg.setObjectApiNames(Lists.newArrayList("CouponObj","CouponDistributionObj"));
        BaseMetaDataResult<PublicResult> convertPublicResult = publicConvertService.convertPublicDescribe(headObj, arg);
        if (!convertPublicResult.isSuccess()) {
            log.warn("convertPublic is failed ea:{}",ea);
        }
    }

    public void enablePublic(String ea,String upStreamTenantId) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        PublicHeaderObj headObj = PublicHeaderObj.newInstance(ei,TraceContext.get().getTraceId());
        EnablePublicArg arg = new EnablePublicArg();
        arg.setUpstreamTenantId(upStreamTenantId);
        arg.setObjectApiNames(Lists.newArrayList("CouponObj","CouponDistributionObj"));
        BaseMetaDataResult<PublicResult> convertPublicResult = publicConvertService.enablePublicDescribe(headObj, arg);
        if (!convertPublicResult.isSuccess()) {
            log.warn("enablePublic is failed ea:{}",ea);
        }
    }

    /**
     * 清除公共对象下游缓存
     * @param ea
     * @param describeApiNames
     */
    public void batchPurge(String ea,List<String> describeApiNames) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        PurgeHeaderObj headObj = PurgeHeaderObj.newInstance(TraceContext.get().getTraceId());
        BatchPurgeArg arg = new BatchPurgeArg();
        arg.setTenantId(String.valueOf(ei));
        arg.setDescribeApiNames(describeApiNames);
        BaseMetaDataResult<PublicResult> publicPurgeResult = publicPurgeService.batchPurge(headObj, arg);
        if (!publicPurgeResult.isSuccess()) {
            log.warn("batchPurge is failed ea:{}",ea);
        }
    }
}
