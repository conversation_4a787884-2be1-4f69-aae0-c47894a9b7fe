package com.facishare.marketing.provider.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg;
import com.facishare.marketing.api.arg.objectgroup.SaveObjectGroupArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.ListObjectGroupResult;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.contstant.DisplayOrderConstants;
import com.facishare.marketing.common.contstant.material.OperateTypeEnum;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.ObjectGroupStatusEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.NestedId;
import com.facishare.marketing.common.typehandlers.value.NestedIdList;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.ObjectGroupRelationDAO;
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO;
import com.facishare.marketing.provider.dto.ContentGroupCountDTO;
import com.facishare.marketing.provider.dto.ObjectGroupDTO;
import com.facishare.marketing.provider.entity.DisplayOrderEntity;
import com.facishare.marketing.provider.entity.ObjectGroupEntity;
import com.facishare.marketing.provider.entity.ObjectGroupRelationEntity;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupDepartmentRelationEntity;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupOuterGroupRelationEntity;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupOuterTenantRelationEntity;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity;
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.privilege.api.module.role.RoleVo;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.paasauthrestapi.result.TenantGroupResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Component
public class ObjectGroupManager {
    @Autowired
    private ObjectGroupDAO objectGroupDAO;
    @Autowired
    private OpenAppAdminService openAppAdminService;
    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;
    @Autowired
    private DisplayOrderManager displayOrderManager;

    @Autowired
    private AuthManager authManager;
    @Autowired
    private AuthPartnerManager authPartnerManager;
    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private UserRoleManager userRoleManager;

    @Autowired
    private MktContentMgmtLogObjManager mktContentMgmtLogObjManager;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    @ReloadableProperty("marketing_appid")
    private String appId;

    public Result<EditObjectGroupResult> editGroup(String ea, Integer fsUserId, String groupId, String groupName, Integer objectType) {
        if (StringUtils.isEmpty(groupId)) {
            //创建分组
            int count = objectGroupDAO.getTotalCountByName(ea, groupName, objectType);
            if (count > 0) {
                log.info("ContentGroupManager.editGroup create failed group already exist ea:{} fsUserId:{} groupId:{} " +
                        "groupName:{} objectType:{}", ea, fsUserId, groupId, groupName, objectType);
                return Result.newError(SHErrorCode.HEXAGON_GROUP_EXIST);
            }

            ObjectGroupEntity entity = new ObjectGroupEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setCreateBy(fsUserId);
            entity.setName(groupName);
            entity.setStatus(ObjectGroupStatusEnum.NORMAL.getStatus());
            entity.setObjectType(objectType);
            entity.setInnerVisible(1);
            entity.setOuterVisible(2); // 默认企业外不可见
            entity.setLevel(1);
            objectGroupDAO.insert(entity);
            /*SaveObjectGroupVisibleArg groupRoleArg = new SaveObjectGroupVisibleArg();
            groupRoleArg.setGroupId(entity.getId());
            groupRoleArg.setRoleIdList(Collections.singletonList("ALL"));
            groupRelationManager.saveGroupRelation(ea, groupRoleArg, objectType);*/
            List<NestedId> nestedIdList = Lists.newArrayList();
            NestedId nestedId = new NestedId();
            nestedId.setId(entity.getId());
            nestedIdList.add(nestedId);
            if (objectType.equals(ObjectTypeEnum.HEXAGON_TEMPLATE.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.HEXAGON_TEMPLATE_GROUP_DISPLAY_KEY, nestedIdList);
            } else if (objectType.equals(ObjectTypeEnum.HEXAGON_SITE.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.HEXAGON_GROUP_DISPLAY_KEY, nestedIdList);
            } else if (objectType.equals(ObjectTypeEnum.FILE.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.FILE_GROUP_DISPLAY_KEY, nestedIdList);
            } else if (objectType.equals(ObjectTypeEnum.IMAGE.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.PHOTO_GROUP_DISPLAY_KEY, nestedIdList);
            } else if (objectType.equals(ObjectTypeEnum.COUPON_TEMPLATE.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.COUPON_TEMPLATE_GROUP_DISPLAY_KEY, nestedIdList);
            } else if (objectType.equals(ObjectTypeEnum.ARTICLE.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.ARTICLE_GROUP_DISPLAY_KEY, nestedIdList);
            } else if (objectType.equals(ObjectTypeEnum.PRODUCT.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.PRODUCT_GROUP_DISPLAY_KEY, nestedIdList);
            } else if (objectType.equals(ObjectTypeEnum.CUSTOMIZE_FORM.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.CUSTOMIZE_FORM_GROUP_DISPLAY_KEY, nestedIdList);
            } else if (objectType.equals(ObjectTypeEnum.QR_POSTER.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.QR_POSTER_GROUP_DISPLAY_KEY, nestedIdList);
            } else if (objectType.equals(ObjectTypeEnum.VIDEO.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.VIDEO_GROUP_DISPLAY_KEY, nestedIdList);
            } else if (objectType.equals(ObjectTypeEnum.QY_GROUP_CODE.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.QY_GROUP_CODE_GROUP_DISPLAY_KEY, nestedIdList);
            } else if (objectType.equals(ObjectTypeEnum.FAN_CODE.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.FAN_CODE_GROUP_DISPLAY_KEY, nestedIdList);
            } else if (objectType.equals(ObjectTypeEnum.USER_MARKETING_GROUP.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.MARKETING_USER_GROUP_DISPLAY_KEY, nestedIdList);
            } else if (objectType.equals(ObjectTypeEnum.QY_WELCOME_MSG.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.QY_WELCOME_MSG_GROUP_DISPLAY_KEY, nestedIdList);
            } else if (objectType.equals(ObjectTypeEnum.QY_SOP.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.QY_SOP_GROUP_DISPLAY_KEY, nestedIdList);
            } else if (objectType.equals(ObjectTypeEnum.QY_GROUP_SOP.getType())) {
                displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.QY_GROUP_SOP_GROUP_DISPLAY_KEY, nestedIdList);
            }

            EditObjectGroupResult result = new EditObjectGroupResult();
            result.setId(entity.getId());
            return Result.newSuccess(result);
        }

        //更新分组
        ObjectGroupEntity entity = objectGroupDAO.queryById(ea, groupId);
        if (entity == null) {
            log.info("ObjectGroupManager.editGroup update group failed group not exist ea:{} fsUserId:{} groupId:{}" +
                    "groupName:{} objectType:{}", ea, fsUserId, groupId, groupName, objectType);
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(groupName) && !StringUtils.equals(groupName, entity.getName())) {
            int count = objectGroupDAO.getTotalCountByName(ea, groupName, objectType);
            if (count > 0) {
                log.info("ObjectGroupManager.editGroup update failed group already exist ea:{} fsUserId:{} groupId:{}" +
                        "groupName:{} objectType:{}", ea, fsUserId, groupId, groupName, objectType);
                return Result.newError(SHErrorCode.HEXAGON_GROUP_EXIST);
            }
        }

        objectGroupDAO.updateInfoById(ea, groupId, groupName, null);

        return Result.newSuccess();
    }

    public Result<EditObjectGroupResult> saveGroup(SaveObjectGroupArg arg) {
        String ea = arg.getEa();
        Integer fsUserId = arg.getFsUserId();
        String groupId = arg.getId();
        String groupName = arg.getName();
        Integer objectType = arg.getObjectType();
        if (StringUtils.isEmpty(groupId)) {
            //创建分组
            int count = objectGroupDAO.getTotalCountByName(ea, groupName, objectType);
            if (count > 0) {
                return Result.newError(SHErrorCode.OBJECT_GROUP_NAME_EXISTS);
            }

            ObjectGroupEntity entity = new ObjectGroupEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setCreateBy(fsUserId);
            entity.setName(groupName);
            entity.setStatus(ObjectGroupStatusEnum.NORMAL.getStatus());
            entity.setObjectType(objectType);
            entity.setInnerVisible(1);
            entity.setOuterVisible(2); // 默认企业外不可见
            entity.setParentId(arg.getParentId());
            entity.setSeqNo(arg.getSeqNo());
            if (StringUtils.isNotBlank(arg.getParentId())) {
                ObjectGroupEntity parentEntity = objectGroupDAO.getById(ea, arg.getParentId());
                if (parentEntity != null) {
                    Integer parentEntityLevel = parentEntity.getLevel();
                    if (parentEntityLevel >= 4) {
                        return Result.newError(SHErrorCode.OBJECT_GROUP_LEVEL_MAX);
                    }
                    entity.setLevel(parentEntity.getLevel() + 1);
                }
            } else {
                entity.setLevel(1);
            }
            objectGroupDAO.insert(entity);
            // 处理可见范围
            SaveObjectGroupVisibleArg visibleArg = arg.getVisible();
            if (visibleArg != null) {
                visibleArg.setGroupId(entity.getId());
                visibleArg.setObjectType(entity.getObjectType());
            }
            objectGroupRelationVisibleManager.saveGroupRelation(ea, visibleArg, objectType);
            EditObjectGroupResult result = new EditObjectGroupResult();
            result.setId(entity.getId());
            return Result.newSuccess(result);
        } else {
            //更新分组
            ObjectGroupEntity entity = objectGroupDAO.queryById(ea, groupId);
            if (entity == null) {
                return Result.newError(SHErrorCode.OBJECT_GROUP_NOT_EXISTS);
            }
            if (StringUtils.isNotEmpty(groupName) && !StringUtils.equals(groupName, entity.getName())) {
                int count = objectGroupDAO.getTotalCountByName(ea, groupName, objectType);
                if (count > 0) {
                    return Result.newError(SHErrorCode.OBJECT_GROUP_NAME_EXISTS);
                }
            }

            objectGroupDAO.updateInfoById(ea, groupId, groupName, arg.getSeqNo());
            // 处理可见范围
            SaveObjectGroupVisibleArg visibleArg = arg.getVisible();
            if (visibleArg != null) {
                visibleArg.setGroupId(entity.getId());
                visibleArg.setObjectType(entity.getObjectType());
            }
            objectGroupRelationVisibleManager.saveGroupRelation(ea, visibleArg, objectType);
        }
        return Result.newSuccess();
    }

    public Result deleteGroup(String ea, Integer fsUserId, String groupId, Integer objectType) {
        ObjectGroupEntity entity = objectGroupDAO.queryById(ea, groupId);
        if (entity == null) {
            log.info("ObjectGroupManager.deleteGroup group failed group not exist ea:{} fsUserId:{} groupId:{}", ea, fsUserId, groupId);
            return Result.newError(SHErrorCode.OBJECT_GROUP_NOT_EXISTS);
        }

        // 校验能否删除
        List<ObjectGroupEntity> subGroupEntities = objectGroupDAO.queryByParentId(ea, groupId);
        if (CollectionUtils.isNotEmpty(subGroupEntities)) {
            return Result.newError(SHErrorCode.OBJECT_GROUP_DELETE_FAIL_EXISTS_SUB);
        }

        List<ObjectGroupRelationEntity> objectGroupRelationEntities;
        if (Objects.equals(objectType, ObjectTypeEnum.HEXAGON_SITE.getType())) {
            // 微页面特殊处理，需要过滤掉状态为5不可见的数据
            objectGroupRelationEntities = objectGroupRelationDAO.queryByGroupId4Hexagon(ea, groupId);
        } else {
            objectGroupRelationEntities = objectGroupRelationDAO.queryByGroupId(ea, groupId);
        }
        if (CollectionUtils.isNotEmpty(objectGroupRelationEntities)) {
            return Result.newError(SHErrorCode.OBJECT_GROUP_DELETE_FAIL_EXISTS_OBJECT);
        }

        objectGroupDAO.deleteById(ea, groupId);
        objectGroupRelationDAO.deleteByGroupId(ea, groupId);
        objectGroupRelationVisibleManager.deleteVisibleByGroupId(groupId);
        return Result.newSuccess();
    }

    /**
     * 当前人员是否为营销通管理员
     */
    public boolean isAppAdmin(String ea, Integer userId) {
        boolean appAdmin = false;
        String fsUserId = "E." + ea + "." + userId;
        BaseResult<Boolean> baseResult = openAppAdminService.isAppAdmin(fsUserId, appId);
        if (!baseResult.isSuccess()) {
            log.warn("ObjectGroupManager.isAppAdmin failed, ea={}, userId={}", ea, userId);
        }
        if (BooleanUtils.isTrue(baseResult.getResult())) {
            appAdmin = true;
        }
        return appAdmin;
    }

    public ObjectGroupEntity getById(String ea, String groupId) {
        return objectGroupDAO.getById(ea, groupId);
    }

    public List<ObjectGroupEntity> getByIdList(String ea, List<String> groupIdList) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(groupIdList)) {
            return objectGroupDAO.getByIdList(ea, groupIdList);
        }
        return Lists.newArrayList();
    }

    /**
     * 查询移动端展示的分组
     *
     * @param ea
     * @param groupIdList
     * @return
     */
    public List<ObjectGroupEntity> getDisplayByIdList(String ea, List<String> groupIdList) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(groupIdList)) {
            return objectGroupDAO.getDisplayByIdList(ea, groupIdList);
        }
        return Lists.newArrayList();
    }

    public List<ObjectGroupEntity> listGroupByEa(String ea, Integer objectType) {
        return objectGroupDAO.listGroupByEa(ea, objectType);
    }

    public Integer sortObjectGroup(String ea, String displayKey, List<ObjectGroupDTO> objectGroupEntityList) {
        DisplayOrderEntity templateGroupDisplayOrder = displayOrderManager.getDisplayOrder(ea, displayKey);
        if (templateGroupDisplayOrder != null) {
            NestedIdList.sortByNestedIds(templateGroupDisplayOrder.getDisplayItems(), objectGroupEntityList, ObjectGroupEntity::getId);
            return templateGroupDisplayOrder.getVersion();
        }
        return null;
    }

    // 获取用户自定义的分组 只会返回有权限查看的分组 并且排好序的
    public ObjectGroupListResult getShowGroup(String ea, Integer fsUserId, Integer objectType, String menuId, Integer status) {
        ObjectGroupListResult result;
        // 菜单id是空的，直接走以前的权限逻辑
        if (StringUtils.isBlank(menuId)) {
            List<ObjectGroupDTO> objectGroupDTOList = objectGroupRelationVisibleManager.getShowGroup(ea, fsUserId, objectType, false);
            result = fillResult(ea, fsUserId, objectType, objectGroupDTOList, status);
        } else {
            // 获取菜单对应的分组
            Result<List<String>> menuObjectGroupResult = appMenuTemplateService.getMenuObjectGroupRule(ea, fsUserId, menuId, objectType);
            if (!menuObjectGroupResult.isSuccess()) {
                ObjectGroupListResult vo = new ObjectGroupListResult();
                vo.setObjectGroupList(Lists.newArrayList());
                return vo;
            }
            // 组装分组展示信息
            List<String> objectGroupIdList = menuObjectGroupResult.getData();
            List<ObjectGroupDTO> objectGroupDTOList = objectGroupRelationVisibleManager.buildObjectGroupDTOByAccessibleGroupId(ea, objectGroupIdList, false);
            result = fillResult(ea, null, objectType, objectGroupDTOList, status);
        }
        if (CollectionUtils.isNotEmpty(result.getObjectGroupList())) {
            // 这里做一个统一的排序，前端那边会重新排序一遍，这里我们只要保证每个端返回的顺序一致即可
            result.getObjectGroupList().sort(Comparator.comparing(ListObjectGroupResult::getCreateTime).reversed());
        }
        return result;
    }

    /**
     * 获取外部有权限的分组
     *
     * @param upstreamEA
     * @param outTenantId
     * @param outUserId
     * @param objectType
     * @return
     */
    public ObjectGroupListResult getShowGroup4Outer(String upstreamEA, String outTenantId, String outUserId, Integer objectType) {
        List<ObjectGroupDTO> objectGroupDTOList = objectGroupRelationVisibleManager.getShowGroup4Outer(upstreamEA, outTenantId, outUserId, objectType);
        return fillResult(upstreamEA, null, objectType, objectGroupDTOList, null);
    }

    /**
     * 填充物料分组可见范围（名称）
     *
     * @param ea
     * @param objectType
     * @param objectGroupDTOList
     * @return
     */
    public ObjectGroupListResult fillResult(String ea, Integer fsUserId, Integer objectType, List<ObjectGroupDTO> objectGroupDTOList, Integer status) {
        ObjectGroupListResult vo = new ObjectGroupListResult();
        List<ListObjectGroupResult> groupResultList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(objectGroupDTOList)) {
            List<String> groupIds;
            if (fsUserId != null) {
                // 有权限的分组id,分组数量只能查询有权限的分组数量
                List<ObjectGroupEntity> accessibleGroupList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, objectType);
                groupIds = accessibleGroupList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            } else {
                groupIds = objectGroupDTOList.stream().filter(e -> e.getIsVisible() != null && e.getIsVisible() == 1).map(ObjectGroupEntity::getId).collect(Collectors.toList());
            }
            // 统计各个分组的数量
            Map<String, Integer> contentGroupCountMap = new HashMap<>();
            List<ContentGroupCountDTO> contentGroupCountDTOList = Lists.newArrayList();
            if (Objects.equals(objectType, ObjectTypeEnum.HEXAGON_SITE.getType())) {
                // 微页面特殊处理，需要过滤掉不可见状态的微页面
                contentGroupCountDTOList = objectGroupRelationDAO.queryObjectCountByGroups4Hexagon(ea, objectType, groupIds);
            } else if (Objects.equals(objectType, ObjectTypeEnum.HEXAGON_TEMPLATE.getType())) {
                // 微页面模板特殊处理，需要过滤掉不可见状态的微页面模板
                contentGroupCountDTOList = objectGroupRelationDAO.queryObjectCountByGroups4HexagonTemplate(ea, objectType, groupIds);
            } else if (Objects.equals(objectType, ObjectTypeEnum.ARTICLE.getType())) {
                contentGroupCountDTOList = objectGroupRelationDAO.queryArticleCountByGroups(ea, groupIds, status);
            } else if (Objects.equals(objectType, ObjectTypeEnum.PRODUCT.getType())) {
                contentGroupCountDTOList = objectGroupRelationDAO.queryProductCountByGroups(ea, groupIds, status);
            } else if (Objects.equals(objectType, ObjectTypeEnum.VIDEO.getType())) {
                contentGroupCountDTOList = objectGroupRelationDAO.queryVideoCountByGroups(ea, groupIds, status);
            } else {
                contentGroupCountDTOList = objectGroupRelationDAO.queryObjectCountByGroups(ea, objectType, groupIds);
            }
            if (CollectionUtils.isNotEmpty(contentGroupCountDTOList)) {
                contentGroupCountMap = contentGroupCountDTOList.stream().collect(Collectors.toMap(ContentGroupCountDTO::getGroupId, ContentGroupCountDTO::getCount, (v1, v2) -> v2));
            }
            // 排序分组，短期兼容历史逻辑，后面这里的排序可以去掉
            Integer sortVersion = sortObjectGroup(ea, getDisplayKey(objectType), objectGroupDTOList);
            vo.setSortVersion(sortVersion);
            for (ObjectGroupDTO objectGroupDTO : objectGroupDTOList) {
                String groupId = objectGroupDTO.getId();
                ListObjectGroupResult groupResult = new ListObjectGroupResult();
                groupResult.setGroupId(groupId);
                groupResult.setGroupName(objectGroupDTO.getName());
                int objectCount = calculateGroupCount(objectGroupDTO, objectGroupDTOList, contentGroupCountMap);
                groupResult.setObjectCount(objectCount);
                groupResult.setParentId(objectGroupDTO.getParentId());
                groupResult.setSeqNo(objectGroupDTO.getSeqNo());
                groupResult.setLevel(objectGroupDTO.getLevel());
                groupResult.setIsVisible(objectGroupDTO.getIsVisible());
                groupResult.setCreateTime(objectGroupDTO.getCreateTime());
                groupResultList.add(groupResult);
            }
        }
        vo.setObjectGroupList(groupResultList);
        return vo;
    }

    // 获取当前分组及子分组的数量
    private int calculateGroupCount(ObjectGroupDTO objectGroup, List<ObjectGroupDTO> objectGroupDTOList, Map<String, Integer> groupIdToCountMap) {
        if (objectGroup.getLevel() == null) {
            return groupIdToCountMap.getOrDefault(objectGroup.getId(), 0);
        }
        int totalCount = 0;
        Stack<String> stack = new Stack<>();
        stack.push(objectGroup.getId());
        Map<String, List<ObjectGroupDTO>> parentIdToSubGroupMap = objectGroupDTOList.stream().filter(e -> StringUtils.isNotBlank(e.getParentId())).collect(Collectors.groupingBy(ObjectGroupDTO::getParentId));
        while (!stack.isEmpty()) {
            String currentGroupId = stack.pop();
            totalCount += groupIdToCountMap.getOrDefault(currentGroupId, 0);
            List<ObjectGroupDTO> subGroupList = parentIdToSubGroupMap.get(currentGroupId);
            if (CollectionUtils.isNotEmpty(subGroupList)) {
                subGroupList.forEach(e -> stack.push(e.getId()));
            }
        }
        return totalCount;
    }

    /**
     * 填充可见范围（角色/部门/企业组/互联企业）
     *
     * @param ea
     * @param fsUserId
     * @param groupResultList
     */
    private void fillVisibleNames(String ea, Integer fsUserId, List<ListObjectGroupResult> groupResultList) {
        if (CollectionUtils.isEmpty(groupResultList)) {
            return;
        }
        List<String> groupIds = groupResultList.stream().map(ListObjectGroupResult::getGroupId).collect(Collectors.toList());
        // 查询角色映射
        List<ObjectGroupRoleRelationEntity> groupRoleRelationEntities = objectGroupRelationVisibleManager.getRoleRelationByGroupIdList(groupIds);
        Map<String, List<ObjectGroupRoleRelationEntity>> roleRelationEntityMap = Maps.newHashMap();
        Map<String, RoleVo> roleVoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(groupRoleRelationEntities)) {
            Set<String> roleIdSet = groupRoleRelationEntities.stream().map(ObjectGroupRoleRelationEntity::getRoleId).collect(Collectors.toSet());
            List<RoleVo> roleVoList = userRoleManager.getRolesByRoleCodeList(ea, Lists.newArrayList(roleIdSet));
            roleVoMap = roleVoList.stream().collect(Collectors.toMap(RoleVo::getRoleCode, e -> e, (v1, v2) -> v1));
            roleRelationEntityMap = groupRoleRelationEntities.stream().collect(Collectors.groupingBy(ObjectGroupRoleRelationEntity::getGroupId));
        }

        // 查询部门映射
        List<ObjectGroupDepartmentRelationEntity> groupDepartmentRelationEntities = objectGroupRelationVisibleManager.getDepartmentRelationByGroupIdList(groupIds);
        Map<String, List<ObjectGroupDepartmentRelationEntity>> departmentRelationEntityMap = Maps.newHashMap();
        Map<Integer, DepartmentDto> departmentMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(groupDepartmentRelationEntities)) {
            Set<Integer> departmentIds = groupDepartmentRelationEntities.stream().map(ObjectGroupDepartmentRelationEntity::getDepartmentId).collect(Collectors.toSet());
            List<DepartmentDto> departmentDtos = authManager.batchGetByDepartmentIds(eieaConverter.enterpriseAccountToId(ea), Lists.newArrayList(departmentIds));
            departmentMap = departmentDtos.stream().collect(Collectors.toMap(DepartmentDto::getDepartmentId, e -> e, (v1, v2) -> v1)); // 部门映射
            departmentRelationEntityMap = groupDepartmentRelationEntities.stream().collect(Collectors.groupingBy(ObjectGroupDepartmentRelationEntity::getGroupId));// 分组id和部门集合的映射
        }

        // 查询企业组映射
        List<ObjectGroupOuterGroupRelationEntity> groupOuterGroupRelationEntities = objectGroupRelationVisibleManager.getOuterGroupRelationByGroupIdList(groupIds);
        Map<String, List<ObjectGroupOuterGroupRelationEntity>> outGroupRelationEntityMap = Maps.newHashMap();
        Map<String, TenantGroupResult> outGroupMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(groupOuterGroupRelationEntities)) {
            Set<String> outGroupIds = groupOuterGroupRelationEntities.stream().map(ObjectGroupOuterGroupRelationEntity::getOuterGroupId).collect(Collectors.toSet());
            List<TenantGroupResult> tenantGroupResults = authPartnerManager.batchGetTenantGroupByIds(ea, Lists.newArrayList(outGroupIds));
            outGroupMap = tenantGroupResults.stream().collect(Collectors.toMap(TenantGroupResult::getId, e -> e, (v1, v2) -> v1)); // 企业组映射
            outGroupRelationEntityMap = groupOuterGroupRelationEntities.stream().collect(Collectors.groupingBy(ObjectGroupOuterGroupRelationEntity::getGroupId));// 分组id和企业组集合的映射
        }

        // 查询互联企业映射
        List<ObjectGroupOuterTenantRelationEntity> groupOuterTenantRelationEntities = objectGroupRelationVisibleManager.getOuterTenantRelationByGroupIdList(groupIds);
        Map<String, List<ObjectGroupOuterTenantRelationEntity>> outTenantRelationEntityMap = Maps.newHashMap();
        Map<String, ObjectData> outTenantMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(groupOuterTenantRelationEntities)) {
            Set<String> outTenantIds = groupOuterTenantRelationEntities.stream().map(ObjectGroupOuterTenantRelationEntity::getOuterTenantId).collect(Collectors.toSet());
            List<ObjectData> objectDataList = authPartnerManager.batchGetOutTenantByIds(ea, fsUserId, Lists.newArrayList("_id", "name"), Lists.newArrayList(outTenantIds));
            outTenantMap = objectDataList.stream().collect(Collectors.toMap(ObjectData::getId, e -> e, (v1, v2) -> v1)); // 企业组映射
            outTenantRelationEntityMap = groupOuterTenantRelationEntities.stream().collect(Collectors.groupingBy(ObjectGroupOuterTenantRelationEntity::getGroupId));// 分组id和互联集合的映射
        }

        for (ListObjectGroupResult groupResult : groupResultList) {
            String groupId = groupResult.getGroupId();
            // 填充角色
            List<ObjectGroupRoleRelationEntity> roleRelationEntities = roleRelationEntityMap.getOrDefault(groupId, Lists.newArrayList());
            List<String> roleNameList = Lists.newArrayList();
            if (roleRelationEntities.stream().anyMatch(e -> "ALL".equals(e.getRoleId()))) {
                roleNameList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491));
            } else {
                for (ObjectGroupRoleRelationEntity roleRelationEntity : roleRelationEntities) {
                    RoleVo roleVo = roleVoMap.get(roleRelationEntity.getRoleId());
                    if (roleVo != null) {
                        roleNameList.add(roleVo.getRoleName());
                    }
                }
            }
            groupResult.setRoleNameList(roleNameList);

            // 填充部门
            List<ObjectGroupDepartmentRelationEntity> departmentRelationEntities = departmentRelationEntityMap.getOrDefault(groupId, Lists.newArrayList());
            List<String> departmentNameList = Lists.newArrayList();
            if (departmentRelationEntities.stream().anyMatch(entity -> Objects.equals(entity.getDepartmentId(), AuthManager.defaultAllDepartment))) {
                departmentNameList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491));
            } else {
                for (ObjectGroupDepartmentRelationEntity entity : departmentRelationEntities) {
                    DepartmentDto departmentDto = departmentMap.get(entity.getDepartmentId());
                    if (departmentDto != null) {
                        departmentNameList.add(departmentDto.getName());
                    }
                }
            }
            groupResult.setDepartmentNameList(departmentNameList);

            // 填充企业组
            List<ObjectGroupOuterGroupRelationEntity> outerGroupRelationEntities = outGroupRelationEntityMap.getOrDefault(groupId, Lists.newArrayList());
            List<String> outGroupNameList = Lists.newArrayList();
            for (ObjectGroupOuterGroupRelationEntity entity : outerGroupRelationEntities) {
                TenantGroupResult tenantGroupResult = outGroupMap.get(entity.getOuterGroupId());
                if (tenantGroupResult != null) {
                    outGroupNameList.add(tenantGroupResult.getName());
                }
            }
            groupResult.setOutGroupNameList(outGroupNameList);

            // 填充互联企业
            List<ObjectGroupOuterTenantRelationEntity> outerTenantRelationEntities = outTenantRelationEntityMap.getOrDefault(groupId, Lists.newArrayList());
            List<String> outTenantNameList = Lists.newArrayList();
            for (ObjectGroupOuterTenantRelationEntity entity : outerTenantRelationEntities) {
                ObjectData data = outTenantMap.get(entity.getOuterTenantId());
                if (data != null) {
                    outTenantNameList.add(data.getName());
                }
            }
            groupResult.setOutTenantNameList(outTenantNameList);
        }
    }

    private String getDisplayKey(int objectType) {
        ObjectTypeEnum objectTypeEnum = ObjectTypeEnum.getByType(objectType);
        switch (objectTypeEnum) {
            case HEXAGON_SITE:
                return DisplayOrderConstants.HEXAGON_GROUP_DISPLAY_KEY;
            case COUPON_TEMPLATE:
                return DisplayOrderConstants.COUPON_TEMPLATE_GROUP_DISPLAY_KEY;
            case ARTICLE:
                return DisplayOrderConstants.ARTICLE_GROUP_DISPLAY_KEY;
            case CUSTOMIZE_FORM:
                return DisplayOrderConstants.CUSTOMIZE_FORM_GROUP_DISPLAY_KEY;
            case PRODUCT:
                return DisplayOrderConstants.PRODUCT_GROUP_DISPLAY_KEY;
            case QR_POSTER:
                return DisplayOrderConstants.QR_POSTER_GROUP_DISPLAY_KEY;
            case IMAGE:
                return DisplayOrderConstants.PHOTO_GROUP_DISPLAY_KEY;
            case FILE:
                return DisplayOrderConstants.FILE_GROUP_DISPLAY_KEY;
            case VIDEO:
                return DisplayOrderConstants.VIDEO_GROUP_DISPLAY_KEY;
            case QY_GROUP_CODE:
                return DisplayOrderConstants.QY_GROUP_CODE_GROUP_DISPLAY_KEY;
            case FAN_CODE:
                return DisplayOrderConstants.FAN_CODE_GROUP_DISPLAY_KEY;
            case USER_MARKETING_GROUP:
                return DisplayOrderConstants.MARKETING_USER_GROUP_DISPLAY_KEY;
            case QY_WELCOME_MSG:
                return DisplayOrderConstants.QY_WELCOME_MSG_GROUP_DISPLAY_KEY;
            case QY_SOP:
                return DisplayOrderConstants.QY_SOP_GROUP_DISPLAY_KEY;
            case QY_GROUP_SOP:
                return DisplayOrderConstants.QY_GROUP_SOP_GROUP_DISPLAY_KEY;
            default:
                return "";
        }
    }

    public List<ObjectGroupEntity> listMobileGroupByEa(String ea, int objectType, Integer mobileDisplay) {
        return objectGroupDAO.listMobileGroupByEa(ea, objectType, mobileDisplay);
    }

    public List<String> getDifferenceCollection(List<String> oldList, List<String> newList) {
        Map<String, String> map = oldList.stream().collect(Collectors.toMap(Function.identity(), Function.identity(), (u, v) -> u));
        return newList.stream().filter(o -> !map.containsKey(o)).collect(Collectors.toList());
    }

//    public ObjectGroupListResult getShowCustomizeMobileGroup(String ea, Integer fsUserId, Integer objectType) {
//        List<ObjectGroupDTO> objectGroupDTOList = objectGroupRelationVisibleManager.getShowGroup(ea, fsUserId, objectType, true);
//        return fillResult(ea, fsUserId, objectType, objectGroupDTOList, isAppAdmin(ea, fsUserId));
//    }

    public void setGroup(String ea, Integer fsUserId, Integer objectType, List<String> objectIdList, String groupId) {
        if (objectType != null && CollectionUtils.isNotEmpty(objectIdList) && StringUtils.isNotBlank(groupId)) {
            ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, groupId);
            if (objectGroupEntity != null) {
                objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, objectType, objectIdList);
                List<ObjectGroupRelationEntity> insertList = new ArrayList<>();
                for (String objectId : objectIdList) {
                    ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
                    newEntity.setId(UUIDUtil.getUUID());
                    newEntity.setEa(ea);
                    newEntity.setGroupId(groupId);
                    newEntity.setObjectId(objectId);
                    newEntity.setObjectType(objectType);
                    insertList.add(newEntity);
                }
                objectGroupRelationDAO.batchInsert(insertList);
                for (String objectId : objectIdList) {
                    mktContentMgmtLogObjManager.createByOperateTypeWithObjectId(ea, fsUserId, objectType, objectId, OperateTypeEnum.SET_GROUP, objectGroupEntity.getName());
                }
            }
        }
    }

    public List<ObjectGroupEntity> getAllGroupByObjectTypeListAndEa(String ea) {
        return objectGroupDAO.getAllGroupByObjectTypeListAndEa(ea);
    }

    public List<ObjectGroupEntity> getAllSubGroupWithoutPermission(String ea, List<String> groupIdList, Integer objectType) {
        if (CollectionUtils.isEmpty(groupIdList)) {
            return Lists.newArrayList();
        }
        List<ObjectGroupEntity> objectGroupEntityList = objectGroupDAO.listGroupByEa(ea, objectType);
        if (CollectionUtils.isEmpty(objectGroupEntityList)) {
            return Lists.newArrayList();
        }
        Queue<String> queue = new LinkedList<>();
        groupIdList.forEach(queue::offer);

        Map<String, List<ObjectGroupEntity>> parentIdToSubGroupMap = objectGroupEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getParentId())).collect(Collectors.groupingBy(ObjectGroupEntity::getParentId));
        List<ObjectGroupEntity> subGroups = new ArrayList<>();
        while (!queue.isEmpty()) {
            String currentId = queue.poll();
            List<ObjectGroupEntity> childrenList = parentIdToSubGroupMap.get(currentId);
            if (childrenList != null) {
                subGroups.addAll(childrenList);
                for (ObjectGroupEntity child : childrenList) {
                    queue.offer(child.getId());
                }
            }
        }
        return subGroups;
    }
}