package com.facishare.marketing.provider.baidu;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhengh on 2021/1/11.
 */
@Data
public class BaiduEqidRequestEntity <T extends IRequestBody> implements Serializable {
    @SerializedName("header")
    private BaiduEqidRequestHeader header;
    @SerializedName("body")
    private T body;

    @Data
    public static class BaiduEqidRequestHeader implements Serializable{
        @SerializedName("host")
        private String host;
        @SerializedName("x-bce-date")
        private String xBceDate;
        @SerializedName("authorization")
        private String authorization;
    }
}
