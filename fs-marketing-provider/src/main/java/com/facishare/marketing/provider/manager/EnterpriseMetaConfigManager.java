package com.facishare.marketing.provider.manager;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.enums.CardCommonSettingShowTypeEnum;
import com.facishare.marketing.common.enums.CardCommonSettingTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.CardCommonSetting;
import com.facishare.marketing.common.typehandlers.value.CardCommonSettingList;
import com.facishare.marketing.provider.dao.EnterpriseMetaConfigDao;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.google.common.collect.Lists;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created  By zhoux 2020/09/07
 **/
@Component
@Slf4j
public class EnterpriseMetaConfigManager {

    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    public CardCommonSettingList handleCardCommonSetting(String ea, CardCommonSettingList cardCommonSettings) {
        if (StringUtils.isBlank(ea)) {
            return new CardCommonSettingList();
        }
        if (CollectionUtils.isEmpty(cardCommonSettings)) {
            // 设置客户联系
            // 判断是否开通企业微信
            QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
            cardCommonSettings = new CardCommonSettingList();
            if (qywxCorpAgentConfigEntity == null) {
                cardCommonSettings.add(new CardCommonSetting(CardCommonSettingTypeEnum.CUSTOMER_CONTACT.getType(), CardCommonSettingShowTypeEnum.PERSONAL_COMMUNICATION.getType(), I18nUtil.get(I18nKeyEnum.MARK_CARDTEMPLATE_CARDTEMPLATEMANAGER_415), true));
            } else {
                cardCommonSettings.add(new CardCommonSetting(CardCommonSettingTypeEnum.CUSTOMER_CONTACT.getType(), CardCommonSettingShowTypeEnum.CONTACT_ME.getType(), I18nUtil.get(I18nKeyEnum.MARK_CARDTEMPLATE_CARDTEMPLATEMANAGER_415), true));
            }
            cardCommonSettings.add(new CardCommonSetting(CardCommonSettingTypeEnum.PRODUCT_STYLE.getType(), CardCommonSettingShowTypeEnum.PRODUCT_DEFAULT_STYLE.getType(), "", true));
           /* if (qywxCorpAgentConfigEntity == null) {
                cardCommonSettings = new CardCommonSettingList();
                cardCommonSettings.buildAllDisableData();
            } else {
                cardCommonSettings = new CardCommonSettingList();
                // 纷享与木里木外特殊处理先将"联系我"隐藏
                Boolean enableStatus = true;
                if (fsAddressBookManager.mustUseFxiaokeAddressBook(ea)) {
                    enableStatus = false;
                }
                cardCommonSettings.add(new CardCommonSetting(CardCommonSettingTypeEnum.CUSTOMER_CONTACT.getType(), CardCommonSettingShowTypeEnum.CONTACT_ME.getType(), I18nUtil.get(I18nKeyEnum.MARK_CARDTEMPLATE_CARDTEMPLATEMANAGER_415), enableStatus));
            }*/
        }
        return cardCommonSettings;
    }

    public Result updateCardCommonSettingCheck(String ea, List<CardCommonSetting> setting) {
        if (CollectionUtils.isEmpty(setting)) {
            return Result.newSuccess();
        }
        // 客户联系设置
        for (CardCommonSetting cardCommonSetting : setting) {
            if (cardCommonSetting.getType().equals(CardCommonSettingTypeEnum.CUSTOMER_CONTACT.getType())) {
                QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
                if (qywxCorpAgentConfigEntity == null && cardCommonSetting.getShowType().equals(CardCommonSettingShowTypeEnum.CONTACT_ME.getType())) {
                    return Result.newError(SHErrorCode.ENTERPRISE_NEED_BIND_QYWX);
                }
            }
        }
        return Result.newSuccess();
    }

}