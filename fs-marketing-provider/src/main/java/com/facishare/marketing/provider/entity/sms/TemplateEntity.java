package com.facishare.marketing.provider.entity.sms;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by zhengh on 2018/12/20.
 */
@Data
@Entity
public class TemplateEntity implements Serializable{
    private String id;                //主键
    private String ea;                //公司ea
    private Integer userId;            //提交审核的userId
    private String creator;           //提交审核的员工姓名
    private String appId;             //sdk appid
    private Integer applyId;          //申请模板，腾讯返回的id
    private String name;              //申请模板名称
    private String content;           //模板内容
    private Integer type;             //模板类型 0：普通短信   1：营销短信
    private String remark;            //申请模板remark
    private Integer status;           //申请状态
    private String reply;             //审核失败原因
    private Integer seqNum;            //模板序号
    private Date createTime;          //申请时间
    private Date updateTime;          //创建时间
}
