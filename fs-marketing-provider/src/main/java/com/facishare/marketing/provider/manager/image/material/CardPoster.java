package com.facishare.marketing.provider.manager.image.material;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.util.FontUtil;
import com.facishare.marketing.common.util.ImageUtil;
import com.facishare.marketing.provider.dao.CardDAO;
import com.facishare.marketing.provider.entity.CardEntity;
import com.facishare.marketing.provider.entity.FSBindEntity;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.FsBindManager;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.facishare.marketing.provider.manager.image.ImageDrawer;
import com.github.mybatis.util.UnicodeFormatter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.Map;

@Slf4j
@Component("cardPoster")
public class CardPoster implements ImageDrawer {

    @Autowired
    private CardDAO cardDAO;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private FsBindManager fsBindManager;

    @Override
    public String draw(Map<String, Object> params) {
        String uid = (String) params.get("uid");
        String ea = null;
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(uid);
        if (fsBindEntity != null) {
            ea = fsBindEntity.getFsEa();
        }
        if (StringUtils.isBlank(uid)) {
            return null;
        }

        try {

            // 获取头像
            CardEntity cardEntity = cardDAO.queryCardInfoByUid(uid);
            if (null == cardEntity) {
                return null;
            }

            List<PhotoEntity> photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.CARD_QRCODE.getType(), uid);
            if (CollectionUtils.isEmpty(photoEntityList)) {
                return null;
            }

            PhotoEntity qrCodePhotoEntity = photoEntityList.get(0);

            final int PIC_SIZE_MULTIPLE = 3;
            final int IMAGE_WIDTH_LIMIT = 1035 * PIC_SIZE_MULTIPLE;
            final int IMAGE_HEIGTH_LIMIT = 1350 * PIC_SIZE_MULTIPLE;


            // 下载头像
            byte[] avatarBytes = fileV2Manager.downloadFileByUrl(cardEntity.getAvatar(), null);
            if (null == avatarBytes || avatarBytes.length < 0) {
                return null;
            }

            // 读取头像
            ByteArrayInputStream avatarIn = new ByteArrayInputStream(avatarBytes);
            BufferedImage avatarBufferdImage = ImageIO.read(avatarIn);
            if (null == avatarBufferdImage) {
                return null;
            }

            avatarBufferdImage = ImageUtil.scale(avatarBufferdImage, 210*PIC_SIZE_MULTIPLE, true);
            avatarBufferdImage = ImageUtil.cropCircular(avatarBufferdImage, avatarBufferdImage.getWidth(), avatarBufferdImage.getWidth()/2);

            // 下载小程序码
            byte[] qrBytes = fileV2Manager.downloadAFile(qrCodePhotoEntity.getPath(), ea);
            if (null == qrBytes || qrBytes.length < 0) {
                return null;
            }

            // 读取小程序码
            ByteArrayInputStream qrIn = new ByteArrayInputStream(qrBytes);
            BufferedImage qrBufferdImage = ImageIO.read(qrIn);
            if (null == qrBufferdImage) {
                return null;
            }



            // ---开始绘制---

            // 绘制空白画布
            BufferedImage bgBufferedImage = new BufferedImage(IMAGE_WIDTH_LIMIT, IMAGE_HEIGTH_LIMIT, BufferedImage.TYPE_INT_RGB);
            Graphics2D bgG2d = bgBufferedImage.createGraphics();
            bgG2d.drawImage(bgBufferedImage, 0, 0, Color.WHITE, null);
            bgG2d.setBackground(Color.WHITE);
            bgG2d.clearRect(0, 0, IMAGE_WIDTH_LIMIT, IMAGE_HEIGTH_LIMIT);

            bgG2d.drawImage(avatarBufferdImage, 75*PIC_SIZE_MULTIPLE, 90*PIC_SIZE_MULTIPLE, avatarBufferdImage.getWidth(), avatarBufferdImage.getHeight(), null);
            bgG2d.drawImage(qrBufferdImage, 263*PIC_SIZE_MULTIPLE, 555*PIC_SIZE_MULTIPLE, 510*PIC_SIZE_MULTIPLE, 510*PIC_SIZE_MULTIPLE, null);
            bgG2d.drawImage(avatarBufferdImage, 405*PIC_SIZE_MULTIPLE, 696*PIC_SIZE_MULTIPLE, 230*PIC_SIZE_MULTIPLE, 230*PIC_SIZE_MULTIPLE, null);
            bgG2d.dispose();

            FontUtil.drawString(bgBufferedImage,
                    new Font(FontUtil.PingFangRegular, Font.PLAIN, 60 * PIC_SIZE_MULTIPLE),
                    new Color(51, 51, 51),
                    new Rectangle(332*PIC_SIZE_MULTIPLE, 90*PIC_SIZE_MULTIPLE, 400*PIC_SIZE_MULTIPLE, 84*PIC_SIZE_MULTIPLE),
                    0, 1, UnicodeFormatter.decodeUnicodeString(cardEntity.getName()), false, 0);

            FontUtil.drawString(bgBufferedImage,
                    new Font(FontUtil.PingFangRegular, Font.PLAIN, 45 * PIC_SIZE_MULTIPLE),
                    new Color(102, 102, 102),
                    new Rectangle(332*PIC_SIZE_MULTIPLE, 173*PIC_SIZE_MULTIPLE, 570*PIC_SIZE_MULTIPLE, 63*PIC_SIZE_MULTIPLE),
                    0, 1, UnicodeFormatter.decodeUnicodeString(cardEntity.getVocation()), true, 0);

            FontUtil.drawString(bgBufferedImage,
                    new Font(FontUtil.PingFangRegular, Font.PLAIN, 45 * PIC_SIZE_MULTIPLE),
                    new Color(102, 102, 102),
                    new Rectangle(332*PIC_SIZE_MULTIPLE, 240*PIC_SIZE_MULTIPLE, 700*PIC_SIZE_MULTIPLE, 63*PIC_SIZE_MULTIPLE),
                    0, 1, UnicodeFormatter.decodeUnicodeString(cardEntity.getCompanyName()), true, 0);

            // 虚线
            Graphics2D dottedLineG2d = bgBufferedImage.createGraphics();
            dottedLineG2d.setColor(new Color(177,184,190));
            Stroke bs = new BasicStroke(1*PIC_SIZE_MULTIPLE, BasicStroke.CAP_BUTT, BasicStroke.JOIN_BEVEL, 0, new float[]{10*PIC_SIZE_MULTIPLE, 5*PIC_SIZE_MULTIPLE},0);
            dottedLineG2d.setStroke(bs);
            dottedLineG2d.drawLine(42*PIC_SIZE_MULTIPLE, 393*PIC_SIZE_MULTIPLE, 993*PIC_SIZE_MULTIPLE, 393*PIC_SIZE_MULTIPLE);
            dottedLineG2d.dispose();

            FontUtil.drawString(bgBufferedImage,
                    new Font(FontUtil.PingFangRegular, Font.PLAIN, 36 * PIC_SIZE_MULTIPLE),
                    new Color(102, 102, 102),
                    new Rectangle(266*PIC_SIZE_MULTIPLE, 1256*PIC_SIZE_MULTIPLE, 504*PIC_SIZE_MULTIPLE, 50*PIC_SIZE_MULTIPLE),
                    0, 1, I18nUtil.get(I18nKeyEnum.MARK_MATERIAL_CARDPOSTER_147), false, 0);

            byte[] bytes = ImageUtil.image2Bytes(bgBufferedImage, "jpg");
//            bytes = ImageUtil.zoom(bytes, 1024);

            String tapath = fileV2Manager.uploadToTApath(bytes, "jpg", null, null);
            if (StringUtils.isBlank(tapath)) {
                return null;
            }

            return tapath;

        } catch (Exception e) {
            log.error("CardPoster.draw exception: ", e);
            return null;
        }
    }

}