package com.facishare.marketing.provider.manager.mail.arg;

import lombok.Data;

import java.io.File;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by zhengh on 2020/6/3.
 */
@Data
public class SendMailReq implements Serializable{
    private String apiUser;
    private String apiKey;
    private String from;
    private List<String> to;
    private String subject;
    private String html;
    private String contentSummary;
    private String fromName;
    private List<String> cc;
    private List<String> bcc;
    private List<String> replyTo;
    private String labelId;
    private String templateInvokeName;
    private Map<String, Object> headers;
    private List<Attachment> attachments;
    private String xsmtpapi;
    private String plain;
    private boolean respEmailId;
    private boolean useNotification;
    private boolean useAddressList;

    @Data
    public static class Attachment implements Serializable{
        private File file;
        private String name;
    }
}
