package com.facishare.marketing.provider.dao.sms;

import com.facishare.marketing.provider.entity.sms.SMSSendDetailEntity;
import com.facishare.marketing.provider.entity.sms.SMSSendEntity;
import com.facishare.marketing.provider.entity.sms.SMSSendSumEntity;
import com.github.mybatis.pagination.Page;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * @创建人 zhengliy
 * @创建时间 2018/12/24 20:02
 * @描述
 */
public interface SendDao {


    @Insert("insert into sms_send (\"id\", \"ea\", \"user_id\", \"creator\", \"sinature_id\", \"template_id\", \"status\", \"actual_sender_count\", \"to_sender_count\", \"consumer_count\", \"type\", "
        + " \"schedule_time\", \"create_time\", \"update_time\", \"host_name\") values("
        + " #{obj.id},\n"
        + " #{obj.ea},\n"
        + " #{obj.userId},\n"
        + " #{obj.creator},\n"
        + " #{obj.sinatureId},\n"
        + " #{obj.templateId},\n"
        + " #{obj.status},\n"
        + " #{obj.actualSenderCount},\n"
        + " #{obj.toSenderCount},\n"
        + " #{obj.consumerCount},\n"
        + " #{obj.type},\n"
        + " #{obj.scheduleTime},\n"
        + " now(),\n"
        + " now(),\n"
        + " #{obj.hostName}\n"
        + ") ON CONFLICT DO NOTHING;")
    void insert2Send(@Param("obj") SMSSendEntity smsSendEntity);

    @Update("update sms_send set "
        + "\"ea\" = #{obj.ea}, "
        + "\"user_id\" = #{obj.userId} , "
        + "\"creator\" = #{obj.creator}, "
        + "\"sinature_id\" = #{obj.sinatureId}, "
        + "\"template_id\" = #{obj.templateId}, "
        + "\"status\" = #{obj.status}, "
        + "\"actual_sender_count\" = #{obj.actualSenderCount}, "
        + "\"to_sender_count\" = #{obj.toSenderCount}, "
        + "\"consumer_count\" = #{obj.consumerCount}, "
        + "\"type\" = #{obj.type},"
        + "\"schedule_time\"=  #{obj.scheduleTime}, "
        + "\"create_time\" = now(), "
        + "\"update_time\" = now(), "
        + "\"host_name\" = #{obj.hostName}"
        + "  where \"id\" = #{obj.id}")
    void updateSMSSend(@Param("obj") SMSSendEntity smsSendEntity);

    @Select("<script>"
        +"select s.ea , s.id, s.create_time, s.template_id, s.update_time,  s.sinature_id , "
        + "s.creator, s.actual_sender_count, \n"
        + "s.to_sender_count, s.status , \n"
        + "s.consumer_count, s.type , \n"
        + "st.content, s.result_code \n"
        + "from sms_send as s ,\n"
        + "sms_template as st \n" + " WHERE s.template_id = st.id  "
        + "<if test=\"startTime != null\">\n  and s.create_time &gt;= #{startTime} </if>   "
        + " <if test=\"endTime != null\">\n and s.create_time &lt;= #{endTime} </if>\n"
        + "  <if test=\"creator != null\"> and  s.creator = #{creator}  </if>  "
        + "<if test=\"searchText != null\">  and st.\"content\" like '%'||#{searchText}||'%' </if>"
        + "  and s.ea = #{ea} order by s.create_time desc"
        + "</script>")
    List<SMSSendSumEntity> querySMSSendSum(@Param("ea") String ea, @Param("page") Page page, @Param("searchText") String searchText, @Param("creator") String creator, @Param("startTime") Date startTime, @Param("endTime") Date endTime);


    @Update("update sms_send set status = #{status}, schedule_time = #{scheduleTime} where id = #{id}")
    void updateStatusAndScheduleOfSMSSendById(@Param("status")Integer status, @Param("id")String id, @Param("scheduleTime") Date scheduleTime);

    @Update("update sms_send set consumer_count = #{consumerCount} where id = #{id}")
    void updateConsumerCountOfSMSSendById(@Param("consumerCount")Integer consumerCount, @Param("id")String id);

    @Update("update sms_send set actual_sender_count = #{actualSenderCount} where id = #{id}")
    void updateactualSenderCountOfSMSSendById(@Param("actualSenderCount")Integer actualSenderCount, @Param("id")String id);


    @Update("update sms_send set status = #{status} where id = #{id}")
    void updateStatusOfSMSSendById(@Param("status")Integer status, @Param("id")String id);

    @Update("update sms_send set status = #{status}, consumer_count = #{consumerCount} where id = #{id}")
    void updateStatusAndConsumerCountOfSMSSendById(@Param("status")Integer status, @Param("id")String id, @Param("consumerCount") Integer consumerCount);

    @Update("update sms_send set status = #{status}, consumer_count = #{consumerCount}, schedule_time = #{scheduleTime} where id = #{id}")
    void updateStatusAndConsumerCountAndScheduleTimeOfSMSSendById(@Param("status")Integer status, @Param("id")String id, @Param("consumerCount") Integer consumerCount, @Param("scheduleTime") Date scheduleTime);


    @Update("update sms_detail set fee = #{fee} where id = #{id}")
    void updateFeeOfSMSSendDetailById(@Param("id")String id, @Param("fee")Integer fee);


    @Select("select * from sms_send where id = #{id}")
    SMSSendEntity getSMSSendById( @Param("id") String id);

    @Select(
    "select sms.id, \n"
        +"sms.ea, \n"
        + "sms.user_id, \n"
        + "sms.creator, \n"
        + "sms.sinature_id,\n"
        + "sms.template_id, \n"
        + "sms.status,  \n"
        + "sms.actual_sender_count, \n"
        + "sms.to_sender_count, \n"
        + "sms.consumer_count, sms.type, \n"
        + "sms.schedule_time, \n"
        + "sms.create_time, \n"
        + "sms.update_time, \n"
        + "sms.host_name  \n"
        + "from sms_send  sms "
        + " JOIN sms_template st on sms.template_id = st.\"id\""
        + "where st.apply_id = #{templateId}  and  sms.status = #{status}")
    List<SMSSendEntity> getSMSSendByTemplateIdAndStatus(@Param("templateId") Integer templateId, @Param("status") Integer status);

    @Update("update sms_detail set sid = #{obj.sid}, status = #{obj.status}, error_msg = #{obj.errorMsg}, fee_tx = #{obj.feeTx}, result_code = #{obj.resultCode} where id = #{obj.id}")
    void updateStatusOfSMSSendDetailId(@Param("obj") SMSSendDetailEntity smsSendDetailEntity);

    @Select("select * from sms_detail where sms_send_id = #{smsSendId}")
    List<SMSSendDetailEntity> getSMSSendDetailBySmsSendId(@Param("smsSendId") String smsSendId);

    @Select("select * from sms_detail where sms_send_id = #{smsSendId} and phone = #{phone}")
    List<SMSSendDetailEntity> getSMSSendDetailBySmsSendIdAndPhone(@Param("smsSendId") String smsSendId, @Param("phone") String phone);


    @Select("select count(*) from sms_detail where sms_send_id = #{smsSendId} and status = #{status}")
    Integer getSMSSendDetailCountBySmsSendIdAndStatus(@Param("smsSendId") String smsSendId, @Param("status") Integer status);

    @Select("select * from sms_detail where sms_send_id = #{smsSendId} and status = #{status}")
    List<SMSSendDetailEntity> getSMSSendDetailBySmsSendIdAndStatus(@Param("smsSendId") String smsSendId, @Param("status") Integer status);

   @Select("select * from sms_detail where sms_send_id = #{smsSendId} and status = #{status}")
    List<SMSSendDetailEntity> getSMSSendDetailBySmsSendIdAndStatus2Page(@Param("smsSendId") String smsSendId, @Param("status") Integer status,@Param("page") Page page);


    @Update("update sms_send set actual_sender_count = #{actualSenderCount} where id = #{smsSendId}")
    void updateActualSenderCountById(@Param("smsSendId") String smsSendId, @Param("actualSenderCount") Integer actualSenderCount);

    @Select("select * from sms_send where status = #{status}")
    List<SMSSendEntity> getSMSSendByStatus(@Param("status") Integer status);

    @Select("select * from sms_send where status = #{status} and host_name = #{hostName} order by schedule_time asc, create_time asc limit #{pageSize}")
    List<SMSSendEntity> querySMSSendByStatus(@Param("status") Integer status, @Param("hostName") String hostName, @Param("limit") Integer limit);


    @Insert("<script>" + "INSERT INTO sms_detail (\"id\",\"sms_send_id\",\"phone\",\"status\",\"fee\",\"fee_tx\",\"params\") VALUES\n"
        + "    <foreach collection=\"entities\" item=\"entity\" index=\"index\" separator=\",\">\n"
        + "      (#{entity.id}, #{entity.smsSendId}, #{entity.phone}, #{entity.status}, #{entity.fee}, #{entity.feeTx},#{entity.params})\n" + "    </foreach>" + "</script>")
    void batchInsertSmsDetail(@Param("entities") Collection<SMSSendDetailEntity> entities);

    @Select("select * from sms_detail where sid = #{sid}")
    SMSSendDetailEntity querySMSSendDetailBySid(@Param("sid") String sid);

    @Delete("delete from sms_send where id = #{id}")
    void deleteSMSSendById(@Param("id") String id);

    @Delete("delete from sms_detail where sms_send_id = #{smsSendId}")
    void deleteSMSSendDetailById(@Param("smsSendId") String smsSendId);

    @Update("update sms_detail set report_status = #{obj.reportStatus}, report_err_msg = #{obj.reportErrMsg}, report_description = #{obj.reportDescription} where id = #{obj.id}")
    void updateSendDetailCallback(@Param("obj") SMSSendDetailEntity smsSendDetailEntity);

    @Update("update sms_send set result_code = #{resultCode} where id = #{id}")
    void updateSMSSendResultCode(@Param("resultCode") String resultCode, @Param("id") String id);

}
