package com.facishare.marketing.provider.manager.socialMedium;


import org.springframework.stereotype.Component;

import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.provider.manager.crmobjectcreator.AbstractObjManager;
import com.facishare.marketing.provider.util.ObjDescribeUtil;

@Component
public class SocialInteractionObjManager extends AbstractObjManager {

    @Override
    public String getApiName() {
        return CrmObjectApiNameEnum.SOCIAL_INTERACTION_OBJ.getName();
    }

    @Override
    public String getJsonData() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/social_medium/social_interaction_json_data.json");
    }

    @Override
    public String getJsonLayout() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/social_medium/social_interaction_json_layout.json");
    }

    @Override
    public String getJsonListLayout() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/social_medium/social_interaction_json_list_layout.json");
    }
    
}
