package com.facishare.marketing.provider.interceptor;

import com.facishare.marketing.common.exception.BusinessException;
import com.facishare.marketing.common.exception.MarketingException;
import com.facishare.marketing.common.exception.MarketingWithoutExceptionInfoException;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.google.common.base.Stopwatch;
import com.google.common.base.Strings;
import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldAttributes;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;

/**
 * <AUTHOR>
 */
@Slf4j
public class ExceptionToResultInterceptor {
    private static final Gson GSON = new GsonBuilder().addSerializationExclusionStrategy(new LogExclusionStrategy()).create();

    public Object around(ProceedingJoinPoint proceedingJoinPoint) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        String className = proceedingJoinPoint.getSignature().getDeclaringType().getSimpleName();
        String methodName = proceedingJoinPoint.getSignature().getName();
        String fullName = className + "#" + methodName;
        try {
            return proceedingJoinPoint.proceed();
        } catch (IllegalArgumentException iae) {
            if (stopwatch.isRunning()) {
                stopwatch.stop();
            }
            log.warn("executed ms:{},method:{}, args:{}, Throwable:", stopwatch.elapsed(TimeUnit.MILLISECONDS), fullName, printObjs(proceedingJoinPoint.getArgs()), iae);
            return new Result<>(SHErrorCode.PARAMS_ERROR.getErrorCode(), Strings.isNullOrEmpty(iae.getMessage()) ? SHErrorCode.PARAMS_ERROR.getErrorMessage():iae.getMessage());
        } catch (OuterServiceRuntimeException ose) {
            if (stopwatch.isRunning()) {
                stopwatch.stop();
            }
            log.warn("executed ms:{},method:{}, args:{}, Throwable:", stopwatch.elapsed(TimeUnit.MILLISECONDS), fullName, printObjs(proceedingJoinPoint.getArgs()), ose);
            return new Result<>(ose.getCode(), ose.getMessage());
        } catch (BusinessException be) {
            if (stopwatch.isRunning()) {
                stopwatch.stop();
            }
            log.warn("executed ms:{},method:{}, args:{}, Throwable:", stopwatch.elapsed(TimeUnit.MILLISECONDS), fullName, printObjs(proceedingJoinPoint.getArgs()), be);
            return new Result<>(be.getErrorCode(), be.getErrorMessage());
        }catch (MarketingException me) {
            if (stopwatch.isRunning()) {
                stopwatch.stop();
            }
            log.warn("executed ms:{},method:{}, args:{}, Throwable:", stopwatch.elapsed(TimeUnit.MILLISECONDS), fullName, printObjs(proceedingJoinPoint.getArgs()), me);
            return new Result<>(me.getErrorCode(), me.getErrorMessage());
        } catch (MarketingWithoutExceptionInfoException mweie) {
            if (stopwatch.isRunning()) {
                stopwatch.stop();
            }
            log.warn("executed ms:{},method:{}, args:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS), fullName, printObjs(proceedingJoinPoint.getArgs()));
            return new Result<>(mweie.getErrorCode(), mweie.getErrorMessage());
        } catch (CrmBusinessException restapiCbe) {
            if (stopwatch.isRunning()) {
                stopwatch.stop();
            }
            log.warn("executed ms:{},method:{}, args:{}, Throwable:", stopwatch.elapsed(TimeUnit.MILLISECONDS), fullName, printObjs(proceedingJoinPoint.getArgs()), restapiCbe);
            return new Result<>(SHErrorCode.SYSTEM_ERROR.getErrorCode(), restapiCbe.getMessage());
        } catch (Throwable t) {
            if (stopwatch.isRunning()) {
                stopwatch.stop();
            }
            if (t instanceof RuntimeException) {
                log.warn("executed ms:{},method:{}, args:{}, Throwable:", stopwatch.elapsed(TimeUnit.MILLISECONDS), fullName, printObjs(proceedingJoinPoint.getArgs()), t);
            } else {
                log.error("executed ms:{},method:{}, args:{}, Throwable:", stopwatch.elapsed(TimeUnit.MILLISECONDS), fullName, printObjs(proceedingJoinPoint.getArgs()), t);
            }
            return new Result<>(SHErrorCode.SYSTEM_ERROR.getErrorCode(), SHErrorCode.SYSTEM_ERROR.getErrorMessage());
        }
    }

    private static String printObjs(Object[] objs) {
        List<Object> objects = new ArrayList<>();
        for (Object obj : objs) {
            if (obj instanceof Serializable) {
                if (obj instanceof byte[]) {
                    objects.add("-byte[]-");
                } else {
                    objects.add(obj);
                }
            } else if (obj == null) {
                objects.add("-null-");
            } else {
                objects.add("-unserializable-");
            }
        }
        String json = GSON.toJson(objects);
//        if (json.length() > 1024) {
//            return json.substring(0, 1024)+"..ignore more..";
//        }
        return json;
    }

    private static class LogExclusionStrategy implements ExclusionStrategy {
        @Override
        public boolean shouldSkipField(FieldAttributes f) {
            return false;
        }

        @Override
        public boolean shouldSkipClass(Class<?> clazz) {
            return byte[].class == clazz;
        }
    }
}
