package com.facishare.marketing.provider.remote.rest.arg;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class UpdateDescribeArg implements Serializable {

    private boolean active = true;
    @SerializedName("include_layout")
    private boolean includeLayout = false;
    @SerializedName("json_data")
    private String jsonData;
    @SerializedName("json_layout")
    private String jsonLayout = "{}";
    @SerializedName("layout_type")
    private String layout_type = "detail";
    @SerializedName("describe_extra")
    private Map<String, Object> describeExtra;

}
