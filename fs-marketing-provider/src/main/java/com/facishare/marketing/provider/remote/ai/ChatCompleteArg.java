package com.facishare.marketing.provider.remote.ai;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChatCompleteArg implements Serializable {

    private String model;
    private Integer maxTokens;
    private Double temperature;
    private List<Message> messages;

    public ChatCompleteArg(List<Message> messages) {
        this.messages = messages;
    }

    public ChatCompleteArg(Double temperature, List<Message> messages) {
        this.temperature = temperature;
        this.messages = messages;
    }

    @Data
    @AllArgsConstructor
    public static class Message implements Serializable {
        private String role;
        private String content;
    }


    @Getter
    public enum Role {
        SYSTEM("system"),
        ASSISTANT("assistant"),
        USER("user");
        String role;

        Role(String role) {
            this.role = role;
        }
    }

}
