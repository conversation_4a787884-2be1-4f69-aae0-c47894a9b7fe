package com.facishare.marketing.provider.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MarketingGroupUserActionEntity implements Serializable {
    private String id;
    private String ea;
    private String marketingGroupId;   //目标人群id
    private Integer status;            //人群运行状态  MarketingUserGroupCalculationStatusEnum
    private int totalMarketingUser;    //人群总人数
    private int currentCalculateUser;  //当前累计计算人数
    private int triggerType;           //触发人群计算方式  MarketingGroupUserActionTriggerTypeEnum
    private int userId;                //操作人id
    private Date startTime;            //开始时间
    private Date createTime;           //创建时间
    private Date upateTime;            //更新时间
}
