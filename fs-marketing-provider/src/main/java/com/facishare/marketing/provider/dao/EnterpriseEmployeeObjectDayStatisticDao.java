package com.facishare.marketing.provider.dao;

import com.facishare.marketing.api.result.EmployeeObjectStatisticResult;
import com.facishare.marketing.api.result.kis.EmployeeSpreadStatisticResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.entity.EnterpriseEmployeeObjectDayStatisticEntity;
import com.github.mybatis.pagination.Page;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 */
public interface EnterpriseEmployeeObjectDayStatisticDao {
    @Select("SELECT * FROM enterprise_employee_object_day_statistic WHERE ea=#{ea} AND \"object_id\"=#{objectId} AND fs_user_id=#{fsUserId} AND \"date\"=#{date}")
    EnterpriseEmployeeObjectDayStatisticEntity getUnique(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("objectId") String objectId, @Param("date") Date date);

    @Insert("INSERT INTO enterprise_employee_object_day_statistic(id, ea, fs_user_id, object_type, object_id, \"date\", create_time) VALUES (#{id}, #{ea},#{fsUserId}, #{objectType}, #{objectId},#{date}, now()) ON CONFLICT DO NOTHING;")
    int insertIgnore(@Param("id") String id, @Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("objectType") Integer objectType, @Param("objectId") String objectId,
        @Param("date") Date date);

    @Update("UPDATE enterprise_employee_object_day_statistic SET forward_count=#{forwardCount},look_up_count=#{lookUpCount},active_count=#{activeCount},\n"
        + "    spread_count=#{spreadCount},all_channel_data=#{allChannelData},update_time=NOW() WHERE id = #{id}")
    int updateStatistic(EnterpriseEmployeeObjectDayStatisticEntity entity);

    @Select("<script> "
        + "SELECT * FROM (SELECT fs_user_id, sum(forward_count) forward_count, sum(look_up_count) look_up_count,sum(active_count) active_count,sum(spread_count) spread_count FROM enterprise_employee_object_day_statistic WHERE ea=#{ea} AND object_id=#{objectId}\n"
        + "    <if test=\"startDate != null\">\n" + "      AND \"date\" &gt;= #{startDate}\n" + "    </if>\n" + "    <if test=\"endDate != null\">\n" + "      AND \"date\" &lt;= #{endDate}\n"
        + "    </if>\n" + "    GROUP BY fs_user_id) tmp \n" + " ORDER BY tmp.active_count DESC, tmp.fs_user_id ASC" + " </script>")
    List<EmployeeObjectStatisticResult> pageQueryEmployeeObjectStatisticResult(@Param("ea") String ea, @Param("objectId") String objectId, @Param("startDate") Date startDate,
        @Param("endDate") Date endDate, Page page);

    @Select("<script> "
        + "SELECT * FROM (SELECT fs_user_id, sum(active_count) active_count,sum(spread_count) spread_count FROM enterprise_employee_object_day_statistic WHERE ea=#{ea} AND object_id=#{objectId}\n"
        + "    <if test=\"startDate != null\">\n" + "      AND \"date\" &gt;= #{startDate}\n" + "    </if>\n" + "    <if test=\"endDate != null\">\n" + "      AND \"date\" &lt;= #{endDate}\n"
        + "    </if>\n" + "    GROUP BY fs_user_id) tmp \n" + " ORDER BY tmp.active_count DESC, tmp.fs_user_id ASC" + " </script>")
    List<EmployeeSpreadStatisticResult> pageQueryEmployeeSpreadStatisticResult(@Param("ea") String ea, @Param("objectId") String objectId, @Param("startDate") Date startDate,
        @Param("endDate") Date endDate, Page page);

    @Select("SELECT COALESCE(count(distinct fs_user_id), 0) FROM enterprise_employee_object_amount_statistic where ea=#{ea} and object_id=#{objectId}")
    int countSpreadEmployees(@Param("ea") String ea, @Param("objectId") String objectId);

    @Select("<script> SELECT count(spread_count)  FROM  enterprise_employee_object_day_statistic    where create_time  between #{startDate} and  #{endDate}   </script>")
    Integer getSpreadCountEnterpriseEmployeeObjectDayWeeklyStatistics(@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    @Select("<script> SELECT count(forward_count)  FROM  enterprise_employee_object_day_statistic    where create_time  between #{startDate} and  #{endDate}   </script>")
    Integer getForwardCountEnterpriseEmployeeObjectDayWeeklyStatistics(@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    @Select("<script> "
        + "select   ea   from activity ac where ac.create_time between #{startDate} and  #{endDate}\n"
        + "UNION   select fs_ea as ea  from  article ar where ar.create_time between #{startDate} and  #{endDate}\n"
        + "UNION   select fs_ea as ea from product  pr where pr.create_time between #{startDate} and  #{endDate}\n"
        + "UNION   select  ea  from  marketing_activity_external_config  ma  where ma.create_time between #{startDate} and  #{endDate}\n"
        + "UNION   select   ea FROM  enterprise_employee_object_day_statistic  es   where  es.spread_count >0  and  es.create_time  between #{startDate} and  #{endDate}  group by ea  "
        + "</script>")
    List<String> getActiveNumberEnterprisesWeeklyStatistics(@Param("startDate") Date startDate,@Param("endDate") Date endDate);
}
