/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.sms.mw;

import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.MarketingEventFormEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.result.sms.mw.PhoneContentResult;
import com.facishare.marketing.api.result.sms.mw.SmsParamDescResult;
import com.facishare.marketing.common.enums.MarketingEventEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.sms.mw.SmsSceneTypeEnum;
import com.facishare.marketing.common.model.SmsParamObject;
import com.facishare.marketing.common.typehandlers.value.FieldValue;
import com.facishare.marketing.common.typehandlers.value.FieldValueList;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.manager.MarketingEventManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.remote.rest.ShortUrlManager;
import com.facishare.marketing.provider.remote.rest.arg.CreateShortUrlArg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Created by ranluch on 2019/7/25.
 */
@Service
@Slf4j
public class SmsParamManager {
    @Autowired
    private ShortUrlManager shortUrlManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private MarketingEventManager marketingEventManager;

    public List<PhoneContentResult> buildParamValueMap(Map<String, List<SmsParamObject>> paramObjectMap) {
        List<PhoneContentResult> phoneContentResults = Lists.newArrayList();
        for (Map.Entry<String, List<SmsParamObject>> entry : paramObjectMap.entrySet()) {
            PhoneContentResult contentResult = new PhoneContentResult();
            contentResult.setPhone(entry.getKey());
            Map<String, String> paramMap = Maps.newHashMap();
            for (SmsParamObject objectEntity : entry.getValue()) {
                paramMap.putAll(objectEntity.getParamValueMap());
            }
            contentResult.setParamMap(paramMap);
            phoneContentResults.add(contentResult);
        }
        return phoneContentResults;
    }

    public List<SmsParamDescResult> getParamDesc(String ea, int sceneType) {
        List<SmsParamDescResult> paramDescResults = Lists.newArrayList();
        for (Map.Entry<String, String> entry : getParamDescMap(ea, SmsSceneTypeEnum.fromType(sceneType)).entrySet()) {
            SmsParamDescResult descResult = new SmsParamDescResult();
            descResult.setValue(entry.getKey()); // 参数
            descResult.setName(entry.getValue()); //参数描述
            paramDescResults.add(descResult);
        }
        return paramDescResults;
    }

    /**
     * 根据市场活动查询短信参数列表
     * @param ea
     * @param marketingEventId
     * @return
     */
    public List<SmsParamDescResult> getParamDescByMarketingEventId(String ea, String marketingEventId) {
        // 查询市场活动类型
        MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, SuperUserConstants.USER_ID, marketingEventId);
        if (marketingEventData == null) {
            return Lists.newArrayList();
        }
        List<SmsParamDescResult> smsParamDescResults = Lists.newArrayList();

        // 获取市场活动自定义字段
        List<CrmUserDefineFieldVo> crmMktCustomerFields = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.MARKETING_EVENT);
        if (crmMktCustomerFields != null && !crmMktCustomerFields.isEmpty()) {
            crmMktCustomerFields.forEach(e -> {
                if (2 == e.getFieldProperty()) {
                    SmsParamDescResult descResult = new SmsParamDescResult();
                    descResult.setValue(e.getFieldName()); // 参数
                    descResult.setName(e.getFieldCaption()); //参数描述
                    descResult.setGroup("base");
                    smsParamDescResults.add(descResult);
                }
            });
        }

        // 获取活动成员自定义字段
        List<CrmUserDefineFieldVo> crmCampaignCustomerFields = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
        if (crmCampaignCustomerFields != null && !crmCampaignCustomerFields.isEmpty()) {
            crmCampaignCustomerFields.forEach(e -> {
                if (2 == e.getFieldProperty()) {
                    SmsParamDescResult descResult = new SmsParamDescResult();
                    descResult.setValue(e.getFieldName()); // 参数
                    descResult.setName(e.getFieldCaption()); //参数描述
                    descResult.setGroup("campaign");
                    smsParamDescResults.add(descResult);
                }
            });
        }

        String eventType = marketingEventData.getEventType();
        String eventForm = marketingEventData.getEventForm();
        if (StringUtils.equals(MarketingEventFormEnum.CONFERENCE_MARKETING.getValue(), eventForm)) {
            // 会议营销
            // 活动基本信息
            for (Map.Entry<String, String> entry : new ActivityEntity().getParamDescMap().entrySet()) {
                SmsParamDescResult descResult = new SmsParamDescResult();
                descResult.setValue(entry.getKey()); // 参数
                descResult.setName(entry.getValue()); //参数描述
                descResult.setGroup("base");
                smsParamDescResults.add(descResult);
            }
            // 参会人员信息
            for (Map.Entry<String, String> entry : new CampaignMergeDataEntity().getParamDescMap().entrySet()) {
                SmsParamDescResult descResult = new SmsParamDescResult();
                descResult.setValue(entry.getKey()); // 参数
                descResult.setName(entry.getValue()); //参数描述
                descResult.setGroup("campaign");
                smsParamDescResults.add(descResult);
            }
            //特殊的参数
            smsParamDescResults.add(new SmsParamDescResult("ticket.code", "参会码（数字）", "campaign"));
            smsParamDescResults.add(new SmsParamDescResult("ticket.url", "参会码（二维码）", "campaign"));
            smsParamDescResults.add(new SmsParamDescResult("activity.url", "会议主页URL", "base"));
        } else if (StringUtils.equals(MarketingEventFormEnum.LIVE_MARKETING.getValue(), eventForm)) {
            // 直播营销
            // 活动基本信息
            for (Map.Entry<String, String> entry : new MarketingLiveEntity().getParamDescMap().entrySet()) {
                SmsParamDescResult descResult = new SmsParamDescResult();
                descResult.setValue(entry.getKey()); // 参数
                descResult.setName(entry.getValue()); //参数描述
                descResult.setGroup("base");
                smsParamDescResults.add(descResult);
            }
            // 参会人员信息
            for (Map.Entry<String, String> entry : new CampaignMergeDataEntity().getParamDescMap().entrySet()) {
                SmsParamDescResult descResult = new SmsParamDescResult();
                descResult.setValue(entry.getKey()); // 参数
                descResult.setName(entry.getValue()); //参数描述
                descResult.setGroup("campaign");
                smsParamDescResults.add(descResult);
            }
        } else {
            // 活动营销
            // 活动基本信息
            smsParamDescResults.add(new SmsParamDescResult("activity.name", "市场活动名称", "base"));
            // 参会人员信息
            for (Map.Entry<String, String> entry : new CampaignMergeDataEntity().getParamDescMap().entrySet()) {
                SmsParamDescResult descResult = new SmsParamDescResult();
                descResult.setValue(entry.getKey()); // 参数
                descResult.setName(entry.getValue()); //参数描述
                descResult.setGroup("campaign");
                smsParamDescResults.add(descResult);
            }
        }
        return smsParamDescResults;
    }

    public FieldValueList getParamFieldList(String ea, Integer sceneType) {
        FieldValueList fieldValues = new FieldValueList();
        for (Map.Entry<String, String> entry : getParamDescMap(ea, SmsSceneTypeEnum.fromType(sceneType)).entrySet()) {
            FieldValue fieldValue = new FieldValue();
            fieldValue.setName(entry.getKey()); // 参数
            fieldValue.setValue(entry.getValue());//参数描述
            fieldValues.add(fieldValue);
        }
        return fieldValues;
    }

    private Map<String, String> getParamDescMap(String ea, SmsSceneTypeEnum sceneType) {
        Map<String, String> paramDescMap = Maps.newHashMap();
        if (sceneType == null) {
            return paramDescMap;
        }
        // 获取活动成员自定义字段
        if (!Arrays.asList(SmsSceneTypeEnum.CONFERENCE_INVITE, SmsSceneTypeEnum.LIVE_INVITE).contains(sceneType)) {
            List<CrmUserDefineFieldVo> crmCustomerFields = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
            if (crmCustomerFields != null && !crmCustomerFields.isEmpty()) {
                crmCustomerFields.forEach(e -> {
                    if (2 == e.getFieldProperty()) {
                        paramDescMap.put(e.getFieldName(), e.getFieldCaption());
                    }
                });
            }
        }
        switch (sceneType) {
            case GENERAL_MARKETING:
                paramDescMap.put("activity.name", I18nUtil.get(I18nKeyEnum.MARK_MW_SMSPARAMMANAGER_96));
                paramDescMap.put("activity.startTime", I18nUtil.get(I18nKeyEnum.MARK_MW_SMSPARAMMANAGER_97));
                paramDescMap.put("activity.endTime", I18nUtil.get(I18nKeyEnum.MARK_MW_SMSPARAMMANAGER_98));
                break;
            case CONFERENCE_ENROLL:
                paramDescMap.putAll(new ActivityEntity().getParamDescMap());
                paramDescMap.putAll(new CustomizeFormDataUserEntity().getParamDescMap());
                //特殊的参数
                paramDescMap.put("ticket.code", I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1079));
                paramDescMap.put("ticket.url", I18nUtil.get(I18nKeyEnum.MARK_MW_SMSPARAMMANAGER_105));
                paramDescMap.put("activity.url", I18nUtil.get(I18nKeyEnum.MARK_MW_SMSPARAMMANAGER_106));
                break;
            case CONFERENCE_INVITE:
                paramDescMap.putAll(new ActivityEntity().getParamDescMap());
                paramDescMap.put("activity.url", I18nUtil.get(I18nKeyEnum.MARK_MW_SMSPARAMMANAGER_106));
                break;
            case LIVE_ENROLL:
            case LIVE_INVITE:
                paramDescMap.putAll(new MarketingLiveEntity().getParamDescMap());
                break;
            default:
                break;
        }
        return paramDescMap;
    }

    public String getShowParamNameTemplate(String template, FieldValueList fieldValues) {
        if (CollectionUtils.isEmpty(fieldValues) || StringUtils.isEmpty(template)) {
            return template;
        }
        String resultStr = template;
        String leftBrace = "\\{";
        String rightBrace = "\\}";
        StringBuilder valueStr;
        StringBuilder nameStr;
        for (FieldValue fieldValue : fieldValues) {
            valueStr = new StringBuilder();
            valueStr.append(leftBrace).append(fieldValue.getValue()).append(rightBrace);

            nameStr = new StringBuilder();
            nameStr.append(leftBrace).append(fieldValue.getName()).append(rightBrace);

            resultStr = resultStr.replaceAll(nameStr.toString(), valueStr.toString());
        }
        return resultStr;
    }

    public String getShortUrl(String url) {
        try {
            CreateShortUrlArg arg = new CreateShortUrlArg();
            arg.setUrl(url);
            Optional<String> shortUrl = shortUrlManager.createShortUrl(arg);
            if (shortUrl.isPresent()){
                return shortUrl.get();
            }else {
                log.warn("SmsParamManager getShortUrl shortUrlResult is null,url={}, arg={}", url, arg);
            }
        } catch (Exception e) {
            log.warn("SmsParamManager getShortUrl is exception, url={}, e", url, e);
        }
        return url;
    }

}