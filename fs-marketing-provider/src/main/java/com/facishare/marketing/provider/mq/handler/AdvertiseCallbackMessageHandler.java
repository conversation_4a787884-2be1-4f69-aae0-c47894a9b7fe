package com.facishare.marketing.provider.mq.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.AdThirdSyncDataArg;
import com.facishare.marketing.api.service.baidu.BaiduCampaignService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AdvertiseCallbackMessageHandler extends AbstractMessageHandler<AdThirdSyncDataArg> {

    @Autowired
    private BaiduCampaignService baiduCampaignService;

    @Override
    protected AdThirdSyncDataArg getMsgObj(MessageExt message) {
        AdThirdSyncDataArg arg = JSON.parseObject(message.getBody(), AdThirdSyncDataArg.class);
        arg.setMessageId(message.getMsgId());
        return arg;
    }

    @Override
    protected String getEa(AdThirdSyncDataArg msgObj) {
        return msgObj.getEa();
    }

    @Override
    protected void directHandle(AdThirdSyncDataArg arg) {
        baiduCampaignService.syncThirdLeadData(arg);
    }

}
