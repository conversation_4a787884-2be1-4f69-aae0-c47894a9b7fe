package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.EnterpriseEmployeeObjectUVStatisticEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 */
public interface EnterpriseEmployeeObjectUVStatisticDao {
    @Select("<script>"
        + "SELECT * FROM enterprise_employee_object_uv_statistic WHERE ea=#{ea} AND object_id=#{objectId} AND fs_user_id IN "
        + "<foreach collection='fsUserIds' item='ite' separator=',' open='(' close=')'>"
        + "#{ite}"
        + "</foreach>"
        + "</script>")
    List<EnterpriseEmployeeObjectUVStatisticEntity> listByFsUserIds(@Param("ea") String ea, @Param("objectId") String objectId, @Param("fsUserIds") List<Integer> fsUserIds);
}
