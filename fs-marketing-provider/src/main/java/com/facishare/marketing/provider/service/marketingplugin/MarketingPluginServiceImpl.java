/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service.marketingplugin;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.CrmOrderDetailInfo;
import com.facishare.marketing.api.CrmOrderProductInfo;
import com.facishare.marketing.api.arg.plugin.BatchGetPluginStatusArg;
import com.facishare.marketing.api.arg.plugin.GetPluginSettingArg;
import com.facishare.marketing.api.arg.plugin.UpdatePluginSettingArg;
import com.facishare.marketing.api.result.OrderResult;
import com.facishare.marketing.api.result.PluginStatusResult;
import com.facishare.marketing.api.result.QueryScenaryResult;
import com.facishare.marketing.api.result.marketingplugin.CheckLicenseListResult;
import com.facishare.marketing.api.result.marketingplugin.MarketingPluginInfoResult;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.api.service.marketingplugin.MarketingPluginService;
import com.facishare.marketing.api.service.whatsapp.WhatsAppService;
import com.facishare.marketing.api.vo.marketingplugin.MarketingPluginConfigVo;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.MarketingPluginTypeEnum;
import com.facishare.marketing.common.enums.VersionEnum;
import com.facishare.marketing.common.enums.advertiser.AdOCPCStatusEnum;
import com.facishare.marketing.common.enums.appMenu.AppMenuTemplateTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.EnterpriseMetaConfigDao;
import com.facishare.marketing.provider.dao.ServiceKnowledgeScenarySettingDao;
import com.facishare.marketing.provider.dao.marketingplugin.MarketingPluginConfigDAO;
import com.facishare.marketing.provider.entity.EnterpriseMetaConfigEntity;
import com.facishare.marketing.provider.entity.ServiceKnowledgeScenarySettingEntity;
import com.facishare.marketing.provider.entity.advertiser.ocpc.AdOCPCConfigEntity;
import com.facishare.marketing.provider.entity.marketingplugin.MarketingPluginConfigEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCConfigManager;
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCUploadManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.*;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.whatsapp.WhatsAppManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.service.whatsapp.WhatsAppServiceImpl;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.license.Result.CheckValidityResult;
import com.facishare.paas.license.arg.CheckValidityArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.CheckValidityPojo;
import com.facishare.paas.license.pojo.FlagPojo;
import com.facishare.webhook.api.dto.AddCrmOrderDto;
import com.facishare.webhook.api.service.VersionRegisterService;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.enterpriserelation2.arg.BatchAddAppRolesWithEaArg;
import com.fxiaoke.enterpriserelation2.arg.BatchInitEnterpriseLinkAppsArg;
import com.fxiaoke.enterpriserelation2.arg.UpdateEnterpriseLinkAppRelationStatusArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.service.AppOuterRoleService;
import com.fxiaoke.enterpriserelation2.service.UpstreamService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 营销插件业务实现类
 * @date 2021/9/7 16:42
 */
@Slf4j
@Service("marketingPluginService")
public class MarketingPluginServiceImpl implements MarketingPluginService {

    @Autowired
    private MarketingPluginConfigManager marketingPluginConfigManager;

    @Autowired
    private MarketingPluginConfigDAO marketingPluginConfigDAO;

    @Autowired
    private UpstreamService upstreamService;

    @Value("${partner.appid}")
    private String partnerAppId;

    @Autowired
    private EIEAConverter eieaConverter;

    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;

    @Autowired
    private AppOuterRoleService appOuterRoleService;


    @Autowired
    private CouponObjDescribeManager couponObjDescribeManager;

    @Autowired
    private UserCouponObjDescribeManager userCouponObjDescribeManager;

    @Autowired
    private WechatCouponObjDescribeManager wechatCouponObjDescribeManager;

    @Autowired
    private ConfigService configService;

    @Autowired
    private CouponInstanceObjDescribeManager couponInstanceObjDescribeManager;

    @Autowired
    private MarketingPluginProxySerivce marketingPluginProxySerivce;

    @Autowired
    private AdOCPCConfigManager adOCPCConfigManager;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    @Autowired
    private AuthManager authManager;

    @ReloadableProperty("zhihu_clue")
    private String zhihuClue;
    @ReloadableProperty("sogou_clue")
    private String sogouClue;
    @ReloadableProperty("kuaishou_clue")
    private String kuaishouClue;
    @ReloadableProperty("shenma_clue")
    private String shenmaClue;
    @ReloadableProperty("clue.addorder")
    private String addorderUrl;
    @ReloadableProperty("facebook_clue")
    private String facebookClue;
    @ReloadableProperty("google_clue")
    private String googleClue;
    @ReloadableProperty("linkedin_clue")
    private String linkedinClue;
    @ReloadableProperty("redbook_clue")
    private String redbookClue;
    @ReloadableProperty("douyin_clue")
    private String douyinClue;


    @Autowired
    private HttpManager httpManager;

    @Autowired
    private AdDataReturnObjDescribeManager adDataReturnBackObjDescribeManager;

    @Autowired
    private AdOCPCUploadManager adOCPCUploadManager;

    @Autowired
    private AppVersionManager appVersionManager;

    @Autowired
    private VersionRegisterService versionRegisterService;

    @ReloadableProperty("knowledgeManagement_App_ProductId")
    private String knowledgeManagementAppProductId;

    @Autowired
    private CustomerServiceSessionObjManager customerServiceSessionObjManager;

//    @ReloadableProperty("customer_service_ea_list")
//    private String eaList;


    private static final String version = "interconnect_app_basic_app";

    private static final String roleId = "er_enterprise";

    @Autowired
    private SpreadChannelManager spreadChannelManager;

    @Autowired
    private AdvertisingDetailsObjManager advertisingDetailsObjManager;

    @Autowired
    private OtherObjectDescribeManager otherObjectDescribeManager;

    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;

    @Autowired
    private MarketingOrderManager marketingOrderManager;

    @Autowired
    private ServiceKnowledgeObjManager serviceKnowledgeObjManager;

    @Autowired
    private ServiceKnowledgeScenarySettingDao serviceKnowledgeScenarySettingDao;

    @Autowired
    private UserRoleManager userRoleManager;

    @Autowired
    private CouponDistributionObjManager couponDistributionObjManager;

    @Autowired
    private MarketingEventManager marketingEventManager;

    @Autowired
    private WhatsAppManager whatsAppManager;

    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;

    @Autowired
    private WhatsAppSendRecordObjManager whatsAppSendRecordObjManager;

    @Autowired
    private WhatsAppService whatsAppService;

    @Autowired
    private UserBehaviorRecordObjManager userBehaviorRecordObjManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private AdCommonManager adCommonManager;

    @Override
    public Result<MarketingPluginInfoResult> queryMarketingPluginInfo(String ea, Integer pluginType) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ACTIVITYSERVICEIMPL_155));
        Preconditions.checkArgument(pluginType != null, I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_MARKETINGPLUGINSERVICEIMPL_222));
        MarketingPluginConfigEntity marketingPluginConfigEntity = marketingPluginConfigDAO.queryMarketingPlugin(ea, pluginType);
        if (marketingPluginConfigEntity == null){
            return Result.newError(SHErrorCode.MERCHANT_NOT_CONFIG_YET);
        }
        MarketingPluginInfoResult pluginInfoResult = BeanUtil.copy(marketingPluginConfigEntity, MarketingPluginInfoResult.class);
        return Result.newSuccess(pluginInfoResult);
    }

    @Override
    public Result<List<MarketingPluginInfoResult>> queryMarketingPluginByEa(String ea) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ACTIVITYSERVICEIMPL_155));
        List<MarketingPluginInfoResult> marketingPluginInfoResults = Lists.newArrayList();
        List<MarketingPluginConfigEntity> marketingPluginConfigEntities = marketingPluginConfigDAO.queryMarketingPluginByEa(ea);
        if (CollectionUtils.isEmpty(marketingPluginConfigEntities)){
            return Result.newSuccess(marketingPluginInfoResults);
        }
        for (MarketingPluginConfigEntity marketingPluginConfigEntity : marketingPluginConfigEntities) {
            MarketingPluginInfoResult pluginInfoResult = BeanUtil.copy(marketingPluginConfigEntity, MarketingPluginInfoResult.class);
            marketingPluginInfoResults.add(pluginInfoResult);
        }
        return Result.newSuccess(marketingPluginInfoResults);
    }

    @Override
    public Result<Void> saveMarketingPluginConfig(MarketingPluginConfigVo vo) {
        Preconditions.checkArgument(vo != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_580));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(vo.getEa()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ACTIVITYSERVICEIMPL_155));
        Preconditions.checkArgument(vo.getPluginType() != null, I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_MARKETINGPLUGINSERVICEIMPL_222));
        Preconditions.checkArgument(vo.getStatus() != null, I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_MARKETINGPLUGINSERVICEIMPL_251));
        String pluginName = MarketingPluginTypeEnum.getName(vo.getPluginType());
        marketingPluginConfigManager.mergePluginConfig(vo.getEa(),vo.getPluginType(),pluginName,vo.getStatus());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> saveOrUpdateMarketingPlugin(String ea, Integer pluginType,Boolean status) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ACTIVITYSERVICEIMPL_155));
        Preconditions.checkArgument(pluginType != null, I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_MARKETINGPLUGINSERVICEIMPL_222));
        Preconditions.checkArgument(status != null, I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_MARKETINGPLUGINSERVICEIMPL_251));

        MarketingPluginBaseService marketingPluginBaseService = marketingPluginProxySerivce.getBean(pluginType);
        if (Objects.nonNull(marketingPluginBaseService)) {
            return marketingPluginBaseService.update(ea, pluginType, status);
        }

        if (pluginType.equals(MarketingPluginTypeEnum.SUYUAN_COUPON.getType()) && !status){
            log.warn("coupon plugin no close. ea={}",ea);
            return Result.newError(SHErrorCode.SUYUAN_COUPON_NOT_CLOSE);
        }
        //查询是否存在该插件数据
        MarketingPluginConfigEntity marketingPluginConfigEntity = marketingPluginConfigDAO.queryMarketingPlugin(ea, pluginType);
        if (marketingPluginConfigEntity == null){
            //如果是伙伴营销开启时,需要进行开启相关配置
            if (pluginType.equals(MarketingPluginTypeEnum.PARTNER_MARKETING.getType()) && status){
                //1.查询资源包
                CheckValidityArg checkArg = new CheckValidityArg();
                Set<String> versions = Sets.newHashSet();
                versions.add(version);
                LicenseContext licenseContext = new LicenseContext();
                licenseContext.setAppId("CRM");
                licenseContext.setTenantId(String.valueOf(eieaConverter.enterpriseAccountToId(ea)));
                licenseContext.setUserId("1000");
                checkArg.setContext(licenseContext);
                checkArg.setProductVersions(versions);
                checkArg.setProductType("3");
                CheckValidityResult checkValidityResult = licenseClient.checkValidity(checkArg);
                boolean flag = false;
                if (0 == checkValidityResult.getErrCode() && checkValidityResult.getResult() != null){
                    CheckValidityPojo validityPojo = checkValidityResult.getResult();
                    Set<FlagPojo> flagPojos = validityPojo.getFlagPojos();
                    for (FlagPojo flagPojo : flagPojos) {
                        if (version.equals(flagPojo.getProductVersion())){
                            flag = flagPojo.isFlag();
                        }
                    }
                }
                if (!flag){
                    return Result.newError(SHErrorCode.NOT_FIND_SOURCE_PACKAGE);
                }

                //2.伙伴营销放到互联应用
                BatchInitEnterpriseLinkAppsArg appsArg = new BatchInitEnterpriseLinkAppsArg();
                List<String> eas = Lists.newArrayList();
                eas.add(ea);
                appsArg.setLinkAppId(partnerAppId);
                appsArg.setEas(eas);
                appsArg.setEmployeeId(-1000);
                HeaderObj headerObj = HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea));
                headerObj.setAppId(partnerAppId);
                RestResult<String> initEnterpriseResult = upstreamService.batchInitEnterpriseLinkApps(headerObj, appsArg);
                log.info("initEnterpriseResult result = {}", GsonUtil.getGson().toJson(initEnterpriseResult));

                //3.把应用添加到互联角色 (企业用户)
                BatchAddAppRolesWithEaArg eaArg = new BatchAddAppRolesWithEaArg();
                eaArg.setFsEa(ea);
                eaArg.setAppId(partnerAppId);
                List<BatchAddAppRolesWithEaArg.RoleVo> roles = new ArrayList<>();
                BatchAddAppRolesWithEaArg batchAddAppRolesWithEaArg = new BatchAddAppRolesWithEaArg();
                BatchAddAppRolesWithEaArg.RoleVo roleVo = batchAddAppRolesWithEaArg.new RoleVo();
                roleVo.setRoleId(roleId);
                roles.add(roleVo);
                eaArg.setRoleAddList(roles);
                eaArg.setAppRoleType(3);
                HeaderObj batchHeader = HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea));
                batchHeader.setAppId(partnerAppId);
                RestResult < Void > addAppRolesResult = appOuterRoleService.batchAddAppRolesWithEa(batchHeader, eaArg);
                log.info("addAppRolesResult result = {}", GsonUtil.getGson().toJson(addAppRolesResult));

                //4.伙伴营销应用开启
                List<String> appIds = Lists.newArrayList();
                appIds.add(partnerAppId);
                UpdateEnterpriseLinkAppRelationStatusArg arg = new UpdateEnterpriseLinkAppRelationStatusArg();
                arg.setFsUserId(-1000);
                arg.setLinkAppIds(appIds);
                arg.setStatus(1);
                arg.setUpstreamEa(ea);
                HeaderObj updateHeader = HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea));
                updateHeader.setAppId(partnerAppId);
                RestResult<String>  enterpriseLinkAppRelationResult = upstreamService.updateEnterpriseLinkAppRelationStatus(updateHeader, arg);
                log.info("enterpriseLinkAppRelationResult result = {}", GsonUtil.getGson().toJson(enterpriseLinkAppRelationResult));
                userBehaviorRecordObjManager.getOrCreateObjDescribe(ea);

                //初始化伙伴营销推广工作台
                appMenuTemplateService.createSystemTemplate(ea, AppMenuTemplateTypeEnum.SYSTEM_PARTNER.getType());
            }
            //如果是溯源优惠券,则需要进行创建CRM对象
            else if (pluginType.equals(MarketingPluginTypeEnum.SUYUAN_COUPON.getType())){
                //1.判断营销通版本
                List<String> allMarketingVersions = appVersionManager.getAllMarketingVersions(ea);
                if (!allMarketingVersions.contains(VersionEnum.STREN.getVersion()) && !allMarketingVersions.contains(VersionEnum.PRO.getVersion())
                        && !allMarketingVersions.contains(VersionEnum.PRO_DINGTALK_30_APP.getVersion())
                        && !allMarketingVersions.contains(VersionEnum.PRO_DINGTALK_100_APP.getVersion())
                        && !allMarketingVersions.contains(VersionEnum.PRO_DINGTALK_500_APP.getVersion())
                ) {
                    return Result.newError(SHErrorCode.NEED_MARKETING_PRO_VERSION);
                }
                //2.判断是否购买订货通
                if (!marketingOrderManager.openOrderApplication(ea)) {
                    return Result.newError(SHErrorCode.NEED_ORDER_DHT);
                }
                //3.判断SFA是否打开开关
                String configValue = getConfigValue(String.valueOf(eieaConverter.enterpriseAccountToId(ea)), "coupon", "-10000");
                if (Strings.isNullOrEmpty(configValue) || "0".equals(configValue)) {
                    return Result.newError(SHErrorCode.COUPON_INST_OBJ_NOT_CREATED);
                }
                //进行CouponInstanceObj 字段添加
                boolean flag = couponInstanceObjDescribeManager.addCouponInstanceFieldDescribe(ea);
                if (!flag) {
                    return Result.newError(SHErrorCode.COUPON_OPEN_FAIL);
                }
                wechatCouponObjDescribeManager.getOrCreateWechatCouponObjDescribe(ea);
                couponObjDescribeManager.getOrCreateCouponObjDescribe(ea);
                userCouponObjDescribeManager.getOrCreateUserCouponObjDescribe(ea);
                couponDistributionObjManager.getOrCreateObjDescribe(ea);
            }
            else if (pluginType.equals(MarketingPluginTypeEnum.ZHIHU_CLUE.getType())){
                if (!checkLicense(ea, VersionEnum.ZHIHU_PLUGIN_APP.getVersion()).getData()) {
                    if (!placePluginOrder(zhihuClue,ea)) {
                        return Result.newSuccess();
                    }
                }
            }else if (pluginType.equals(MarketingPluginTypeEnum.SOGOU_CLUE.getType())){
                if (!checkLicense(ea, VersionEnum.SOUGOU_PLUGIN_APP.getVersion()).getData()) {
                    if (!placePluginOrder(sogouClue,ea)) {
                        return Result.newSuccess();
                    }
                }
            }else if (pluginType.equals(MarketingPluginTypeEnum.KUAISHOU_CLUE.getType())){
                if (!checkLicense(ea, VersionEnum.KUAISHOU_PLUGIN_APP.getVersion()).getData()) {
                    if (!placePluginOrder(kuaishouClue,ea)) {
                        return Result.newSuccess();
                    }
                }
            }else if (pluginType.equals(MarketingPluginTypeEnum.SHENMA_CLUE.getType())){
                if (!checkLicense(ea, VersionEnum.SHENMA_PLUGIN_APP.getVersion()).getData()) {
                    if (!placePluginOrder(shenmaClue,ea)) {
                        return Result.newSuccess();
                    }
                }
            } else if (pluginType.equals(MarketingPluginTypeEnum.AD_OCPC.getType())) {
                adDataReturnBackObjDescribeManager.getOrCreateAdSendObjDescribe(ea);
                adOCPCUploadManager.initRule(ea);
                otherObjectDescribeManager.tryAddLeadJoinCompensateStatusField(ea);
            } else if (MarketingPluginTypeEnum.getAdPluginTypeList().contains(pluginType)) {
                ThreadPoolUtils.execute(() -> {
                    appVersionManager.addMarketingIntegrationAppOrder(ea);
                    userRoleManager.initAdAccountMarketingConfig(ea);
                    adCommonManager.initAdvertiseConfig(ea);
                }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
                if (pluginType.equals(MarketingPluginTypeEnum.GOOGLE_AD.getType())) {
                    if (!checkLicense(ea, VersionEnum.GOOGLE_PLUGIN_APP.getVersion()).getData()) {
                        if (!placePluginOrder(googleClue,ea)) {
                            return Result.newSuccess();
                        }
                    }
                }
            } else if (pluginType.equals(MarketingPluginTypeEnum.KNOWLEDGE_MANAGEMENT_APP.getType())) {
                if (!addKnowledgeManagementAppOrder(ea)) {
                    return Result.newSuccess();
                }
            } else if (pluginType.equals(MarketingPluginTypeEnum.MARKETING_CUSTOMER_INTEGRATION.getType())) {
                //判断是否购买了在线客服
                if (!(appVersionManager.checkSpecialVersionOrders(ea, VersionEnum.CUSTOMER_SERVICE_PRO_APP)||appVersionManager.checkSpecialVersionOrders(ea,VersionEnum.CUSTOMER_SERVICE_APP))) {
                    return Result.newError(SHErrorCode.NOT_BUG_CUSTOMER_SERVICE);
                }
                //客服会话列表添加访客字段
                customerServiceSessionObjManager.tryUpdateCustomFieldLabel(ea);
                //线索添加在线客服推广渠道
                List<SpreadChannelManager.SpreadChannleOption> spreadChannleOptionList = Lists.newArrayList();
                SpreadChannelManager.SpreadChannleOption spreadChannleOption = new SpreadChannelManager.SpreadChannleOption();
                spreadChannleOption.setLabel("在线客服");
                spreadChannleOption.setValue("onlineservice");
                spreadChannleOptionList.add(spreadChannleOption);
                spreadChannelManager.addChannel(ea, -10000, spreadChannleOptionList, false);
            } else if (pluginType.equals(MarketingPluginTypeEnum.MARKETING_DATA_ISOLATION.getType())) {
                //判断是否购买了营销通旗舰版
                //10.5新分版 数据权限只由是否购买插件控制
//                if (!appVersionManager.checkSpecialVersionOrders(ea, VersionEnum.STREN)) {
//                    return Result.newError(SHErrorCode.NEED_MARKETING_STREN_VERSION);
//                }
            } else if (pluginType.equals(MarketingPluginTypeEnum.WHATS_APP.getType())) {
                Result<Integer> result = whatsAppService.checkWhatsAppPluginOpenCondition(ea);
                int resultCode = result.getData();
                if (resultCode == WhatsAppServiceImpl.FORBID_TO_OPEN_WHATSAPP_PLUGIN) {
                    return Result.newError(SHErrorCode.FORBID_TO_OPEN_WHATSAPP_PLUGIN);
                } else if (resultCode == WhatsAppServiceImpl.ENTERPRISE_NOT_HAVE_AUTHORIZATION) {
                    return Result.newError(SHErrorCode.ENTERPRISE_NOT_HAVE_AUTHORIZATION);
                }
                whatsAppSendRecordObjManager.getOrCreateWhatsAppSendRecordObjDescribe(ea);
                marketingActivityRemoteManager.addWhatsAppSpreadTypeOptions(ea);
            } else if (pluginType.equals(MarketingPluginTypeEnum.LINKEDIN.getType())) {
                String currentVersion = appVersionManager.getCurrentAppVersion(ea);
                Set<String> standardVersionSet = Sets.newHashSet(VersionEnum.STAN.getVersion(), VersionEnum.STAN_DINGTALK_30_APP.getVersion(), VersionEnum.STAN_DINGTALK_100_APP.getVersion(), VersionEnum.STAN_DINGTALK_500_APP.getVersion());
                // 如果是标准版、并且没有购买海外插件
                if (standardVersionSet.contains(currentVersion) && !checkLicense(ea, VersionEnum.OVERSEAS_PLUGIN.getVersion()).getData()) {
                    return Result.newError(SHErrorCode.FORBID_OPEN_LINKEDIN_PLUGINS);
                }
                if (!checkLicense(ea, VersionEnum.LINKED_PLUGIN_APP.getVersion()).getData()) {
                    //进行下单处理
                    if (!placePluginOrder(linkedinClue,ea)) {
                        return Result.newSuccess();
                    }
                }
                adCommonManager.initAdvertiseConfig(ea);
            } else if (pluginType.equals(MarketingPluginTypeEnum.MARKETING_AI_PLUGIN.getType())) {
                // 判断是否有license: marketing_ai_plugin_app
                if (!checkLicense(ea, VersionEnum.MARKETING_AI_PLUGIN_APP.getVersion()).getData()) {
                    return Result.newError(SHErrorCode.FORBID_OPEN_MARKETING_AI_PLUGIN);
                }
            } else if (pluginType.equals(MarketingPluginTypeEnum.FACEBOOK_MARKETING_PLUGIN.getType())){
                //判断是否有license: facebook_data_sync_app
                if (!checkLicense(ea, VersionEnum.FACEBOOK_PLUGIN_APP.getVersion()).getData()) {
                    //进行下单处理
                    if (!placePluginOrder(facebookClue,ea)) {
                        return Result.newSuccess();
                    }
                }
            } else if (pluginType.equals(MarketingPluginTypeEnum.XIAO_HONG_SHU_MARKETING_PLUGIN.getType())){
                //判断是否有license: redbook_data_sync_app
                if (!checkLicense(ea, VersionEnum.RED_BOOK_PLUGIN_APP.getVersion()).getData()) {
                    //进行下单处理
                    if (!placePluginOrder(redbookClue,ea)) {
                        return Result.newSuccess();
                    }
                }
            } else if (pluginType.equals(MarketingPluginTypeEnum.DOU_YIN_LAI_KE_MARKETING_PLUGIN.getType())) {
                // 判断是否有license: douyinlife_data_sync_app
                if (!checkLicense(ea, VersionEnum.DOU_YIN_LIFE_PLUGIN_APP.getVersion()).getData()) {
                    // 没有购买就进行下单处理
                    if (!placePluginOrder(douyinClue, ea)) {
                        return Result.newSuccess();
                    }
                }
            }
            //进行新增插入记录
            String pluginName = MarketingPluginTypeEnum.getName(pluginType);
            marketingPluginConfigManager.mergePluginConfig(ea,pluginType,pluginName,status);
            return Result.newSuccess();
        }
        //进行更新数据操作
        marketingPluginConfigDAO.updatePluginStatus(ea,pluginType,status);
        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> queryMarketingPluginStatus(String ea, Integer pluginType) {
        Boolean currentPluginStatus = marketingPluginConfigManager.getCurrentPluginStatus(ea, pluginType);
        return Result.newSuccess(currentPluginStatus);
    }

    @Override
    public Result<List<QueryScenaryResult>> queryServiceKnowledgeScenaryList(String ea) {
        if(StringUtils.isBlank(ea)){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<QueryScenaryResult> resultList = serviceKnowledgeObjManager.queryScenaryList(ea, 1);
        if(CollectionUtils.isEmpty(resultList)){
            return Result.newSuccess(resultList);
        }
        List<QueryScenaryResult> scenaryResults = resultList.stream().filter(o -> !"other".equals(o.getValue())).collect(Collectors.toList());
        return Result.newSuccess(scenaryResults);
    }

    @Override
    public Result insertOrUpdateServiceKnowledgeScenary(String ea, String id) {
        Result<List<QueryScenaryResult>> listResult = queryServiceKnowledgeScenaryList(ea);
        if(!listResult.isSuccess() || CollectionUtils.isEmpty(listResult.getData())){
            return Result.newError(999999998,I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_MARKETINGPLUGINSERVICEIMPL_485));
        }
        List<String> collect = listResult.getData().stream().map(QueryScenaryResult::getValue).collect(Collectors.toList());
        if(!collect.contains(id)){
            return Result.newError(999999999,I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_MARKETINGPLUGINSERVICEIMPL_489));
        }
        ServiceKnowledgeScenarySettingEntity entity = serviceKnowledgeScenarySettingDao.getDetailByEa(ea);
        if(entity==null){
            ServiceKnowledgeScenarySettingEntity insertEntity = new ServiceKnowledgeScenarySettingEntity();
            insertEntity.setId(UUIDUtil.getUUID());
            insertEntity.setEa(ea);
            insertEntity.setScenary(id);
            serviceKnowledgeScenarySettingDao.insert(insertEntity);
            return Result.newSuccess();
        }
        serviceKnowledgeScenarySettingDao.updateByEa(ea,id);
        return Result.newSuccess();
    }

    @Override
    public Result<QueryScenaryResult> queryServiceKnowledgeScenarySetting(String ea) {
        ServiceKnowledgeScenarySettingEntity entity = serviceKnowledgeScenarySettingDao.getDetailByEa(ea);
        if(entity==null||StringUtils.isBlank(entity.getScenary())){
            return Result.newSuccess();
        }
        List<QueryScenaryResult> resultList = serviceKnowledgeObjManager.queryScenaryList(ea, 1);
        for (QueryScenaryResult queryScenaryResult : resultList) {
            if (entity.getScenary().equals(queryScenaryResult.getValue())){
                return Result.newSuccess(queryScenaryResult);
            }
        }
        return Result.newSuccess();
    }

//    @Override
//    public Result<Boolean> queryEnterCustomerServiceEaList(String ea) {
//        boolean flag = false;
//        String[] split = eaList.split(",");
//        List<String> eas = Arrays.asList(split);
//        if (eas.contains(ea)) {
//            flag = true;
//        }
//        return Result.newSuccess(flag);
//    }

    // 下单营销一体化
    public boolean addKnowledgeManagementAppOrder(String ea) {
        AddCrmOrderDto.Argument cmrOrderArgument = new AddCrmOrderDto.Argument();
        com.facishare.webhook.api.model.CrmOrderDetailInfo crmOrderDetailInfo = new com.facishare.webhook.api.model.CrmOrderDetailInfo();
        crmOrderDetailInfo.setEnterpriseAccount(ea);
        crmOrderDetailInfo.setOrderTime(System.currentTimeMillis());
        //1-标准（购买），2-试用，3-赠送
        crmOrderDetailInfo.setOrderTpye(1);
        cmrOrderArgument.setCrmOrderDetailInfo(crmOrderDetailInfo);
        com.facishare.webhook.api.model.CrmOrderProductInfo crmOrderProductInfo = new com.facishare.webhook.api.model.CrmOrderProductInfo();
        crmOrderProductInfo.setOrderAmount("0");
        crmOrderProductInfo.setQuantity(1);
        crmOrderProductInfo.setAllResourceCount(1);
        crmOrderProductInfo.setBeginTime(System.currentTimeMillis());
        //开通100年
        crmOrderProductInfo.setEndTime(System.currentTimeMillis() + 1000L * 60 * 60 * 24 * 365 * 100);
        crmOrderProductInfo.setProductId(knowledgeManagementAppProductId);
        cmrOrderArgument.setCrmOrderProductInfo(crmOrderProductInfo);
        //  AddCrmOrderDto.Result result = versionRegisterService.addCrmOrder(cmrOrderArgument);
        OrderResult result = appVersionManager.getOrderResult(cmrOrderArgument);
        log.info("知识库下单,arg:[{}], result:[{}]", cmrOrderArgument, result);
        if(result!=null && result.isSuccess()){
            return true;
        }
        return false;
    }

     private  String getConfigValue(String tenantId, String configKey, String userId) {
        User user = new User(tenantId, userId);
        return configService.findTenantConfig(user, configKey);
    }

    private boolean getOrderResult(String productId, String ea) {
        long beginTime = System.currentTimeMillis();
        long endTime = beginTime + 24L * 60 * 60 * 1000 * 365 * 100;
        String url  = addorderUrl +"/versionRegisterService/addCrmOrder";
        CrmOrderProductInfo crmOrderProductInfo = new CrmOrderProductInfo();
        CrmOrderDetailInfo crmOrderDetailInfo = new CrmOrderDetailInfo();
        crmOrderDetailInfo.setEnterpriseAccount(ea);
        crmOrderDetailInfo.setOrderId(UUIDUtil.getUUID());
        crmOrderDetailInfo.setOrderTpye("2");
        crmOrderDetailInfo.setOrderTime(String.valueOf(beginTime));
        crmOrderProductInfo.setAllResourceCount("1");
        crmOrderProductInfo.setBeginTime(String.valueOf(beginTime));
        crmOrderProductInfo.setEndTime(String.valueOf(endTime));
        crmOrderProductInfo.setProductId(productId);
        crmOrderProductInfo.setOrderAmount("0.0");
        crmOrderProductInfo.setQuantity("1");
        HashMap<String, Object> map = new HashMap<>();
        map.put("crmOrderProductInfo",crmOrderProductInfo);
        map.put("crmOrderDetailInfo",crmOrderDetailInfo);
        OrderResult result = httpManager.executePostHttp(map, url, new TypeToken<OrderResult>() {});
        if(result!=null&&result.isSuccess()){
            return true;
        }
        return false;
    }

    private boolean placePluginOrder(String productId, String ea) {
        String currentVersion = appVersionManager.getCurrentAppVersion(ea);
        long beginTime = System.currentTimeMillis();
        long endTime = marketingPluginConfigManager.getVersionEndTime(ea,currentVersion);
        String url  = addorderUrl +"/versionRegisterService/addCrmOrder";
        CrmOrderProductInfo crmOrderProductInfo = new CrmOrderProductInfo();
        CrmOrderDetailInfo crmOrderDetailInfo = new CrmOrderDetailInfo();
        crmOrderDetailInfo.setEnterpriseAccount(ea);
        crmOrderDetailInfo.setOrderId(UUIDUtil.getUUID());
        crmOrderDetailInfo.setOrderTpye("3");
        crmOrderDetailInfo.setOrderTime(String.valueOf(beginTime));
        crmOrderProductInfo.setAllResourceCount("1");
        crmOrderProductInfo.setBeginTime(String.valueOf(beginTime));
        crmOrderProductInfo.setEndTime(String.valueOf(endTime));
        crmOrderProductInfo.setProductId(productId);
        crmOrderProductInfo.setOrderAmount("0.0");
        crmOrderProductInfo.setQuantity("1");
        HashMap<String, Object> map = new HashMap<>();
        map.put("crmOrderProductInfo",crmOrderProductInfo);
        map.put("crmOrderDetailInfo",crmOrderDetailInfo);
        OrderResult result = httpManager.executePostHttp(map, url, new TypeToken<OrderResult>() {});
        if(result!=null && result.isSuccess()){
            return true;
        }
        return false;
    }


    //TODO 860全网之后可以去掉这个接口
    @Override
    public Result<Boolean> isOpenInvalidRebate(Integer tenantId, Integer userId) {
        if (tenantId == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        log.info("isOpenMarketingAdPlugin tenantId: {}", tenantId);
        try {
            String ea = eieaConverter.enterpriseIdToAccount(tenantId);
            EnterpriseMetaConfigEntity metaConfigEntity = enterpriseMetaConfigDao.getByEa(ea);
            if (metaConfigEntity == null) {
                return Result.newSuccess(false);
            }
            List<AdOCPCConfigEntity> configEntityList = adOCPCConfigManager.getByEaAndInvalidRebateStatus(ea, AdOCPCStatusEnum.NORMAL.getStatus());
//            List<MarketingPluginConfigEntity> configEntityList = marketingPluginConfigDAO.queryMarketingPluginByTypes(ea, MarketingPluginTypeEnum.getAdPluginTypeList());
            if (CollectionUtils.isEmpty(configEntityList)) {
                return Result.newSuccess(false);
            }
            return Result.newSuccess(true);
        } catch (Exception e) {
            log.error("isOpenMarketingAdPlugin error, tenantId: {}", tenantId);
        }
        return Result.newSuccess(false);
    }

    @Override
    public Result<Boolean> checkLicense(String ea, String moduleCode) {
        boolean result = appVersionManager.checkSpecialVersionOrders(ea, moduleCode);
        return Result.newSuccess(result);
    }

    @Override
    public Result<PluginStatusResult> batchGetMarketingPluginStaus(BatchGetPluginStatusArg arg) {
        List<MarketingPluginConfigEntity> entityList;
        List<MarketingPluginTypeEnum> marketingPluginTypeEnums = Lists.newArrayList(MarketingPluginTypeEnum.values());
        if (CollectionUtils.isNotEmpty(arg.getPluginTypeList())) {
            entityList = marketingPluginConfigDAO.queryMarketingPluginByTypes(arg.getEa(), arg.getPluginTypeList());
            marketingPluginTypeEnums = marketingPluginTypeEnums.stream().filter(e -> arg.getPluginTypeList().contains(e.getType())).collect(Collectors.toList());
        } else {
            entityList = marketingPluginConfigDAO.queryMarketingPluginByEa(arg.getEa());
        }
        entityList = entityList == null ? Lists.newArrayList() : entityList;
        Map<Integer, MarketingPluginConfigEntity> pluginTypeToEntityMap = entityList.stream().collect(Collectors.toMap(MarketingPluginConfigEntity::getPluginType, e -> e, (v1, v2) -> v1));
        PluginStatusResult statusResult = new PluginStatusResult();
        List<PluginStatusResult.Item> dataList = Lists.newArrayList();
        for (MarketingPluginTypeEnum value : marketingPluginTypeEnums) {
            int type = value.getType();
            PluginStatusResult.Item item = new PluginStatusResult.Item();
            item.setPluginType(type);
            MarketingPluginConfigEntity configEntity = pluginTypeToEntityMap.get(type);
            item.setStatus(configEntity != null && configEntity.getStatus());
            dataList.add(item);
        }
        statusResult.setDataList(dataList);
        return Result.newSuccess(statusResult);
    }

    @Override
    public Result<CheckLicenseListResult> checkLicenseList(String ea, List<String> moduleCodes) {
        CheckLicenseListResult result = new CheckLicenseListResult();
        List<CheckLicenseListResult.LicenseConfig> licenseConfigs = appVersionManager.checkLicenseConfigs(ea, moduleCodes);
        result.setConfigs(licenseConfigs);
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> updateConfig(UpdatePluginSettingArg arg) {
        if (arg.getPluginType() == null || arg.getSetting() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEa();
        MarketingPluginConfigEntity marketingPluginConfigEntity = marketingPluginConfigDAO.queryMarketingPlugin(ea, arg.getPluginType());
        if (marketingPluginConfigEntity == null) {
            return Result.newError(-1, I18nUtil.get("mark.plugin.not.enable", "插件未开启,请先开启插件"));
        }
        int pluginType = arg.getPluginType();
        if (pluginType == MarketingPluginTypeEnum.MARKETING_SDR.getType() || pluginType == MarketingPluginTypeEnum.MARKETING_AI_PLUGIN.getType()) {
            handleAiOrSdrPluginSetting(arg.getEa(), marketingPluginConfigEntity, arg.getSetting());
        }
        return Result.newSuccess();
    }

    private void handleAiOrSdrPluginSetting(String ea, MarketingPluginConfigEntity marketingPluginConfigEntity, JSONObject newSetting) {
        String oldSetting = marketingPluginConfigEntity.getSetting();
        JSONObject settingJson = StringUtils.isBlank(oldSetting) ? new JSONObject() : JSONObject.parseObject(oldSetting);
        settingJson.putAll(newSetting);
        marketingPluginConfigDAO.updateSetting(ea, marketingPluginConfigEntity.getId(), settingJson.toJSONString());
    }

    @Override
    public Result<JSONObject> getSetting(GetPluginSettingArg arg) {
        String ea = arg.getEa();
        MarketingPluginConfigEntity marketingPluginConfigEntity = marketingPluginConfigDAO.queryMarketingPlugin(ea, arg.getPluginType());
        if (marketingPluginConfigEntity == null || StringUtils.isBlank(marketingPluginConfigEntity.getSetting())) {
            return Result.newSuccess();
        }
        JSONObject jsonObject = JSONObject.parseObject(marketingPluginConfigEntity.getSetting());
        if (arg.getPluginType() == MarketingPluginTypeEnum.MARKETING_SDR.getType()) {
            Object sdrRuleIdObj = jsonObject.remove("sdr_rule_id");
            if (sdrRuleIdObj != null) {
                String sdrRuleId = sdrRuleIdObj.toString();
                ObjectData objectData = crmV2Manager.getObjectData(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.SDR_BUSINESS_RULE_OBJ.getName(), sdrRuleId);
                if (objectData != null) {
                    JSONObject ruleData = new JSONObject();
                    ruleData.put("ruleName", objectData.getName());
                    ruleData.put("sdrRuleId", objectData.getId());
                    ruleData.put("replyLanguage", objectData.getString("reply_language"));
                    ruleData.put("toneStyle", objectData.get("tone_style"));
                    ruleData.put("consultationRobotGoal", objectData.get("consultation_robot_target"));
                    ruleData.put("consultationInfoFields", objectData.get("lead_info"));
                    String moduleId = objectData.getString("score_module");
                    ruleData.put("conversionModelId", moduleId);
                    ObjectData scoreModuleObjectData = crmV2Manager.getObjectData(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.SDR_SCORING_MODEL_OBJ.getName(), moduleId);
                    if (scoreModuleObjectData != null) {
                        ruleData.put("conversionModelName", scoreModuleObjectData.getName());
                    }
                    ruleData.put("conversionMetrics", objectData.getBigDecimal("conversion_metric"));
                    ruleData.put("knowledgeSpace", objectData.getString("knowledge_base_ref"));
                    ruleData.put("rule", objectData.getString("followup_rules"));
                    jsonObject.put("ruleData", ruleData);
                }
            }
        }
        return Result.newSuccess(jsonObject);
    }
}