package com.facishare.marketing.provider.service.emailMaterial;

import com.facishare.marketing.api.arg.emailMaterial.GetEmailMaterialArg;
import com.facishare.marketing.api.arg.emailMaterial.SaveEmailMaterialArg;
import com.facishare.marketing.api.result.emailMaterial.GetEmailMaterialResult;
import com.facishare.marketing.api.result.emailMaterial.SaveEmailMaterialResult;
import com.facishare.marketing.api.service.emailMaterial.EmailMaterialService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.emailMaterial.EmailMaterialDao;
import com.facishare.marketing.provider.entity.emailMaterial.EmailMaterialEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2025/2/25
 * @Desc
 **/
@Slf4j
@Service("emailMaterialService")
public class EmailMaterialServiceImpl implements EmailMaterialService {

    @Autowired
    private EmailMaterialDao emailMaterialDao;

    @Override
    public Result<SaveEmailMaterialResult> save(SaveEmailMaterialArg arg) {
        String ea = arg.getEa();
        Integer userId = arg.getFsUserId();
        SaveEmailMaterialResult result = new SaveEmailMaterialResult();
        if (StringUtils.isBlank(arg.getId())) {
            // 新增
            String uuid = UUIDUtil.getUUID();
            EmailMaterialEntity entity = new EmailMaterialEntity();
            entity.setEa(ea);
            entity.setId(uuid);
            entity.setTitle(arg.getTitle());
            entity.setContent(arg.getContent());
            entity.setCreateBy(userId);
            entity.setUpdateBy(userId);
            emailMaterialDao.insert(entity);
            result.setId(uuid);
        } else {
            // 更新
            EmailMaterialEntity entity = emailMaterialDao.getById(arg.getId());
            if (entity == null) {
                return Result.newError(SHErrorCode.NO_DATA);
            }
            entity.setTitle(arg.getTitle());
            entity.setContent(arg.getContent());
            entity.setUpdateBy(userId);
            emailMaterialDao.update(entity);
            result.setId(arg.getId());
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<GetEmailMaterialResult> get(GetEmailMaterialArg arg) {
        if (StringUtils.isBlank(arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), "id is null");
        }
        EmailMaterialEntity entity = emailMaterialDao.getById(arg.getId());
        if (entity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        GetEmailMaterialResult result = new GetEmailMaterialResult();
        result.setId(entity.getId());
        result.setTitle(entity.getTitle());
        result.setContent(entity.getContent());
        return Result.newSuccess(result);
    }
}
