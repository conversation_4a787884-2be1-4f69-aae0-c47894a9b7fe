package com.facishare.marketing.provider.entity.usermarketingaccount;

import java.io.Serializable;
import java.util.Date;

import com.facishare.marketing.common.enums.ChannelEnum;
import com.google.common.base.Joiner;
import lombok.Data;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 11/12/2018
 */
@Data
public class UserMarketingMiniappAccountRelationEntity implements Serializable {
    private String id;
    /**
     * 客脉小程序id
     */
    private String uid;
    /**
     * 企业ea
     */
    private String ea;
    /**
     * 用户营销账号id
     */
    private String userMarketingId;
    private Date createTime;
    private Date updateTime;
    public String generateUniqueKey() {
        return Joiner.on(".").join(ChannelEnum.MINIAPP.name(), this.getEa(), this.getUid());
    }
}
