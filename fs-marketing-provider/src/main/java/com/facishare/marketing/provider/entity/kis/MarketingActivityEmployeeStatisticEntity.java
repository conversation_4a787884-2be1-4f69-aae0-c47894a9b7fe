package com.facishare.marketing.provider.entity.kis;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by ranluch on 2019/2/25.
 */
@Data
@Entity
public class MarketingActivityEmployeeStatisticEntity implements Serializable {
    private String id;
    private String ea;   // 公司帐号
    private Integer fsUserId;
    private String marketingActivityId; //营销活动id
    private Integer spreadCount;   // 员工自己推广的次数
    private Integer forwardCount; // 转发次数，不含员工
    private Integer lookUpCount; // 访问次数，不含员工
    private Integer forwardUserCount; // 转发人数，不含员工
    private Integer lookUpUserCount; // 访问人数，不含员工
    private Integer leadAccumulationCount; // 线索累积量
    private Integer customerAccumulationCount; // 客户累积量
    private Date createTime;
    private Date updateTime;

    public void mergeData(MarketingActivityEmployeeStatisticEntity mergeData) {
        this.setSpreadCount(getIntValue(this.getSpreadCount()) + getIntValue(mergeData.getSpreadCount()));
        this.setForwardCount(getIntValue(this.getForwardCount()) + getIntValue(mergeData.getForwardCount()));
        this.setLookUpCount(getIntValue(this.getLookUpCount()) + getIntValue(mergeData.getLookUpCount()));
        this.setForwardUserCount(getIntValue(this.getForwardUserCount()) + getIntValue(mergeData.getForwardUserCount()));
        this.setLookUpUserCount(getIntValue(this.getLookUpUserCount()) + getIntValue(mergeData.getLookUpUserCount()));
        this.setLeadAccumulationCount(getIntValue(this.getLeadAccumulationCount()) + getIntValue(mergeData.getLeadAccumulationCount()));
        this.setCustomerAccumulationCount(getIntValue(this.getCustomerAccumulationCount()) + getIntValue(mergeData.getCustomerAccumulationCount()));
    }

    private int getIntValue(Integer value){
        return value == null ? 0 : value.intValue();
    }
}
