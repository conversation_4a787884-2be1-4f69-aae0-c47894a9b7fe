package com.facishare.marketing.provider.entity.hexagon;

import com.facishare.marketing.common.annoation.FilterLog;
import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
public class HexagonPageEntity implements Serializable {
    private String id;

    private String ea;

    private String hexagonSiteId;

    private String name;

    private String shareTitle;

    private String shareDesc;

    private String sharePicH5Apath;

    private String sharePicMpApath;

    @FilterLog
    private String content;

    private Integer isHomepage;

    private String formId;

    private Integer status;

    private Integer createBy;

    private Integer updateBy;

    private Date createTime;

    private Date updateTime;
}