package com.facishare.marketing.provider.entity.sms.mw;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by ranluch on 2019/2/18.
 */
@Data
public class MwSmsSendEntity implements Serializable {
    private String id;
    private String ea;

    // 短信签名id
    private String signatureId;
    // 模板id
    private String templateId;
    // 发送状态
    private Integer status;
    //实际发送人数
    private Integer actualSenderCount;
    //需要发送人数
    private Integer toSenderCount;

    private Integer totalFee;

    // 1：及时发送 2：定时发送
    private Integer type;

    //定时发送时间
    private Date scheduleTime;

    private String hostName;

    // 返回的错误码，用来展示对应的错误提示
    private Integer resultCode;

    // 平台流水号
    private Long msgid;

    // 自定义流水号
    private String custid;

    private Integer creatorUserId;
    private String creatorName;
    private Date createTime;
    private Date updateTime;
    private List<String> userGroups;
    private Integer channelType;

    private Long sendFlag;
    private Date sendTime;

    private int rebackFee; //统计用，并不是数据库字段
    private int senderReduce; // 实际发送减少人数

    // 发送业务，只给 短信发送记录对象使用 没有业务含义
    private String businessType;

    // 发送对象，只给 短信发送记录对象使用 没有业务含义
    private String receiver;

    // 发送节点，只给 短信发送记录对象使用 没有业务含义
    private String sendNode;

    // 发送节点类型，只给 短信发送记录对象使用 没有业务含义
    private String nodeType;

    private String objectId; //对象id
}
