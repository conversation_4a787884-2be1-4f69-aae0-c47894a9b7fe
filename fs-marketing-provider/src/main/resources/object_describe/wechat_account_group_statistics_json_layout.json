{"api_name": "layout_hfk1M", "display_name": "默认布局", "layout_description": "", "ref_object_api_name": "WechatAccountGroupStatisticsObj", "is_default": true, "is_deleted": false, "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["top_info", "tabs_1I6z2"], ["relevant_team_component"]], "columns": [{"width": "auto"}, {"width": "500px", "retractable": true}]}]}, "package": "CRM", "version": 1, "layout_type": "detail", "buttons": [], "is_deal_ui": true, "agent_type": null, "is_show_fieldname": null, "what_api_name": null, "default_component": "form_component", "ui_event_ids": [], "hidden_buttons": [], "hidden_components": ["component_BZTZR", "sale_log"], "namespace": null, "events": [], "components": [{"field_section": [], "buttons": [{"action_type": "default", "api_name": "Edit_button_default", "label": "编辑"}, {"action_type": "default", "api_name": "SaleRecord_button_default", "label": "销售记录"}, {"action_type": "default", "api_name": "Dial_button_default", "label": "打电话"}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "label": "更换负责人"}, {"action_type": "default", "api_name": "StartBPM_button_default", "label": "发起业务流程"}, {"action_type": "default", "api_name": "Abolish_button_default", "label": "作废"}, {"action_type": "default", "api_name": "StartStagePropellor_button_default", "label": "发起阶段推进器"}, {"action_type": "default", "api_name": "Lock_button_default", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "label": "解锁"}, {"action_type": "default", "api_name": "Clone_button_default", "label": "复制"}, {"action_type": "default", "api_name": "ChangePartner_button_default", "label": "更换合作伙伴"}, {"action_type": "default", "api_name": "ChangePartnerOwner_button_default", "label": "更换外部负责人"}, {"action_type": "default", "api_name": "DeletePartner_button_default", "label": "移除合作伙伴"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "type": "simple", "exposedButton": 3}, {"field_section": [{"field_name": "owner", "render_type": "employee"}, {"field_name": "owner_department", "render_type": "text"}, {"field_name": "last_modified_time", "render_type": "date_time"}, {"field_name": "record_type", "render_type": "record_type"}], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "type": "top_info"}, {"field_section": [], "buttons": [], "api_name": "relevant_team_component", "related_list_name": "", "header": "相关团队", "nameI18nKey": "paas.udobj.constant.relevant_team", "type": "user_list"}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "name"}, {"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}, {"is_readonly": true, "is_required": false, "render_type": "text", "field_name": "record_id"}, {"is_readonly": true, "is_required": false, "render_type": "text", "field_name": "qywx_user_id"}, {"is_readonly": true, "is_required": false, "render_type": "object_reference", "field_name": "user_id"}, {"is_readonly": true, "is_required": false, "render_type": "object_reference", "field_name": "external_user_id"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "leads_id"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "custom_id"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "contact_id"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "resource"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "add_time"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "marketing_event_id"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "friend_status"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息"}, {"show_header": true, "form_fields": [{"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "group_a8c39", "tab_index": "ltr", "column": 2, "header": "系统信息"}], "buttons": [], "api_name": "form_component", "related_list_name": "", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "type": "form", "column": 2}, {"field_section": [], "buttons": [], "api_name": "operation_log", "related_list_name": "", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "type": "related_record"}, {"components": [["form_component"], ["operation_log"], ["BPM_related_list"], ["Approval_related_list"], ["payment_recordrelated_list_generate_by_UDObjectServer"]], "buttons": [], "api_name": "tabs_1I6z2", "tabs": [{"api_name": "form_component_10aLQ", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "operation_log_H1s4Z", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log"}, {"api_name": "BPM_related_list_h9ilb", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list"}, {"api_name": "Approval_related_list_sLu86", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow"}, {"api_name": "tab_payment_recordrelated_list_generate_by_UDObjectServer", "header": "收款记录", "nameI18nKey": "paas.udobj.receipt"}], "header": "页签容器", "type": "tabs"}, {"relationType": 2, "buttons": [], "api_name": "payment_recordrelated_list_generate_by_UDObjectServer", "related_list_name": "payment_record_LIST", "ref_object_api_name": "payment_record", "header": "收款记录", "nameI18nKey": "paas.udobj.receipt", "type": "relatedlist"}, {"field_section": [], "buttons": [], "api_name": "BPM_related_list", "related_list_name": "", "ref_object_api_name": "BPM", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list", "type": "relatedlist"}, {"field_section": [], "buttons": [], "api_name": "Approval_related_list", "related_list_name": "", "ref_object_api_name": "Approval", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow", "type": "relatedlist"}], "enable_mobile_layout": false, "mobile_layout": {}}