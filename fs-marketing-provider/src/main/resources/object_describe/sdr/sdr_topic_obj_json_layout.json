{"components": [{"field_section": [], "buttons": [{"action_type": "default", "api_name": "Edit_button_default", "label": "编辑"}, {"action_type": "default", "api_name": "Dial_button_default", "label": "打电话"}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "label": "更换负责人"}, {"action_type": "default", "api_name": "StartBPM_button_default", "label": "发起业务流程"}, {"action_type": "default", "api_name": "Abolish_button_default", "label": "作废"}, {"action_type": "default", "api_name": "StartStagePropellor_button_default", "label": "发起阶段推进器"}, {"action_type": "default", "api_name": "Lock_button_default", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "label": "解锁"}, {"action_type": "default", "api_name": "Clone_button_default", "label": "复制"}, {"action_type": "default", "api_name": "ChangePartner_button_default", "label": "更换合作伙伴"}, {"action_type": "default", "api_name": "ChangePartnerOwner_button_default", "label": "更换外部负责人"}, {"action_type": "default", "api_name": "DeletePartner_button_default", "label": "移除合作伙伴"}, {"action_type": "default", "api_name": "EnterAccount_button_default", "label": "入账"}, {"action_type": "default", "api_name": "CancelEntry_button_default", "label": "取消入账"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "exposedButton": 3, "_id": "head_info", "type": "simple", "isSticky": false, "grayLimit": 1}, {"field_section": [{"field_name": "owner"}, {"field_name": "owner_department"}, {"field_name": "last_modified_time"}, {"field_name": "record_type"}], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "nameI18nKey": "paas.udobj.summary_info", "_id": "top_info", "type": "top_info", "isSticky": false, "grayLimit": 1}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "full_line": false, "is_required": true, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": false, "full_line": false, "is_required": true, "render_type": "text", "field_name": "topic_content"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "object_reference", "field_name": "score_model"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "object_reference", "field_name": "score_dimensions"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": false, "full_line": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "full_line": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}], "api_name": "base_field_section__c", "tab_index": "ltr", "defaultValue": "基本信息", "column": 2, "header": "基本信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": true, "full_line": false, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "full_line": false, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "full_line": false, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "full_line": false, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "group_F38nr__c", "tab_index": "ltr", "defaultValue": "系统信息", "column": 2, "header": "系统信息", "collapse": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "i18nInfoList": [{"customKey": "layout.sdr_topic_library__c.detail.layout_q1Cf8__c.group.base_field_section__c.header", "apiName": "base_field_section__c", "defaultValue": "基本信息", "preKey": "paas.metadata.layout.base_info", "languageInfo": {"de": "Grundlegende Informationen", "pt": "Informações Básicas", "sw-TZ": "Makundi ya Makundi", "zh-TW": "基本資訊", "ko-KR": "기본 정보", "pt-BR": "Informações básicas", "en": "Basic Information", "es-ES": "Información básica", "kk-KZ": "Басқару құралдары", "zh-CN": "基本信息", "ur-PK": "بیچارہ معلومات", "it-IT": "Informazioni di base", "ru-RU": "Основная информация", "pl-PL": "Podstawowe informacje", "ar": "البيانات الأساسية", "nl-NL": "Basisinformatie", "id-ID": "Informasi <PERSON>", "tr-TR": "<PERSON><PERSON> bilgiler", "fr-FR": "Informations de base", "vi-VN": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "ja-JP": "基本情報", "th-TH": "ข้อมูลพื้นฐาน"}, "value": "基本信息"}, {"customKey": "layout.sdr_topic_library__c.detail.layout_q1Cf8__c.group.group_F38nr__c.header", "apiName": "group_F38nr__c", "defaultValue": "系统信息", "value": "系统信息"}], "column": 2, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "_id": "form_component", "type": "form", "isSticky": false, "grayLimit": 1}, {"relationType": 2, "buttons": [], "api_name": "sdr_topic__c_topic__c_related_list", "related_list_name": "target_related_list_tt1123__c", "ref_object_api_name": "SDRConversationCaseObj", "limit": 1, "header": "沟通话术案例", "_id": "sdr_topic__c_topic__c_related_list", "type": "relatedlist", "field_api_name": "sdr_topic__c"}, {"field_section": [], "buttons": [], "api_name": "bpm_component", "related_list_name": "", "header": "业务流组件", "nameI18nKey": "paas.udobj.bpm_component", "_id": "bpm_component", "type": "bpm_component", "isSticky": false, "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "stage_component", "related_list_name": "", "header": "阶段推进器组件", "nameI18nKey": "paas.udobj.stage_component", "_id": "stage_component", "type": "stage_component", "isSticky": false, "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "approval_component", "related_list_name": "", "header": "审批流组件", "nameI18nKey": "paas.udobj.approval_component", "_id": "approval_component", "type": "approval_component", "isSticky": false, "grayLimit": 1}, {"components": [["form_component"], ["BPM_related_list"], ["Approval_related_list"], ["operation_log"], ["relevant_team_component"], ["sdr_recommended_topic__c_recommended_topic__c_related_list"], ["sdr_topic__c_topic__c_related_list"]], "buttons": [], "api_name": "tabs_6EN0p__c", "tabs": [{"api_name": "form_component_r4wvq__c", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "BPM_related_list_2S1Ky__c", "header": "业务流程", "nameI18nKey": "paas.udobj.process_list"}, {"api_name": "Approval_related_list_87h2Z__c", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow"}, {"api_name": "operation_log_1744800674886", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log"}, {"api_name": "relevant_team_component_1744800677709", "header": "相关团队", "nameI18nKey": "paas.udobj.constant.relevant_team"}, {"api_name": "tab_sdr_recommended_topic__c_recommended_topic__c_related_list", "header": "SDR推荐话题记录", "nameI18nKey": "sdr_recommended_topic__c.field.recommended_topic__c.reference_label"}, {"api_name": "tab_sdr_topic__c_topic__c_related_list", "header": "沟通话术案例"}], "header": "页签容器", "_id": "tabs_6EN0p__c", "type": "tabs", "isSticky": false}, {"show_header": true, "field_section": [], "buttons": [], "api_name": "operation_log", "related_list_name": "", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "_id": "operation_log", "type": "related_record", "isSticky": false, "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "relevant_team_component", "related_list_name": "", "header": "相关团队", "nameI18nKey": "paas.udobj.constant.relevant_team", "_id": "relevant_team_component", "type": "user_list", "isSticky": false, "grayLimit": 1}, {"relationType": 2, "buttons": [], "api_name": "sdr_recommended_topic__c_recommended_topic__c_related_list", "related_list_name": "target_related_list_urRVt__c", "ref_object_api_name": "SDRTopicRecommendationObj", "limit": 1, "header": "SDR推荐话题记录", "_id": "sdr_recommended_topic__c_recommended_topic__c_related_list", "type": "relatedlist", "field_api_name": "recommended_topic__c"}, {"field_section": [], "buttons": [], "api_name": "BPM_related_list", "related_list_name": "", "ref_object_api_name": "BPM", "limit": 1, "define_type": "general", "header": "业务流程", "nameI18nKey": "paas.udobj.process_list", "_id": "BPM_related_list", "type": "relatedlist", "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "Approval_related_list", "related_list_name": "", "ref_object_api_name": "Approval", "limit": 1, "define_type": "general", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow", "_id": "Approval_related_list", "type": "relatedlist", "grayLimit": 1}], "buttons": [], "package": "CRM", "i18nInfoList": [{"apiName": "display_name", "languageInfo": {"de": "Standardlayout", "pt": "disposição padrão", "sw-TZ": "Layout <PERSON>", "zh-TW": "預設布局", "ko-KR": "기본 배치", "pt-BR": "disposição padrão", "en": "Default Layout", "es-ES": "disposición predeterminada", "kk-KZ": "Стандартті түсінік", "zh-CN": "默认布局", "ur-PK": "میںزیکی طور پر ترتیب دیا جاتا ہے", "it-IT": "Disposizione predefinita", "ar": "تخطيط افتراضي", "pl-PL": "Domyślny układ", "ru-RU": "Стандартный макет", "nl-NL": "Standaard layout", "id-ID": "Layout Default", "tr-TR": "Varsayılan düzen", "fr-FR": "Disposition par défaut", "vi-VN": "<PERSON><PERSON><PERSON>n mặc định", "ja-JP": "デフォルトのレイアウト", "th-TH": "การตั้งค่าเริ่มต้น"}}], "ref_object_api_name": "SDRTopicObj", "layout_type": "detail", "hidden_buttons": ["SaleRecord_button_default"], "enable_mobile_layout": false, "ui_event_ids": [], "display_name": "默认布局", "is_default": true, "hidden_components": ["payment_recordrelated_list_generate_by_UDObjectServer__c", "sale_log", "biDashboardCom", "eservice_knowledge_recommend", "ShareGPT_widget", "richTextWidget"], "is_deleted": false, "api_name": "SDRTopicObj_default_layout", "layout_description": "", "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["stage_component", "bpm_component", "top_info", "tabs_6EN0p__c"], ["approval_component"]], "columns": [{"width": "auto"}, {"width": "500px", "retractable": true}]}]}, "events": []}