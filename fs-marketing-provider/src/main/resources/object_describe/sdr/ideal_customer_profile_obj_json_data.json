{"store_table_name": "ideal_customer_profile_obj", "package": "CRM", "is_active": true, "is_deleted": false, "api_name": "IdealCustomerProfileObj", "icon_path": "", "description": "", "define_type": "package", "display_name": "理想客户画像", "fields": {"contact_location": {"describe_api_name": "IdealCustomerProfileObj", "is_index": false, "is_active": true, "is_encrypted": false, "is_support_town": false, "auto_adapt_places": false, "is_unique": false, "description": "", "group_type": "area", "label": "联系人位置", "type": "group", "is_required": false, "api_name": "contact_location", "is_support_village": false, "define_type": "package", "is_single": false, "is_index_field": false, "fields": {"area_country": "contact_country", "area_location": "contact_belong_location", "area_detail_address": "contact_detail_address", "area_city": "contact_city", "area_province": "contact_province", "area_district": "contact_district"}, "is_extend": false, "help_text": "", "status": "new"}, "lock_rule": {"describe_api_name": "IdealCustomerProfileObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "label_r": "锁定规则", "is_index_field": false, "is_extend": false, "status": "new"}, "employee_scale": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "disable_after_filter": true, "description": "", "type": "select_one", "default_to_zero": false, "is_required": false, "enable_clone": true, "options": [{"label": "小型企业（1-49）", "value": "small"}, {"label": "中型企业（50-249）", "value": "medium"}, {"label": "大型企业（250-999）", "value": "large"}, {"label": "超大型企业（1,000+）", "value": "enterprise"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "is_extend": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "公司员工人数", "api_name": "employee_scale", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "industry_keywords": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "long_text", "default_to_zero": false, "is_required": false, "enable_clone": true, "define_type": "package", "is_single": false, "is_extend": false, "max_length": 2000, "is_index": true, "is_active": true, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "行业关键词", "api_name": "industry_keywords", "is_index_field": false, "help_text": "", "status": "new"}, "contact_city": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "联系人所在市", "type": "city", "used_in": "component", "is_required": false, "api_name": "contact_city", "options": [], "define_type": "package", "is_single": false, "cascade_parent_api_name": "contact_province", "is_extend": false, "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "contact_belong_location": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_location": false, "auto_adapt_places": false, "is_unique": false, "description": "", "label": "联系人所在定位", "is_geo_index": false, "type": "location", "used_in": "component", "is_required": false, "api_name": "contact_belong_location", "range_limit": false, "define_type": "package", "radius_range": 100, "is_single": false, "is_extend": false, "is_index_field": false, "help_text": "", "status": "new"}, "founding_year_min": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "最早成立年份", "type": "date", "time_zone": "GMT+8", "default_to_zero": false, "is_required": false, "enable_clone": true, "api_name": "founding_year_min", "define_type": "package", "date_format": "yyyy", "is_single": false, "is_extend": false, "is_index_field": false, "help_text": "", "status": "new"}, "contact_district": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "label": "联系人所在区", "type": "district", "used_in": "component", "is_required": false, "api_name": "contact_district", "options": [], "define_type": "package", "is_single": false, "cascade_parent_api_name": "contact_city", "is_extend": false, "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "extend_obj_data_id": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_required": false, "api_name": "extend_obj_data_id", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "is_extend": false, "status": "released", "max_length": 64}, "company_hq_province": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "省", "type": "province", "used_in": "component", "is_required": false, "api_name": "company_hq_province", "options": [], "define_type": "package", "is_single": false, "cascade_parent_api_name": "belong_country", "is_extend": false, "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "life_status_before_invalid": {"describe_api_name": "IdealCustomerProfileObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "enable_clone": true, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "label_r": "作废前生命状态", "is_index_field": false, "is_extend": false, "max_length": 256, "status": "new"}, "owner_department": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "label_r": "负责人主属部门", "is_extend": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "job_title": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "long_text", "default_to_zero": false, "is_required": false, "enable_clone": true, "define_type": "package", "is_single": false, "is_extend": false, "max_length": 2000, "is_index": true, "is_active": true, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "职位", "api_name": "job_title", "is_index_field": false, "help_text": "", "status": "new"}, "company_hq_location": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_location": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "定位", "is_geo_index": false, "type": "location", "used_in": "component", "is_required": false, "enable_clone": true, "api_name": "company_hq_location", "range_limit": false, "define_type": "package", "radius_range": 100, "is_single": false, "is_extend": false, "is_index_field": false, "help_text": "", "status": "new"}, "founding_year_max": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "最晚成立年份", "type": "date", "time_zone": "GMT+8", "default_to_zero": false, "is_required": false, "enable_clone": true, "api_name": "founding_year_max", "define_type": "package", "date_format": "yyyy", "is_single": false, "is_extend": false, "is_index_field": false, "help_text": "", "status": "new"}, "max_employee_growth": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "description": "", "type": "number", "decimal_places": 0, "default_to_zero": true, "is_required": false, "enable_clone": true, "define_type": "package", "is_single": false, "is_extend": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 14, "default_value": "", "label": "最高年员工人数增长", "api_name": "max_employee_growth", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "disable_after_filter": true, "type": "select_one", "default_to_zero": false, "is_required": false, "enable_clone": true, "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "label_r": "锁定状态", "is_extend": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "0", "label": "锁定状态", "is_need_convert": false, "api_name": "lock_status", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "package": {"describe_api_name": "IdealCustomerProfileObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "is_extend": false, "max_length": 200, "status": "released"}, "min_employee_growth": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "description": "", "type": "number", "decimal_places": 0, "default_to_zero": true, "is_required": false, "enable_clone": true, "define_type": "package", "is_single": false, "is_extend": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 14, "default_value": "", "label": "最低年员工人数增长", "api_name": "min_employee_growth", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "target_industries": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "long_text", "default_to_zero": false, "is_required": false, "enable_clone": true, "define_type": "package", "is_single": false, "is_extend": false, "max_length": 2000, "is_index": true, "is_active": true, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "行业", "api_name": "target_industries", "is_index_field": false, "help_text": "", "status": "new"}, "create_time": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "is_extend": false, "status": "released"}, "contact_province": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "联系人所在省", "type": "province", "used_in": "component", "is_required": false, "api_name": "contact_province", "options": [], "define_type": "package", "is_single": false, "cascade_parent_api_name": "contact_country", "is_extend": false, "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "revenue_min": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "description": "", "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "enable_clone": true, "define_type": "package", "is_single": false, "is_extend": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "length": 12, "default_value": "", "label": "最低年营收（万元）", "currency_unit": "￥", "currency_type": "oc", "api_name": "revenue_min", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "version": {"describe_api_name": "IdealCustomerProfileObj", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "版本", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "is_extend": false, "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_active": true, "is_unique": false, "description": "", "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "is_extend": false, "status": "released"}, "relevant_team": {"describe_api_name": "IdealCustomerProfileObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "label_r": "相关团队", "is_index_field": false, "is_extend": false, "help_text": "相关团队", "status": "new"}, "technologies": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "long_text", "default_to_zero": false, "is_required": false, "enable_clone": true, "define_type": "package", "is_single": false, "is_extend": false, "max_length": 2000, "is_index": true, "is_active": true, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "使用技术", "api_name": "technologies", "is_index_field": false, "help_text": "", "status": "new"}, "data_own_department": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "department", "is_required": false, "wheres": [], "optional_type": "department", "define_type": "package", "is_single": true, "label_r": "归属部门", "is_extend": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "归属部门", "is_need_convert": false, "api_name": "data_own_department", "is_index_field": false, "help_text": "", "status": "new"}, "name": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "name", "is_unique": true, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "is_single": false, "label_r": "名称", "is_extend": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "名称", "api_name": "name", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "_id": {"describe_api_name": "IdealCustomerProfileObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "ID", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "is_extend": false, "max_length": 200, "status": "released"}, "tenant_id": {"describe_api_name": "IdealCustomerProfileObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "租户ID", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "is_extend": false, "max_length": 200, "status": "released"}, "revenue_max": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "description": "", "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "enable_clone": true, "define_type": "package", "is_single": false, "is_extend": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "length": 12, "default_value": "", "label": "最高年营收（万元）", "currency_unit": "￥", "currency_type": "oc", "api_name": "revenue_max", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "belong_detail_address": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "used_in": "component", "is_required": false, "enable_clone": true, "define_type": "package", "input_mode": "", "is_single": false, "is_extend": false, "max_length": 300, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "详细地址", "api_name": "belong_detail_address", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "data_own_organization": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "department", "is_required": false, "wheres": [], "optional_type": "department", "define_type": "package", "is_single": true, "label_r": "归属组织", "is_extend": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "归属组织", "is_need_convert": false, "api_name": "data_own_organization", "is_index_field": false, "help_text": "", "status": "released"}, "company_hq_district": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "区", "type": "district", "used_in": "component", "is_required": false, "api_name": "company_hq_district", "options": [], "define_type": "package", "is_single": false, "cascade_parent_api_name": "company_hq_city", "is_extend": false, "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "recruitment_positions": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "long_text", "default_to_zero": false, "is_required": false, "enable_clone": true, "define_type": "package", "is_single": false, "is_extend": false, "max_length": 2000, "is_index": true, "is_active": true, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "公司招聘人员", "api_name": "recruitment_positions", "is_index_field": false, "help_text": "", "status": "new"}, "origin_source": {"describe_api_name": "IdealCustomerProfileObj", "is_index": false, "is_active": true, "is_unique": false, "description": "", "label": "数据来源", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "origin_source", "options": [{"label": "数据同步", "value": "0"}], "define_type": "system", "is_extend": false, "config": {"display": 0}, "status": "released"}, "lock_user": {"describe_api_name": "IdealCustomerProfileObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "enable_clone": true, "api_name": "lock_user", "define_type": "package", "is_single": true, "label_r": "加锁人", "is_index_field": false, "is_extend": false, "status": "new"}, "is_deleted": {"describe_api_name": "IdealCustomerProfileObj", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "删除状态", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "is_extend": false, "status": "released"}, "object_describe_api_name": {"describe_api_name": "IdealCustomerProfileObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "is_extend": false, "max_length": 200, "status": "released"}, "out_owner": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_active": true, "is_unique": false, "description": "", "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "is_extend": false, "config": {"display": 1}, "status": "released"}, "department": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "long_text", "default_to_zero": false, "is_required": false, "enable_clone": true, "define_type": "package", "is_single": false, "is_extend": false, "max_length": 2000, "is_index": true, "is_active": true, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "部门", "api_name": "department", "is_index_field": false, "help_text": "", "status": "new"}, "contact_detail_address": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "used_in": "component", "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "is_extend": false, "max_length": 300, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "联系人所在详细地址", "api_name": "contact_detail_address", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "owner": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "default_value": "", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "owner", "define_type": "package", "is_single": true, "is_extend": false, "label_r": "负责人", "is_index_field": false, "help_text": "", "status": "new"}, "last_modified_time": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "is_extend": false, "status": "released"}, "belong_country": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "国家", "type": "country", "used_in": "component", "is_required": false, "api_name": "belong_country", "options": [], "define_type": "package", "is_single": false, "is_extend": false, "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "life_status": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "disable_after_filter": true, "type": "select_one", "default_to_zero": false, "is_required": false, "enable_clone": true, "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "label_r": "生命状态", "is_extend": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "normal", "label": "生命状态", "is_need_convert": false, "api_name": "life_status", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_active": true, "is_unique": false, "description": "", "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "is_extend": false, "status": "released"}, "out_tenant_id": {"describe_api_name": "IdealCustomerProfileObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "is_extend": false, "config": {"display": 0}, "max_length": 200, "status": "released"}, "record_type": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "label_r": "业务类型", "is_index_field": false, "is_extend": false, "config": {}, "help_text": "", "status": "released"}, "company_hq": {"describe_api_name": "IdealCustomerProfileObj", "is_index": false, "is_active": true, "is_encrypted": false, "is_support_town": false, "auto_adapt_places": false, "description": "", "is_unique": false, "group_type": "area", "label": "公司总部位置", "type": "group", "is_required": false, "api_name": "company_hq", "is_support_village": false, "define_type": "package", "is_single": false, "is_index_field": false, "fields": {"area_country": "belong_country", "area_location": "company_hq_location", "area_detail_address": "belong_detail_address", "area_city": "company_hq_city", "area_province": "company_hq_province", "area_district": "company_hq_district"}, "is_extend": false, "help_text": "", "status": "new"}, "company_hq_city": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "市", "type": "city", "used_in": "component", "is_required": false, "api_name": "company_hq_city", "options": [], "define_type": "package", "is_single": false, "cascade_parent_api_name": "company_hq_province", "is_extend": false, "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "seniority": {"describe_api_name": "IdealCustomerProfileObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "disable_after_filter": true, "description": "", "type": "select_many", "default_to_zero": false, "is_required": false, "enable_clone": true, "options": [{"label": "控股人", "value": "owner"}, {"label": "创始人", "value": "founder"}, {"label": "CXO", "value": "CXO"}, {"label": "合伙人", "value": "partner"}, {"label": "副总裁", "value": "vp"}, {"label": "负责人", "value": "head"}, {"label": "总监", "value": "director"}, {"label": "经理", "value": "manager"}, {"label": "高级员工", "value": "senior"}, {"label": "初级员工", "value": "entry"}, {"label": "实习生", "value": "intern"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "is_extend": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": [], "label": "职级", "api_name": "seniority", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "contact_country": {"describe_api_name": "IdealCustomerProfileObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "联系人所在国家", "type": "country", "used_in": "component", "is_required": false, "api_name": "contact_country", "options": [], "define_type": "package", "is_single": false, "is_extend": false, "is_index_field": false, "config": {}, "help_text": "", "status": "new"}}, "actions": {}, "is_open_display_name": false}