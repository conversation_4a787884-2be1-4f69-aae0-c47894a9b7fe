{"package": "CRM", "is_active": true, "description": "用于记录营销通发送的邮件明细", "display_name": "邮件发送明细", "is_open_display_name": false, "index_version": 1, "icon_index": 32, "is_deleted": false, "api_name": "EmailSendRecordObj", "store_table_name": "email_send_record_obj", "icon_path": "", "is_udef": true, "define_type": "package", "fields": {"tenant_id": {"describe_api_name": "EmailSendRecordObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "max_length": 200, "status": "released", "is_extend": false}, "lock_rule": {"describe_api_name": "EmailSendRecordObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "label_r": "锁定规则", "is_index_field": false, "status": "new", "is_extend": false}, "data_own_organization": {"describe_api_name": "EmailSendRecordObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "归属组织", "type": "department", "is_need_convert": false, "is_required": false, "wheres": [], "api_name": "data_own_organization", "define_type": "package", "is_single": true, "label_r": "归属组织", "is_index_field": false, "help_text": "", "status": "released", "is_extend": false}, "open_count": {"describe_api_name": "EmailSendRecordObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "number", "decimal_places": 0, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 4, "is_index": true, "is_active": true, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 4, "default_value": "", "label": "打开次数", "api_name": "open_count", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new", "is_extend": false}, "lock_user": {"describe_api_name": "EmailSendRecordObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "label_r": "加锁人", "is_index_field": false, "status": "new", "is_extend": false}, "fail_reason": {"describe_api_name": "EmailSendRecordObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "失败原因", "api_name": "fail_reason", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new", "is_extend": false}, "is_deleted": {"describe_api_name": "EmailSendRecordObj", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "status": "released", "is_extend": false}, "marketing_activity_id": {"describe_api_name": "EmailSendRecordObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "label": "营销活动", "target_api_name": "MarketingActivityObj", "target_related_list_name": "target_related_list_1ilKV__c", "target_related_list_label": "邮件发送明细", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "marketing_activity_id", "is_index_field": true, "help_text": "", "status": "new", "is_extend": false}, "life_status_before_invalid": {"describe_api_name": "EmailSendRecordObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "label_r": "作废前生命状态", "is_index_field": false, "max_length": 256, "status": "new", "is_extend": false}, "object_describe_api_name": {"describe_api_name": "EmailSendRecordObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "max_length": 200, "status": "released", "is_extend": false}, "spam_report": {"describe_api_name": "EmailSendRecordObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": false, "label": "是否垃圾举报", "type": "true_or_false", "is_required": false, "api_name": "spam_report", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "package", "is_single": false, "is_index_field": false, "help_text": "", "status": "new", "is_extend": false}, "owner_department": {"describe_api_name": "EmailSendRecordObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "label_r": "负责人主属部门", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new", "is_extend": false}, "out_owner": {"describe_api_name": "EmailSendRecordObj", "is_index": true, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "config": {"display": 1}, "status": "released", "description": "", "is_extend": false}, "send_status": {"describe_api_name": "EmailSendRecordObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "发送状态", "type": "select_one", "is_required": false, "api_name": "send_status", "options": [{"label": "待发送", "value": "wait_send"}, {"label": "已送达", "value": "sent"}, {"label": "未送达", "value": "un_send"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "is_index_field": false, "config": {}, "help_text": "", "status": "new", "is_extend": false}, "owner": {"describe_api_name": "EmailSendRecordObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "owner", "define_type": "package", "is_single": true, "label_r": "负责人", "is_index_field": false, "help_text": "", "status": "new", "is_extend": false}, "lock_status": {"describe_api_name": "EmailSendRecordObj", "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "label_r": "锁定状态", "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "0", "label": "锁定状态", "is_need_convert": false, "api_name": "lock_status", "is_index_field": false, "config": {}, "help_text": "", "status": "new", "is_extend": false}, "package": {"describe_api_name": "EmailSendRecordObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "max_length": 200, "status": "released", "is_extend": false}, "last_modified_time": {"describe_api_name": "EmailSendRecordObj", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released", "is_extend": false}, "create_time": {"describe_api_name": "EmailSendRecordObj", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released", "is_extend": false}, "click_count": {"describe_api_name": "EmailSendRecordObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "number", "decimal_places": 0, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 4, "is_index": true, "is_active": true, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 4, "default_value": "", "label": "点击次数", "api_name": "click_count", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new", "is_extend": false}, "life_status": {"describe_api_name": "EmailSendRecordObj", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "label_r": "生命状态", "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "normal", "label": "生命状态", "is_need_convert": false, "api_name": "life_status", "is_index_field": false, "config": {}, "help_text": "", "status": "new", "is_extend": false}, "last_modified_by": {"describe_api_name": "EmailSendRecordObj", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released", "description": "", "is_extend": false}, "send_time": {"describe_api_name": "EmailSendRecordObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "date_time", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "not_use_multitime_zone": false, "default_value": "", "label": "发送时间", "time_zone": "GMT+8", "api_name": "send_time", "date_format": "yyyy-MM-dd HH:mm", "is_index_field": false, "help_text": "", "status": "new", "is_extend": false}, "out_tenant_id": {"describe_api_name": "EmailSendRecordObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "config": {"display": 0}, "max_length": 200, "status": "released", "is_extend": false}, "version": {"describe_api_name": "EmailSendRecordObj", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released", "is_extend": false}, "created_by": {"describe_api_name": "EmailSendRecordObj", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released", "description": "", "is_extend": false}, "receiver": {"describe_api_name": "EmailSendRecordObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "default_value": "", "label": "收件邮箱", "type": "text", "is_required": false, "api_name": "receiver", "define_type": "package", "is_single": false, "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new", "is_extend": false}, "relevant_team": {"describe_api_name": "EmailSendRecordObj__c", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "label_r": "相关团队", "is_index_field": false, "help_text": "相关团队", "status": "new", "is_extend": false}, "record_type": {"describe_api_name": "EmailSendRecordObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "label_r": "业务类型", "is_index_field": false, "config": {}, "help_text": "", "status": "released", "is_extend": false}, "is_unsubscribe": {"describe_api_name": "EmailSendRecordObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": false, "label": "是否取消订阅", "type": "true_or_false", "is_required": false, "api_name": "is_unsubscribe", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "package", "is_single": false, "is_index_field": false, "help_text": "", "status": "new", "is_extend": false}, "data_own_department": {"describe_api_name": "EmailSendRecordObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "wheres": [], "api_name": "data_own_department", "define_type": "package", "is_single": true, "label_r": "归属部门", "is_index_field": false, "help_text": "", "status": "new", "is_extend": false}, "name": {"describe_api_name": "EmailSendRecordObj", "default_is_expression": false, "prefix": "{yyyy}-{mm}-{dd}", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": true, "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "postfix": "", "input_mode": "", "is_single": false, "label_r": "主属性", "max_length": 100, "is_index": true, "is_active": true, "auto_number_type": "normal", "is_encrypted": false, "serial_number": 6, "default_value": "{yyyy}-{mm}-{dd}000001", "label": "记录编号", "condition": "NONE", "api_name": "name", "func_api_name": "", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new", "is_extend": false}, "status_code": {"describe_api_name": "EmailSendRecordObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 12, "default_value": "", "label": "发件状态码", "api_name": "status_code", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new", "is_extend": false}, "_id": {"describe_api_name": "EmailSendRecordObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "max_length": 200, "status": "released", "is_extend": false}, "preview_url": {"describe_api_name": "EmailSendRecordObj__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "^(((http[s]?|ftp):\\/\\/|www\\.)[a-z0-9\\.\\-]+\\.([a-z]{2,4})|((http[s]?|ftp):\\/\\/)?(([01]?[\\d]{1,2})|(2[0-4][\\d])|(25[0-5]))(\\.(([01]?[\\d]{1,2})|(2[0-4][\\d])|(25[0-5]))){3})(:\\d+)?(\\/[a-z0-9\\$\\^\\*\\+\\?\\(\\)\\{\\}\\.\\-_~!@#%&:;\\/=<>]*)?", "description": "", "is_unique": false, "default_value": "", "label": "发送内容", "type": "url", "is_required": false, "api_name": "preview_url", "define_type": "package", "is_single": false, "is_index_field": false, "help_text": "", "status": "new", "is_extend": false}, "task_id": {"describe_api_name": "EmailSendRecordObj", "is_index": true, "is_active": true, "pattern": "", "is_unique": false, "description": "邮件任务Id", "label": "邮件任务Id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "task_id", "define_type": "package", "max_length": 200, "status": "released", "is_extend": false}, "extend_obj_data_id": {"is_required": false, "api_name": "extend_obj_data_id", "is_index": true, "status": "released", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "max_length": 64, "is_extend": false}, "business_type": {"default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "default_value": "", "label": "发送业务", "api_name": "business_type", "is_index_field": false, "status": "new", "help_text": ""}, "send_object": {"default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "index_name": "t_5", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "发送对象", "api_name": "send_object", "is_index_field": false, "status": "new", "help_text": ""}, "send_node": {"default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "index_name": "t_4", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "发送节点", "api_name": "send_node", "is_index_field": false, "status": "new", "help_text": ""}, "marketing_event_id": {"default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "label": "市场活动", "target_api_name": "MarketingEventObj", "target_related_list_name": "target_related_list_fk37d__c", "target_related_list_label": "邮件发送记录", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "marketing_event_id", "is_index_field": true, "help_text": "", "status": "new", "is_extend": false}}, "actions": {}}