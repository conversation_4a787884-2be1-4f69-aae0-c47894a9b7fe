package demo

import com.facishare.marketing.provider.crowd.dao.MarketingEventTimedTaskEntityDao
import org.apache.ibatis.datasource.pooled.PooledDataSource
import org.apache.ibatis.mapping.Environment
import org.apache.ibatis.session.Configuration
import org.apache.ibatis.session.SqlSession
import org.apache.ibatis.session.SqlSessionFactory
import org.apache.ibatis.session.SqlSessionFactoryBuilder
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory
import spock.lang.Specification

class TargetCrowdOperationDaoSpec extends Specification {

    SqlSession session = null
    MarketingEventTimedTaskEntityDao marketingEventTimedTaskEntityDao = null;

    void setup() {
        def dataSource = new PooledDataSource(
                "org.postgresql.Driver", // 数据库驱动类
                "********************************************", // 数据库 URL
                "mankeep_user", // 数据库用户名
                "Sj^K6V7^KN3MnrCxV6C8" // 数据库密码
        )
        def transactionFactory = new JdbcTransactionFactory()
        def environment = new Environment("development", transactionFactory, dataSource)
        def configuration = new Configuration(environment)

        // 注册 typeAliases
//        def typeAliasRegistry = configuration.getTypeAliasRegistry()
        // 注册 typeHandlers
//        def typeHandlerRegistry = configuration.getTypeHandlerRegistry()

        configuration.addMapper(MarketingEventTimedTaskEntityDao.class) // 添加映射器接口
        SqlSessionFactory sqlSessionFactory = new SqlSessionFactoryBuilder().build(configuration)
        def session = sqlSessionFactory.openSession()
        marketingEventTimedTaskEntityDao = session.getMapper(MarketingEventTimedTaskEntityDao)
    }

    def "get by id"() {
        when:
        def entity = marketingEventTimedTaskEntityDao.getById("0")

        then:
        entity == null
    }

    void cleanup() {
        if (session != null) {
            session.rollback()
            session.close()
        }
    }


}
