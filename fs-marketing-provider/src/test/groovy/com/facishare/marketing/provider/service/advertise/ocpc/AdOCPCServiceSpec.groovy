package com.facishare.marketing.provider.service.advertise.ocpc

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.arg.advertiser.AdOCPCBeforeAfterDataReportArg
import com.facishare.marketing.api.arg.advertiser.AdOCPCConfigArg
import com.facishare.marketing.api.arg.advertiser.AdOCPCConfigQueryArg
import com.facishare.marketing.api.arg.advertiser.AdOCPCConfigUpdateArg
import com.facishare.marketing.api.arg.advertiser.GetVirtualPhoneArg
import com.facishare.marketing.api.arg.advertiser.JointDebuggingArg
import com.facishare.marketing.api.arg.advertiser.OCPCUploadRuleCreateArg
import com.facishare.marketing.api.arg.advertiser.ReUploadAdDataArg
import com.facishare.marketing.provider.baidu.BaiduHttpManager
import com.facishare.marketing.provider.baidu.GetBaiduVirtualPhoneResult
import com.facishare.marketing.provider.baidu.RequestResult
import com.facishare.marketing.provider.baidu.ResultHeader
import com.facishare.marketing.provider.bo.advertise.AdConvertCostStatisticsBO
import com.facishare.marketing.provider.dao.EnterpriseMetaConfigDao
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignDAO
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignDataDAO
import com.facishare.marketing.provider.entity.EnterpriseMetaConfigEntity
import com.facishare.marketing.provider.entity.advertiser.ocpc.AdOCPCConfigEntity
import com.facishare.marketing.provider.entity.advertiser.ocpc.AdOCPCUploadRuleEntity
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignEntity
import com.facishare.marketing.provider.innerResult.BaiduUploadConvertResult
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCConfigManager
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCUploadManager
import com.facishare.marketing.provider.remote.CrmMetadataManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.service.advertiser.ocpc.AdOCPCServiceImpl
import com.fxiaoke.crmrestapi.common.data.InnerPage
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult
import com.fxiaoke.crmrestapi.service.MetadataActionService
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

class AdOCPCServiceSpec extends Specification {

    // 模拟依赖项
    def adOCPCConfigManager = Mock(AdOCPCConfigManager)
    def adAccountManager = Mock(AdAccountManager)
    def adOCPCUploadManager = Mock(AdOCPCUploadManager)
    def baiduHttpManager = Mock(BaiduHttpManager)
    def crmMetadataManager = Mock(CrmMetadataManager)
    def crmV2Manager = Mock(CrmV2Manager)
    def metadataActionService = Mock(MetadataActionService)
    def eIEAConverter = Mock(EIEAConverter)
    def baiduCampaignDAO = Mock(BaiduCampaignDAO)
    def enterpriseMetaConfigDao = Mock(EnterpriseMetaConfigDao)
    def objectDescribeService = Mock(ObjectDescribeService)
    def baiduCampaignDataDAO = Mock(BaiduCampaignDataDAO)
    def eieaConverter = Mock(EIEAConverter)


    // 待测系统
    def adOCPCService = new AdOCPCServiceImpl(
            "adOCPCConfigManager": adOCPCConfigManager,
            "adAccountManager": adAccountManager,
            "adOCPCUploadManager": adOCPCUploadManager,
            "baiduHttpManager": baiduHttpManager,
            "crmMetadataManager": crmMetadataManager,
            "crmV2Manager": crmV2Manager,
            "metadataActionService": metadataActionService,
            "eIEAConverter": eIEAConverter,
            "baiduCampaignDAO": baiduCampaignDAO,
            "enterpriseMetaConfigDao": enterpriseMetaConfigDao,
            "objectDescribeService": objectDescribeService,
            "baiduCampaignDataDAO": baiduCampaignDataDAO,
            "eieaConverter": eieaConverter
    )

    ResultHeader getHeader(status) {
        ResultHeader header = new ResultHeader()
        header.setStatus(status)
        header.setFailures([new ResultHeader.Failure(code: 1, message: "hhhh")])
        return header
    }

    @Unroll
    def "bindAccount"() {
        given:
        adOCPCConfigManager.saveOCPCConfig(*_) >> "id"
        adOCPCConfigManager.getById(*_) >> new AdOCPCConfigEntity(id: 1, jointDebuggingLandingUrl: "www")
        when:
        adOCPCService.saveOCPCConfig(arg)
        then:
        noExceptionThrown()
        where:
        arg << [new AdOCPCConfigArg(), new AdOCPCConfigArg(adAccountId: "ad", token: "token")]

    }

    @Unroll
    def "updateStatus"() {
        given:
        adOCPCConfigManager.getById(*_) >> entity
        adOCPCConfigManager.updateStatus(*_) >> { println "ddd" }
        when:
        adOCPCService.updateStatus(arg)
        then:
        noExceptionThrown()
        where:
        entity                   | arg
        null                     | null
        null                     | new AdOCPCConfigUpdateArg(id: 1, ea: "ea", status: 1, invalidRebateStatus: 1)
        new AdOCPCConfigEntity() | new AdOCPCConfigUpdateArg(id: 1, ea: "ea", status: 1, invalidRebateStatus: 1)

    }

    @Unroll
    def "jointDebugging"() {
        given:
        adAccountManager.queryAccountById(*_) >> adAccount
        adOCPCConfigManager.getByAdAccountId(*_) >> config
        baiduHttpManager.sendAdDataToBaidu(*_) >> result


        when:
        adOCPCService.jointDebugging(arg)
        then:
        noExceptionThrown()
        where:
        arg                                                                                             | adAccount                      | config                                                                          | result
        new JointDebuggingArg()                                                                         | null                           | null                                                                            | null
        new JointDebuggingArg(ea: "ea", adAccountId: "ad", landingUrl: "www", conversionType: 1)        | null                           | null                                                                            | null
        new JointDebuggingArg(ea: "ea", adAccountId: "ad", landingUrl: "www", conversionType: 1)        | new AdAccountEntity(ea: "ea2") | null                                                                            | null
        new JointDebuggingArg(ea: "ea", adAccountId: "ad", landingUrl: "www", conversionType: 1)        | new AdAccountEntity(ea: "ea")  | null                                                                            | null
        new JointDebuggingArg(ea: "ea", adAccountId: "ad", landingUrl: "www", conversionType: 1)        | new AdAccountEntity(ea: "ea")  | new AdOCPCConfigEntity()                                                        | null
        new JointDebuggingArg(ea: "ea", adAccountId: "ad", landingUrl: "bd_v", conversionType: 1)       | new AdAccountEntity(ea: "ea")  | new AdOCPCConfigEntity(token: "token", jointDebuggingLandingUrl: "www")         | null
        new JointDebuggingArg(ea: "ea", adAccountId: "ad", landingUrl: "bd_vid=eee", conversionType: 1) | new AdAccountEntity(ea: "ea")  | new AdOCPCConfigEntity(token: "token", jointDebuggingLandingUrl: "bd_vid=eeee") | null
        new JointDebuggingArg(ea: "ea", adAccountId: "ad", landingUrl: "bd_vid=eee", conversionType: 1) | new AdAccountEntity(ea: "ea")  | new AdOCPCConfigEntity(token: "token", jointDebuggingLandingUrl: "bd_vid=eeee") | new BaiduUploadConvertResult(status: 0)
        new JointDebuggingArg(ea: "ea", adAccountId: "ad", landingUrl: "bd_vid=eee", conversionType: 1) | new AdAccountEntity(ea: "ea")  | new AdOCPCConfigEntity(token: "token", jointDebuggingLandingUrl: "bd_vid=eeee") | new BaiduUploadConvertResult(status: 1, errors: [new BaiduUploadConvertResult.ErrorResult()])

    }

    @Unroll
    def "saveUploadRule"() {
        given:
        adOCPCUploadManager.saveUploadRule(*_) >> null
        when:
        adOCPCService.saveUploadRule(new OCPCUploadRuleCreateArg())
        then:
        noExceptionThrown()

    }

    @Shared
    def objectData = new ObjectData()

    @Unroll
    def "reUploadAdData"() {
        given:
        objectData.put("status", "fail")
        objectData.put("_id", "landing_page")
        objectData.put("ad_platform", "baidu")
        objectData.put("ad_account_id", "ss")
        objectData.put("landing_page", "landing_page")
        objectData.put("landing_page_url", "landing_page_url")
        objectData.put("convent_type", "2")
        crmMetadataManager.batchGetByIdsV3(*_) >> crmData

        adAccountManager.queryEnableAccountByEa(*_) >> adAccount
        adOCPCConfigManager.getByAdAccountIdList(*_) >> config
        eIEAConverter.enterpriseAccountToId(*_) >> 1
        baiduHttpManager.sendAdDataToBaidu(*_) >> result
        metadataActionService.edit(*_) >> null
        when:
        adOCPCService.reUploadAdData("1", arg)
        then:
        noExceptionThrown()
        where:
        arg                                   | crmData      | adAccount                                                       | config                                                 | result
        new ReUploadAdDataArg()               | null         | null                                                            | null                                                   | null
        new ReUploadAdDataArg(idList: ["11"]) | [objectData] | null                                                            | null                                                   | null
        new ReUploadAdDataArg(idList: ["11"]) | [objectData] | null                                                            | null                                                   | null
        new ReUploadAdDataArg(idList: ["11"]) | [objectData] | []                                                              | null                                                   | null
        new ReUploadAdDataArg(idList: ["11"]) | [objectData] | [new AdAccountEntity(id: "id", source: "百度", username: "ss")] | null                                                   | null
        new ReUploadAdDataArg(idList: ["11"]) | [objectData] | [new AdAccountEntity(id: "id", source: "百度", username: "ss")] | [new AdOCPCConfigEntity(status: 1, adAccountId: "id")] | new BaiduUploadConvertResult(status: 0)
        new ReUploadAdDataArg(idList: ["11"]) | [objectData] | [new AdAccountEntity(id: "id", source: "百度", username: "ss")] | [new AdOCPCConfigEntity(status: 1, adAccountId: "id")] | new BaiduUploadConvertResult(status: 1, errors: [new BaiduUploadConvertResult.ErrorResult()])
        new ReUploadAdDataArg(idList: ["11"]) | [objectData] | [new AdAccountEntity(id: "id", source: "百度", username: "ss")] | [new AdOCPCConfigEntity(status: 1, adAccountId: "id")] | new BaiduUploadConvertResult(status: 3, errors: [new BaiduUploadConvertResult.ErrorResult()])

    }

    @Unroll
    def "isOpenInvalidRebate"() {
        given:
        eieaConverter.enterpriseIdToAccount(*_) >> "ea"
        enterpriseMetaConfigDao.getByEa(*_) >> metaConfig
        adOCPCConfigManager.getByEaAndInvalidRebateStatus(*_) >> config
        when:
        adOCPCService.isOpenInvalidRebate(tenantId, 1)
        then:
        noExceptionThrown()
        where:
        tenantId | metaConfig                       | config
        null     | null                             | null
        1        | null                             | null
        1        | new EnterpriseMetaConfigEntity() | null
        1        | new EnterpriseMetaConfigEntity() | [new AdOCPCConfigEntity()]
    }

    @Unroll
    def "queryOCPCConfig"() {
        given:
        adAccountManager.queryAccountByEaAndSource(*_) >> adAccountEntityList
        adOCPCConfigManager.getByAdAccountIdList(*_) >> adOCPCConfigEntityList

        when:
        adOCPCService.queryOCPCConfig(arg)
        then:
        noExceptionThrown()
        where:
        arg                                            | adAccountEntityList             | adOCPCConfigEntityList
        new AdOCPCConfigQueryArg()                     | []                              | []
        new AdOCPCConfigQueryArg(adAccountSource: "5") | []                              | []
        new AdOCPCConfigQueryArg(adAccountSource: "1") | []                              | []
        new AdOCPCConfigQueryArg(adAccountSource: "1") | [new AdAccountEntity(id: "id")] | [new AdOCPCConfigEntity(adAccountId: "id", status: 1, invalidRebateStatus: 1)]
    }

    @Unroll
    def "getUploadRule"() {
        given:
        adOCPCUploadManager.getByEaAndAdAccountSourceAndConvertType(*_) >> [new AdOCPCUploadRuleEntity()]

        when:
        adOCPCService.getUploadRule("ea", source, 1)
        then:
        noExceptionThrown()
        where:
        source << [null, "5", "1"]
    }

    @Shared
    def shareObjectData = new ObjectData()

    @Unroll
    def "getVirtualPhone"() {
        given:
        crmV2Manager.getDetail(*_) >> leadData
        baiduCampaignDAO.queryCampaignByMarketingEventId(*_) >> baiduCampaignEntity
        adAccountManager.queryEnableAccountById(*_) >> adAccountEntity
        adOCPCConfigManager.getByAdAccountId(*_) >> ocpcConfig
        crmV2Manager.getDetail(*_) >> marketingPromotionSourceData
        baiduHttpManager.getVirtualPhone(*_) >> getBaiduVirtualPhoneResult
        shareObjectData.put("tel", "110")
        shareObjectData.put("marketing_event_id", marketingEventId)
        shareObjectData.put("marketing_promotion_source_id", marketingPromotionSourceId)
        shareObjectData.put("landing_url", landingUrl)
        shareObjectData.put("third_platform_data_id", thirdPlatformDataId)

        when:
        adOCPCService.getVirtualPhone(new GetVirtualPhoneArg(leadId: leadId))
        then:
        noExceptionThrown()
        where:
        leadId | leadData        | marketingEventId | marketingPromotionSourceId | landingUrl               | thirdPlatformDataId | baiduCampaignEntity                       | adAccountEntity                     | ocpcConfig                                                | marketingPromotionSourceData | getBaiduVirtualPhoneResult
        ""     | null            | null             | null                       | null                     | null                | null                                      | null                                | null                                                      | null                         | null
        "lead" | null            | null             | null                       | null                     | null                | null                                      | null                                | null                                                      | null                         | null
        "lead" | shareObjectData | null             | null                       | null                     | null                | null                                      | null                                | null                                                      | shareObjectData              | null
        "lead" | shareObjectData | "event"          | null                       | null                     | null                | null                                      | null                                | null                                                      | shareObjectData              | null
        "lead" | shareObjectData | "event"          | "promo"                    | null                     | null                | null                                      | null                                | null                                                      | shareObjectData              | null
        "lead" | shareObjectData | "event"          | "promo"                    | null                     | null                | new BaiduCampaignEntity(adAccountId: "a") | null                                | null                                                      | shareObjectData              | null
        "lead" | shareObjectData | "event"          | "promo"                    | null                     | null                | new BaiduCampaignEntity(adAccountId: "a") | new AdAccountEntity()               | null                                                      | shareObjectData              | null
        "lead" | shareObjectData | "event"          | "promo"                    | null                     | null                | new BaiduCampaignEntity(adAccountId: "a") | new AdAccountEntity()               | new AdOCPCConfigEntity()                                  | shareObjectData              | null
        "lead" | shareObjectData | "event"          | "promo"                    | null                     | null                | new BaiduCampaignEntity(adAccountId: "a") | new AdAccountEntity()               | new AdOCPCConfigEntity(status: 1)                         | shareObjectData              | null
        "lead" | shareObjectData | "event"          | "promo"                    | null                     | null                | new BaiduCampaignEntity(adAccountId: "a") | new AdAccountEntity()               | new AdOCPCConfigEntity(status: 1, invalidRebateStatus: 1) | shareObjectData              | null
        "lead" | shareObjectData | "event"          | "promo"                    | "www.baidu.com"          | null                | new BaiduCampaignEntity(adAccountId: "a") | new AdAccountEntity()               | new AdOCPCConfigEntity(status: 1, invalidRebateStatus: 1) | shareObjectData              | null
        "lead" | shareObjectData | "event"          | "promo"                    | "www.baidu.com?bd_vid=1" | null                | new BaiduCampaignEntity(adAccountId: "a") | new AdAccountEntity()               | new AdOCPCConfigEntity(status: 1, invalidRebateStatus: 1) | shareObjectData              | null
        "lead" | shareObjectData | "event"          | "promo"                    | "www.baidu.com?bd_vid=1" | "dddd"              | new BaiduCampaignEntity(adAccountId: "a") | new AdAccountEntity()               | new AdOCPCConfigEntity(status: 1, invalidRebateStatus: 1) | shareObjectData              | null
        "lead" | shareObjectData | "event"          | "promo"                    | "www.baidu.com?bd_vid=1" | "dddd"              | new BaiduCampaignEntity(adAccountId: "a") | new AdAccountEntity(source: "百度") | new AdOCPCConfigEntity(status: 1, invalidRebateStatus: 1) | shareObjectData              | new RequestResult(data: [])
        "lead" | shareObjectData | "event"          | "promo"                    | "www.baidu.com?bd_vid=1" | "dddd"              | new BaiduCampaignEntity(adAccountId: "a") | new AdAccountEntity(source: "百度") | new AdOCPCConfigEntity(status: 1, invalidRebateStatus: 1) | shareObjectData              | new RequestResult(data: [new GetBaiduVirtualPhoneResult(resCode: 102)])
        "lead" | shareObjectData | "event"          | "promo"                    | "www.baidu.com?bd_vid=1" | "dddd"              | new BaiduCampaignEntity(adAccountId: "a") | new AdAccountEntity(source: "百度") | new AdOCPCConfigEntity(status: 1, invalidRebateStatus: 1) | shareObjectData              | new RequestResult(data: [new GetBaiduVirtualPhoneResult(resCode: 101)])
    }

    @Unroll
    def "getAllOCPCMarketingEventId"() {
        given:
        crmV2Manager.getAllObjByQueryArg(*_) >> []
        when:
        adOCPCService.getAllOCPCMarketingEventId("ea")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "beforeAfterDataReport"() {
        given:
        eIEAConverter.enterpriseAccountToId(*_) >> 1
        objectDescribeService.getDescribe(*_) >> describeResult
        crmV2Manager.listCrmObjectByFilterV3(*_) >> adReturnObjectDataPage
        baiduCampaignDAO.queryByMarketingEventIdList(*_) >> baiduCampaignEntityList
        def spy = Spy(adOCPCService)
        spy.getAllOCPCMarketingEventId(*_) >> ["eventId"]
        baiduCampaignDataDAO.statisticsConvertCostData(*_) >>> [[new AdConvertCostStatisticsBO(actionDate: new Date(), convertCost: BigDecimal.ONE)], [new AdConvertCostStatisticsBO(actionDate: new Date(), convertCost: BigDecimal.ONE)]]
        shareObjectData.put("send_back_time", System.currentTimeMillis())

        when:
        spy.beforeAfterDataReport(new AdOCPCBeforeAfterDataReportArg(marketingEventIdList: eventIdList))
        then:
        noExceptionThrown()
        where:
        describeResult                                                                                    | adReturnObjectDataPage                     | baiduCampaignEntityList                   | eventIdList
        null                                                                                              | null                                       | null                                      | null
        new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ControllerGetDescribeResult()) | null                                       | null                                      | null
        new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ControllerGetDescribeResult()) | new InnerPage(dataList: [shareObjectData]) | null                                      | ["eventId"]
        new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ControllerGetDescribeResult()) | new InnerPage(dataList: [shareObjectData]) | [new BaiduCampaignEntity(campaignId: 1L)] | ["eventId"]
        new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ControllerGetDescribeResult()) | new InnerPage(dataList: [shareObjectData]) | [new BaiduCampaignEntity(campaignId: 1L)] | null
    }
}
