package com.facishare.marketing.provider.manager.crmobjectcreator

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.provider.manager.ObjectServiceManager
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe
import com.fxiaoke.crmrestapi.common.result.InnerResult
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult
import com.fxiaoke.crmrestapi.result.CreateObjectResult
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import spock.lang.Specification
import spock.lang.Unroll

class ContentPropagatioonDetailObjManagerSpec extends Specification {


    def objectDescribeCrmService = Mock(ObjectDescribeCrmService)
    def eieaConverter = Mock(EIEAConverter)


    def contentPropagationDetailObjManager = new ContentPropagationDetailObjManager(
            "objectDescribeCrmService": objectDescribeCrmService,
            "eieaConverter": eieaConverter
    )


    @Unroll
    def "getApiName"() {
        given:
        when:
        contentPropagationDetailObjManager.getApiName()
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getJsonData"() {
        given:
        when:
        contentPropagationDetailObjManager.getJsonData()
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getJsonLayout"() {
        given:
        when:
        contentPropagationDetailObjManager.getJsonLayout()
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getJsonListLayout"() {
        given:
        when:
        contentPropagationDetailObjManager.getJsonListLayout()
        then:
        noExceptionThrown()
    }
}
