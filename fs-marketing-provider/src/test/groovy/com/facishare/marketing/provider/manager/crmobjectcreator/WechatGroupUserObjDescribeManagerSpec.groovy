package com.facishare.marketing.provider.manager.crmobjectcreator

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.common.enums.qywx.AppScopeEnum
import com.facishare.marketing.provider.dao.qywx.QywxGroupCodeDAO
import com.facishare.marketing.provider.entity.QyWxAddressBookEntity
import com.facishare.marketing.provider.entity.qywx.QywxGroupCodeEntity
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity
import com.facishare.marketing.provider.innerResult.qywx.CustomerGroupDetailResult
import com.facishare.marketing.provider.manager.ObjectServiceManager
import com.facishare.marketing.provider.manager.QywxVirtualFsUserManager
import com.facishare.marketing.provider.manager.qywx.*
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.fxiaoke.crmrestapi.common.data.InnerPage
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe
import com.fxiaoke.crmrestapi.common.data.Page
import com.fxiaoke.crmrestapi.common.result.InnerResult
import com.fxiaoke.crmrestapi.common.result.Result
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult
import com.fxiaoke.crmrestapi.result.CreateObjectResult
import com.fxiaoke.crmrestapi.service.MetadataActionService
import com.fxiaoke.crmrestapi.service.MetadataControllerService
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

class WechatGroupUserObjDescribeManagerSpec extends Specification {

    def eieaConverter = Mock(EIEAConverter)
    def objectDescribeService = Mock(ObjectDescribeService)
    def objectServiceManager = Mock(ObjectServiceManager)
    def metadataActionService = Mock(MetadataActionService)
    def wechatWorkExternalUserObjManager = Mock(WechatWorkExternalUserObjManager)
    def metadataControllerService = Mock(MetadataControllerService)
    def objectDescribeCrmService = Mock(ObjectDescribeCrmService)
    def crmV2Manager = Mock(CrmV2Manager)
    def qywxAddressBookManager = Mock(QywxAddressBookManager)
    def qywxVirtualFsUserManager = Mock(QywxVirtualFsUserManager)
    def qywxGroupCodeDAO = Mock(QywxGroupCodeDAO)

    def wechatGroupUserObjDescribeManager = new WechatGroupUserObjDescribeManager(
            eieaConverter: eieaConverter,
            objectDescribeService: objectDescribeService,
            objectServiceManager: objectServiceManager,
            metadataActionService: metadataActionService,
            metadataControllerService: metadataControllerService,
            objectDescribeCrmService: objectDescribeCrmService,
            qywxGroupCodeDAO: qywxGroupCodeDAO,
            qywxAddressBookManager: qywxAddressBookManager,
            crmV2Manager: crmV2Manager,
            qywxVirtualFsUserManager: qywxVirtualFsUserManager,
            wechatWorkExternalUserObjManager: wechatWorkExternalUserObjManager,
    )


    @Shared
    def sharedObjectData = new ObjectData()

    @Unroll
    def "getOrCreateWechatGroupUserObjDescribe"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        objectDescribeService.getDescribe(*_) >> getDescribeResultResult
        objectServiceManager.createObject(*_) >> result
        when:
        wechatGroupUserObjDescribeManager.getOrCreateWechatGroupUserObjDescribe("ea")
        then:
        noExceptionThrown()
        where:
        getDescribeResultResult                                                                           | result
        new com.fxiaoke.crmrestapi.common.result.Result(code: -1)                                         | null
        new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ControllerGetDescribeResult()) | null
        new com.fxiaoke.crmrestapi.common.result.Result(code: *********,)                                 | new InnerResult(errCode: -1)
        new com.fxiaoke.crmrestapi.common.result.Result(code: *********,)                                 | new InnerResult(errCode: 0, result: new CreateObjectResult())
    }

    @Unroll
    def "batchAddWechatGroupUserToCrm"() {
        given:
        eieaConverter.enterpriseIdToAccount(*_) >> "ea"
        def spy = Spy(wechatGroupUserObjDescribeManager)
        spy.pageAddWechatGroupUserToCrm(*_) >> true
        spy.addWechatGroupTeamMember(*_) >> { printf "ddd" }
        when:
        spy.batchAddWechatGroupUserToCrm(1, new CustomerGroupDetailResult(qywxGroupChat: new CustomerGroupDetailResult.QywxGroupChat(qywxGroupMembers: [new CustomerGroupDetailResult.QywxGroupMember()])), "id", 1, AppScopeEnum.MARKETING)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "pageAddWechatGroupUserToCrm"() {
        given:
        eieaConverter.enterpriseIdToAccount(*_) >> "ea"
        def spy = Spy(wechatGroupUserObjDescribeManager)
        spy.doAddWechatGroupUserToCrm(*_) >> true
        when:
        spy.pageAddWechatGroupUserToCrm(1, [new CustomerGroupDetailResult.QywxGroupMember()], "id", 1, AppScopeEnum.MARKETING)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "replaceWechatGroupTeamMember"() {
        given:
        crmV2Manager.replaceTeamMember(*_) >> true
        qywxVirtualFsUserManager.getCurrentVirtualUserByEaAndQyIds(*_) >> [new QywxVirtualFsUserEntity(userId: 1)]
        when:
        wechatGroupUserObjDescribeManager.replaceWechatGroupTeamMember("ea", "id", [new CustomerGroupDetailResult.QywxGroupMember(groupMemberUserId: "id")], AppScopeEnum.MARKETING)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "addWechatGroupTeamMember"() {
        given:
        crmV2Manager.doAddTeamMemberToCrm(*_) >> null
        qywxVirtualFsUserManager.getCurrentVirtualUserByEaAndQyIds(*_) >> [new QywxVirtualFsUserEntity(userId: 1)]
        when:
        wechatGroupUserObjDescribeManager.addWechatGroupTeamMember("ea", "id", memberList, AppScopeEnum.MARKETING)
        then:
        noExceptionThrown()
        where:
        memberList << [[], [new CustomerGroupDetailResult.QywxGroupMember(groupMemberUserId: "id")]]
    }

    @Unroll
    def "doAddWechatGroupUserToCrm"() {
        given:
        qywxAddressBookManager.queryByEaAndUserId(*_) >> new QyWxAddressBookEntity(name: "name")
        eieaConverter.enterpriseIdToAccount(*_) >> "ea"
        qywxGroupCodeDAO.getByEaAndState(*_) >> new QywxGroupCodeEntity()
        metadataControllerService.list(*_) >> result
        metadataActionService.add(*_) >> null
        crmV2Manager.editObjectData(*_) >> null
        sharedObjectData.put("app_scope", ["CRM"])
        when:
        wechatGroupUserObjDescribeManager.doAddWechatGroupUserToCrm(1, qywxGroupMember, "id", 1, AppScopeEnum.MARKETING)
        then:
        noExceptionThrown()
        where:
        qywxGroupMember                                                                                                                                                  | result
        new CustomerGroupDetailResult.QywxGroupMember(groupMemberUserType: 2, joinTime: 1L, joinScene: 1, invitor: new CustomerGroupDetailResult.Invitor(), state: "dd") | new Result<Page<ObjectData>>(code: -1, data: new Page<ObjectData>(dataList: [sharedObjectData]))
        new CustomerGroupDetailResult.QywxGroupMember(groupMemberUserType: 2, joinTime: 1L, joinScene: 1, invitor: new CustomerGroupDetailResult.Invitor(), state: "dd") | new Result<Page<ObjectData>>(code: 0, data: new Page<ObjectData>(dataList: [sharedObjectData]))
    }

    @Unroll
    def "queryGroupUserObjectUserList"() {
        given:
        metadataControllerService.list(*_) >> new Result<Page<ObjectData>>(code: 0, data: new Page<ObjectData>(dataList: [sharedObjectData]))
        sharedObjectData.put("status", "0")
        when:
        wechatGroupUserObjDescribeManager.queryGroupUserObjectUserList(1, "id")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "pageDelWechatGroupUserCrm"() {
        given:
        eieaConverter.enterpriseIdToAccount(*_) >> 1
        metadataControllerService.list(*_) >> new Result<Page<ObjectData>>(code: 0, data: new Page<ObjectData>(dataList: [sharedObjectData]))
        crmV2Manager.editObjectData(*_) >> null
        when:
        wechatGroupUserObjDescribeManager.pageDelWechatGroupUserCrm(1, ["userId"], "chatId", 1, AppScopeEnum.MARKETING)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "delWechatGroupUserTeamMember"() {
        given:
        crmV2Manager.doRemoveTeamMemberToCrm(*_) >> { printf "dd" }
        when:
        wechatGroupUserObjDescribeManager.delWechatGroupUserTeamMember("ea", userIdList, "groupId")
        then:
        noExceptionThrown()
        where:
        userIdList << [[], [1]]
    }

    @Unroll
    def "doUpdateGroupUserToCrm"() {
        given:
        def spy = Spy(wechatGroupUserObjDescribeManager)
        spy.queryGroupUserObjectUserList(*_) >> ["user", "removeUser"]
        spy.pageDelWechatGroupUserCrm(*_) >> { printf "dd" }
        spy.delWechatGroupUserTeamMember(*_) >> { printf "dd" }
        spy.replaceWechatGroupTeamMember(*_) >> { printf "dd" }
        spy.pageAddWechatGroupUserToCrm(*_) >> true
        eieaConverter.enterpriseIdToAccount(*_) >> 1
        qywxVirtualFsUserManager.getCurrentVirtualUserByEaAndQyIds(*_) >> [new QywxVirtualFsUserEntity(userId: 1)]
        eieaConverter.enterpriseIdToAccount(*_) >> "ea"
        sharedObjectData.put("app_scope", ["CRM"])
        sharedObjectData.put("user_id", "user")
        metadataControllerService.list(*_) >> new Result<Page<ObjectData>>(code: 0, data: new Page<ObjectData>(dataList: [sharedObjectData]))
        metadataActionService.edit(*_) >> null
        when:
        spy.doUpdateGroupUserToCrm(1, detailResult, "id", 1, AppScopeEnum.MARKETING)
        then:
        noExceptionThrown()
        where:
        detailResult << [new CustomerGroupDetailResult(qywxGroupChat: new CustomerGroupDetailResult.QywxGroupChat()),
                         new CustomerGroupDetailResult(qywxGroupChat: new CustomerGroupDetailResult.QywxGroupChat(qywxGroupMembers: [new CustomerGroupDetailResult.QywxGroupMember(groupMemberUserId: "user2", joinTime: 1L, joinScene: 1)])),
                         new CustomerGroupDetailResult(qywxGroupChat: new CustomerGroupDetailResult.QywxGroupChat(qywxGroupMembers: [new CustomerGroupDetailResult.QywxGroupMember(groupMemberUserId: "user", joinTime: 1L, joinScene: 1)]))]
    }

    @Unroll
    def "tryUpdateCustomFieldLabel"() {
        given:
        def spy = Spy(wechatGroupUserObjDescribeManager)
        spy.getOrCreateWechatGroupUserObjDescribe(*_) >> objectDescribe
        eieaConverter.enterpriseIdToAccount(*_) >> 1
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        objectDescribeCrmService.updateCustomFieldDescribe(*_) >> null
        when:
        spy.tryUpdateCustomFieldLabel("ea")
        then:
        noExceptionThrown()
        where:
        objectDescribe << [null, new ObjectDescribe(fields: []), new ObjectDescribe(fields: ["qr_code_id": new Object(), "qr_code_name": new Object()])]
    }

    @Unroll
    def "queryGroupUserObjectUserListByV3"() {
        given:
        crmV2Manager.listCrmObjectByFilterV3(*_) >> innerGroupPage
        sharedObjectData.put("_id", "id")
        sharedObjectData.put("status", "0")
        when:
        wechatGroupUserObjDescribeManager.queryGroupUserObjectUserListByV3("ea", ["id"], AppScopeEnum.MARKETING)
        then:
        noExceptionThrown()
        where:
        innerGroupPage << [null, new InnerPage<>(dataList: [sharedObjectData])]
    }

    @Unroll
    def "addTeamMemberByQywxBindCrmEvent"() {
        given:
        crmV2Manager.countCrmObjectByFilterV3(*_) >> total
        crmV2Manager.listCrmObjectByFilterV3(*_) >> innerPage
        crmV2Manager.doAddTeamMemberToCrm(*_) >> null
        sharedObjectData.put("qw_group_chat_id", "id")
        when:
        wechatGroupUserObjDescribeManager.addTeamMemberByQywxBindCrmEvent("ea", "user", fsUserId)
        then:
        noExceptionThrown()
        where:
        fsUserId  | total | innerPage
        ********* | 0     | null
        1         | 0     | null
        1         | 1     | null
        1         | 1     | new InnerPage<>(dataList: [sharedObjectData])
    }

    @Unroll
    def "addAppSourceField"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        objectDescribeService.getDescribe(*_) >> objectDescribeResult
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        when:
        wechatGroupUserObjDescribeManager.addAppSourceField("ea")
        then:
        noExceptionThrown()
        where:
        objectDescribeResult << [null, new com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult>(code: 0, data: new ControllerGetDescribeResult(describe: new ObjectDescribe(fields: [])))]
    }

    @Unroll
    def "queryGroupUserByGroupId"() {
        given:
        crmV2Manager.countCrmObjectByFilterV3(*_) >> totalCouont
        crmV2Manager.listCrmObjectByFilterV3(*_) >> page
        when:
        wechatGroupUserObjDescribeManager.queryGroupUserByGroupId("ea", "id", null)
        then:
        noExceptionThrown()
        where:
        totalCouont | page
        0           | null
        1           | null
        1           | new InnerPage<>(dataList: [sharedObjectData])
    }

    @Shared
    def sharedObjectData2 = new ObjectData()

    @Unroll
    def "fixRepeatGroupUserObj"() {
        given:
        crmV2Manager.listCrmObjectScanByIdV3(*_) >>> [innerPage, null]
        crmV2Manager.countCrmObjectByFilterV3(*_) >> totalCount
        crmV2Manager.listCrmObjectByFilterV3(*_) >> page
        crmV2Manager.bulkInvalidIgnoreError(*_) >> null
        sharedObjectData.put("_id", "id")
        sharedObjectData.put("user_id", "id")
        sharedObjectData2.put("user_id", "id")
        when:
        wechatGroupUserObjDescribeManager.fixRepeatGroupUserObj("ea")
        then:
        noExceptionThrown()
        where:
        innerPage                                   | totalCount | page
        null                                        | 0          | null
        new InnerPage(dataList: [sharedObjectData]) | 0          | null
        new InnerPage(dataList: [sharedObjectData]) | 1          | null
        new InnerPage(dataList: [sharedObjectData]) | 1          | new InnerPage(dataList: [sharedObjectData, sharedObjectData2])
    }
}
