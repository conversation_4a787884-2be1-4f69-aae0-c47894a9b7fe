package com.facishare.marketing.provider.manager.crmobjectcreator

import com.beust.jcommander.internal.Maps
import com.facishare.converter.EIEAConverter
import com.facishare.marketing.provider.dao.marketingplugin.WeChatCouponDAO
import com.facishare.marketing.provider.entity.marketingplugin.WechatCouponEntity
import com.facishare.marketing.provider.innerResult.crm.CreateObjResult
import com.facishare.marketing.provider.manager.ObjectServiceManager
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult
import com.fxiaoke.crmrestapi.service.MetadataActionService
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import spock.lang.Specification
import spock.lang.Unroll

class CustomerServiceSessionObjManagerSpec extends Specification {


    def eieaConverter = Mock(EIEAConverter)
    def objectDescribeService = Mock(ObjectDescribeService)
    def objectDescribeCrmService = Mock(ObjectDescribeCrmService)




    def customerServiceSessionObjManager = new CustomerServiceSessionObjManager(
            "eieaConverter": eieaConverter,
            "objectDescribeService": objectDescribeService,
            "objectDescribeCrmService": objectDescribeCrmService
    )

    @Unroll
    def "getCustomerServiceSessionObjDescribe"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        objectDescribeService.getDescribe(*_) >> result
        when:
        customerServiceSessionObjManager.getCustomerServiceSessionObjDescribe("88146")
        then:
        noExceptionThrown()
        where:
        result << [new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ControllerGetDescribeResult()), new com.fxiaoke.crmrestapi.common.result.Result(code: -1, data: new ControllerGetDescribeResult())]
    }

    @Unroll
    def "tryUpdateCustomFieldLabel"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        def spy = Spy(customerServiceSessionObjManager)
        spy.getCustomerServiceSessionObjDescribe(*_) >> objectDescribe
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        when:
        spy.tryUpdateCustomFieldLabel("88146")
        then:
        noExceptionThrown()
        where:
        objectDescribe << [null, new ObjectDescribe(fields: Maps.newHashMap())]
    }

}
