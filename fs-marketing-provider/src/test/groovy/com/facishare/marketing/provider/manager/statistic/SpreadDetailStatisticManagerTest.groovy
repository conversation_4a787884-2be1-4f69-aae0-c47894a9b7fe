package com.facishare.marketing.provider.manager.statistic

import com.facishare.marketing.provider.dao.CardDAO
import com.facishare.marketing.provider.dao.FSBindDAO
import com.facishare.marketing.provider.dao.MarketingActivityObjectRelationDAO
import com.facishare.marketing.provider.dao.UserDAO
import com.facishare.marketing.provider.dao.dingding.DingAddressBookDAO
import com.facishare.marketing.provider.dao.qywx.QyWxAddressBookDAO
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeDAO
import com.facishare.marketing.provider.dao.qywx.QywxVirtualFsUserDAO
import com.facishare.marketing.provider.dao.statistic.SpreadDetailStatisticDao
import com.facishare.marketing.provider.dto.EmployeePromoteDetailDTO
import com.facishare.marketing.provider.entity.CardEntity
import com.facishare.marketing.provider.entity.DingAddressBookEntity
import com.facishare.marketing.provider.entity.FSBindEntity
import com.facishare.marketing.provider.entity.MarketingActivityObjectRelationEntity
import com.facishare.marketing.provider.entity.QyWxAddressBookEntity
import com.facishare.marketing.provider.entity.UserEntity
import com.facishare.marketing.provider.entity.qywx.QywxAddFanQrCodeEntity
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity
import com.facishare.marketing.provider.manager.kis.ObjectManager
import com.facishare.marketing.provider.manager.qywx.QywxAddressBookManager
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.statistic.outapi.result.ObjectActionStatisticsResult
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService
import com.fxiaoke.crmrestapi.common.data.InnerPage
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.result.Result
import spock.lang.*

class SpreadDetailStatisticManagerTest extends Specification {

    def spreadDetailStatisticManager = new SpreadDetailStatisticManager()

    def spreadDetailStatisticDao = Mock(SpreadDetailStatisticDao)
    def crmV2Manager = Mock(CrmV2Manager)
    def qyWxAddressBookDAO = Mock(QyWxAddressBookDAO)
    def qywxAddressBookManager = Mock(QywxAddressBookManager)
    def qywxVirtualFsUserDAO = Mock(QywxVirtualFsUserDAO)
    def dingAddressBookDAO = Mock(DingAddressBookDAO)
    def wechatWorkExternalUserObjManager = Mock(WechatWorkExternalUserObjManager)
    def objectManager = Mock(ObjectManager)
    def marketingActivityObjectRelationDAO = Mock(MarketingActivityObjectRelationDAO)
    def userMarketingStatisticService = Mock(UserMarketingStatisticService)
    def cardDAO = Mock(CardDAO)
    def userDAO = Mock(UserDAO)
    def fsBindDAO = Mock(FSBindDAO)
    def qywxAddFanQrCodeDAO = Mock(QywxAddFanQrCodeDAO)

    def setup() {
        spreadDetailStatisticManager.spreadDetailStatisticDao = spreadDetailStatisticDao
        spreadDetailStatisticManager.crmV2Manager = crmV2Manager
        spreadDetailStatisticManager.qyWxAddressBookDAO = qyWxAddressBookDAO
        spreadDetailStatisticManager.qywxAddressBookManager = qywxAddressBookManager
        spreadDetailStatisticManager.qywxVirtualFsUserDAO = qywxVirtualFsUserDAO
        spreadDetailStatisticManager.dingAddressBookDAO = dingAddressBookDAO
        spreadDetailStatisticManager.wechatWorkExternalUserObjManager = wechatWorkExternalUserObjManager
        spreadDetailStatisticManager.objectManager = objectManager
        spreadDetailStatisticManager.marketingActivityObjectRelationDAO = marketingActivityObjectRelationDAO
        spreadDetailStatisticManager.userMarketingStatisticService = userMarketingStatisticService
        spreadDetailStatisticManager.cardDAO = cardDAO
        spreadDetailStatisticManager.userDAO = userDAO
        spreadDetailStatisticManager.fsBindDAO = fsBindDAO
        spreadDetailStatisticManager.qywxAddFanQrCodeDAO = qywxAddFanQrCodeDAO
    }

    //已测试完成
    @Unroll
    def "syncData2EmployeePromoteDetailObjTest"() {
        given:
        spreadDetailStatisticDao.getEmployeePromoteDetail(*_) >> argEmployeePromoteDetailDTOs1 >> argEmployeePromoteDetailDTOs2
        qywxVirtualFsUserDAO.queryQyUserIdByVirtualInfo(*_) >> argFsUserId
        crmV2Manager.getOneByList(*_) >> argOneObjectData
        crmV2Manager.getDetail(*_) >> argObjectData
        crmV2Manager.addOrUpdateObjectDataAndChangOwner(*_) >> new Result()
        qywxAddressBookManager.queryByEaAndUserId(*_) >> new QyWxAddressBookEntity(userId:"100000001",name:"1133")
        dingAddressBookDAO.queryUserByEaAndDingUserId(*_) >> new DingAddressBookEntity(userId:"200000001",name:"1133")
        wechatWorkExternalUserObjManager.queryFsUserIdByRoles2(*_) >> 0

        when:
        spreadDetailStatisticManager.syncData2EmployeePromoteDetailObj("ea", "date")
        then:
        noExceptionThrown() // todo - validate something


        where:
        argEmployeePromoteDetailDTOs1                            | argEmployeePromoteDetailDTOs2                                               | argFsUserId | argOneObjectData                 | argObjectData
        []                                                       | []                                                                          | "fsUserId"  | new ObjectData()                 | new ObjectData()
        [new HashMap<String, Object>("fs_user_id": "100000001")] | [new HashMap<String, Object>("fs_user_id": "100000001", "date": "1000000")] | "100000001" | new ObjectData()                 | new ObjectData()
        [new HashMap<String, Object>("fs_user_id": "200000001")] | [new HashMap<String, Object>("fs_user_id": "200000001", "date": "1000000")] | "200000001" | new ObjectData()                 | new ObjectData()
        [new HashMap<String, Object>("fs_user_id": "1000")]      | [new HashMap<String, Object>("fs_user_id": "1000", "date": "1000000")]      | "200000001" | new ObjectData(spread_type: "1") | new ObjectData()
        [new HashMap<String, Object>("fs_user_id": "1000")]      | [new HashMap<String, Object>("fs_user_id": "1000", "date": "1000000")]      | "200000001" | new ObjectData(spread_type: "2") | new ObjectData()

    }

    def "syncData2EmployeePromoteDetailObjV2Test"() {
        given:
        spreadDetailStatisticDao.getEmployeePromoteDetailV2(*_) >> argEmployeePromoteDetailDTOs1 >> argEmployeePromoteDetailDTOs2
        crmV2Manager.getDetail(*_) >> new ObjectData(spread_type: "1")
        crmV2Manager.getOneByList(*_) >> new ObjectData(spread_type: "1")
        crmV2Manager.concurrentListCrmObjectByFilterV3(*_) >> new InnerPage<ObjectData>()
        crmV2Manager.addOrUpdateObjectDataAndChangOwner(*_) >> new Result()
        qywxAddressBookManager.queryByEaAndUserId(*_) >> new QyWxAddressBookEntity()
        qywxVirtualFsUserDAO.queryQyUserIdByVirtualInfo(*_) >> "queryQyUserIdByVirtualInfoResponse"
        qywxVirtualFsUserDAO.queryQyUserIdByUserIdAndEa(*_) >> new QywxVirtualFsUserEntity("id", "ea", 0, "corpId", "qyUserId", new GregorianCalendar(2024, Calendar.MAY, 11, 16, 3).getTime(), 0, new GregorianCalendar(2024, Calendar.MAY, 11, 16, 3).getTime())
        dingAddressBookDAO.queryUserByEaAndDingUserId(*_) >> new DingAddressBookEntity()
        wechatWorkExternalUserObjManager.queryFsUserIdByRoles2(*_) >> 0
        objectManager.getObjectName(*_) >> ["getObjectNameResponse": "getObjectNameResponse"]
        marketingActivityObjectRelationDAO.queryByMarketingActivityIdList(*_) >> [new MarketingActivityObjectRelationEntity("id", "ea", "marketingEventId", "marketingActivityId", 0, "objectId")]
        userMarketingStatisticService.getObjectActionStatistics(*_) >> new com.facishare.marketing.statistic.common.result.Result<List<ObjectActionStatisticsResult>>()
        cardDAO.queryCardInfoById(*_) >> new CardEntity()
        userDAO.queryByUid(*_) >> new UserEntity()
        fsBindDAO.queryFSBindByUid(*_) >> new FSBindEntity()
        qywxAddFanQrCodeDAO.queryByUserId(*_) >> new QywxAddFanQrCodeEntity()

        when:
        spreadDetailStatisticManager.syncData2EmployeePromoteDetailObjV2("ea", "date")
        then:
        noExceptionThrown() // todo - validate something

    }


    def "syncData2ContentPropagationDetailObjTest"() {
        given:
        spreadDetailStatisticDao.getContentPropagationDetail(*_) >> [["getContentPropagationDetailResponse": "getContentPropagationDetailResponse"]]
        crmV2Manager.addOrUpdateObjectDataAndChangOwner(*_) >> new com.fxiaoke.crmrestapi.common.result.Result()

        when:
        spreadDetailStatisticManager.syncData2ContentPropagationDetailObj("ea", "date")
        then:
        noExceptionThrown() // todo - validate something

    }

}