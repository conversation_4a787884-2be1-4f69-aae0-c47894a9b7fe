package com.facishare.marketing.provider.manager

import com.beust.jcommander.internal.Maps
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll
import com.facishare.marketing.common.typehandlers.value.FieldMappings
import com.facishare.marketing.provider.dao.ClueManagementDAO
import com.facishare.marketing.provider.entity.ClueManagementEntity
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity
import com.facishare.marketing.provider.manager.permission.DataPermissionManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.fxiaoke.crmrestapi.common.data.ObjectData
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

class ClueManagementManagerSpec extends Specification {

    def clueManagementDAO = Mock(ClueManagementDAO)
    def objectManager = Mock(com.facishare.marketing.provider.manager.kis.ObjectManager)
    def dataPermissionManager = Mock(DataPermissionManager)
    def crmV2Manager = Mock(CrmV2Manager)


    def clueManagementManager = new ClueManagementManager(
            clueManagementDAO: clueManagementDAO,
            objectManager: objectManager,
            dataPermissionManager: dataPermissionManager,
            crmV2Manager: crmV2Manager
    )

    @Unroll
    def "addCustomizeFormCommonData"() {
        given:
        clueManagementDAO.getSettingByEa(*_) >> managementEntity
        objectManager.getObjectName(*_) >> "oibj"
        objectManager.getObjectCreateUser(*_) >> 1

        when:
        clueManagementManager.addCustomizeFormCommonData(Maps.newHashMap(), "ea", objectId, 1, new CustomizeFormDataEnroll(marketingSourceType: 1, marketingSourceSite: "3", userAgent: "dd", ipAddr: "dd"))
        then:
        noExceptionThrown()
        where:
        objectId | managementEntity
        ""       | null
        "sss"    | null
        "aa"     | new ClueManagementEntity(turnOnCustomizeFormDataCommonMapping: false)
        "aa"     | new ClueManagementEntity(turnOnCustomizeFormDataCommonMapping: true, customizeFormDataCommonMapping:
                FieldMappings.newInstance([new FieldMappings.FieldMapping(),
                                           new FieldMappings.FieldMapping(mankeepFieldName: "enrollObjectName", crmFieldName: "a"),
                                           new FieldMappings.FieldMapping(mankeepFieldName: "enrollObjectCreateUser", crmFieldName: "a"),
                                           new FieldMappings.FieldMapping(mankeepFieldName: "marketingSourceType", crmFieldName: "a"),
                                           new FieldMappings.FieldMapping(mankeepFieldName: "marketingSourceSite", crmFieldName: "a"),
                                           new FieldMappings.FieldMapping(mankeepFieldName: "userAgent", crmFieldName: "a"),
                                           new FieldMappings.FieldMapping(mankeepFieldName: "ipAddr", crmFieldName: "a"),
                                           new FieldMappings.FieldMapping(crmFieldName: "data_own_organization", mankeepFieldName: "dd", defaultValue: "[\"11\"]")]))
    }


    @Shared
    def objectData = new ObjectData()

    @Shared
    def objectData2 = new ObjectData()

    @Shared
    def person = new ObjectData()

    @Unroll
    def "addCustomizeFormOrganizationData"() {
        given:
        objectData = new ObjectData()
        objectData.setOwner(-10000)
        objectData2 = new ObjectData()
        objectData2.setOwner(1000)
        dataPermissionManager.isOpenDataOwnOrganization(*_) >> isOpen
        crmV2Manager.getOneByList(*_) >> objectData11
        crmV2Manager.getObjectData(*_) >> personnelObj
        person = new ObjectData()
        person.put("data_own_organization", "[\"1\"]")
        when:
        clueManagementManager.addCustomizeFormOrganizationData(Maps.newHashMap(), "ea", eventId, new CustomizeFormDataEntity(createBy: createBy))
        then:
        noExceptionThrown()
        where:
        isOpen | objectData11 | personnelObj | createBy | eventId
        false  | null         | null         | 1        | null
        true   | null         | null         | -10000   | null
        true   | null         | null         | -10000   | "33"
        true   | objectData   | null         | -10000   | "33"
        true   | objectData   | null         | -10000   | "33"
        true   | objectData2  | null         | -10000   | "33"
        true   | person       | null         | -10000   | "33"
    }

    @Unroll
    def "getDefaultDataOwnOrganization"() {
        given:
        dataPermissionManager.isOpenDataOwnOrganization(*_) >> isOpen
        clueManagementDAO.getSettingByEa(*_) >> managementEntity

        when:
        clueManagementManager.getDefaultDataOwnOrganization("ea")
        then:
        noExceptionThrown()
        where:
        isOpen | managementEntity
        false  | null
        true  | null
        true   | new ClueManagementEntity(customizeFormDataCommonMapping:
                FieldMappings.newInstance([
                        new FieldMappings.FieldMapping(mankeepFieldName: "ipAddr", crmFieldName: "a"),
                        new FieldMappings.FieldMapping(crmFieldName: "data_own_organization", mankeepFieldName: "dd"),
                        new FieldMappings.FieldMapping(crmFieldName: "data_own_organization", mankeepFieldName: "dd", defaultValue: "[\"11\"]")]))
    }


}