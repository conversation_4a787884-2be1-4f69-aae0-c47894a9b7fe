package com.facishare.marketing.provider.manager

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.result.UserMarketingActionResult
import com.facishare.marketing.common.typehandlers.value.OfficialWebsiteEventContent
import com.facishare.marketing.common.util.DateUtil
import com.facishare.marketing.provider.dao.CustomizeFormDataObjectDAO
import com.facishare.marketing.provider.dao.officialWebsite.OfficialWebsiteDAO
import com.facishare.marketing.provider.dao.officialWebsite.OfficialWebsiteEventAttributesDAO
import com.facishare.marketing.provider.dao.officialWebsite.OfficialWebsiteTrackDAO
import com.facishare.marketing.provider.dto.CustomizeFormClueNumDTO
import com.facishare.marketing.provider.entity.CustomizeFormDataObjectEntity
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteEntity
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteEventAttributesEntity
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteTrackEntity
import com.facishare.marketing.provider.manager.object.ObjectBindDetailManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager
import com.fxiaoke.crmrestapi.common.data.LeadsData
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.Page
import com.fxiaoke.crmrestapi.common.result.Result
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

class OfficialWebsiteManagerSpec extends Specification {

    def officialWebsiteDAO = Mock(OfficialWebsiteDAO)
    def officialWebsiteTrackDAO = Mock(OfficialWebsiteTrackDAO)
    def customizeFormDataObjectDAO = Mock(CustomizeFormDataObjectDAO)
    def objectBindDetailManager = Mock(ObjectBindDetailManager)
    def crmV2Manager = Mock(CrmV2Manager)
    def eieaConverter = Mock(EIEAConverter)
    def objectDescribeCrmService = Mock(ObjectDescribeCrmService)
    def officialWebsiteEventAttributesDAO = Mock(OfficialWebsiteEventAttributesDAO)
    def campaignMergeDataResetManager = Mock(CampaignMergeDataResetManager)
    def metadataControllerServiceManager = Mock(MetadataControllerServiceManager)
    def objectDescribeService = Mock(ObjectDescribeService)

    def officialWebsiteManager = new OfficialWebsiteManager(
            officialWebsiteDAO: officialWebsiteDAO,
            officialWebsiteTrackDAO: officialWebsiteTrackDAO,
            customizeFormDataObjectDAO: customizeFormDataObjectDAO,
            objectBindDetailManager: objectBindDetailManager,
            crmV2Manager: crmV2Manager,
            eieaConverter: eieaConverter,
            objectDescribeCrmService: objectDescribeCrmService,
            officialWebsiteEventAttributesDAO: officialWebsiteEventAttributesDAO,
            campaignMergeDataResetManager: campaignMergeDataResetManager,
            metadataControllerServiceManager: metadataControllerServiceManager,
            objectDescribeService: objectDescribeService
    )


    @Unroll
    def "queryOfficialWebsiteTrackByEa"() {
        given:
        officialWebsiteDAO.getOfficialWebsiteById(*_) >> officialWebsiteEntity
        officialWebsiteDAO.getOfficialWebsiteByEa(*_) >> officialWebsiteEntity
        officialWebsiteTrackDAO.getTrackInfoByOfficialWebsiteId(*_) >> []
        when:
        officialWebsiteManager.queryOfficialWebsiteTrackByEa(ea, true, websiteId)
        then:
        noExceptionThrown()
        where:
        ea   | websiteId | officialWebsiteEntity
        null | null      | null
        "ea" | null      | null
        "ea" | "id"      | new OfficialWebsiteEntity(status: 1)
        "ea" | "id"      | new OfficialWebsiteEntity(status: 0)
    }

    @Unroll
    def "matchTrackInfo"() {
        given:
        when:
        officialWebsiteManager.matchTrackInfo(officialWebsiteTrackEntityList, "www.baidu.com?a=8")
        then:
        noExceptionThrown()
        where:
        officialWebsiteTrackEntityList << [[], [new OfficialWebsiteTrackEntity(trackUrl: "www.baidu.com*")], [new OfficialWebsiteTrackEntity(trackUrl: "www.fxiaoke.com")]]
    }

    @Unroll
    def "getFormListByWebsiteId"() {
        given:
        officialWebsiteDAO.getOfficialWebsiteById(*_) >> officialWebsiteEntity
        customizeFormDataObjectDAO.getObjectBindingFormList(*_) >> [new CustomizeFormDataObjectEntity(formId: "id")]
        objectBindDetailManager.getTargetObjectFormIds(*_) >> ["id": "id"]
        when:
        officialWebsiteManager.getFormListByWebsiteId(id)
        then:
        noExceptionThrown()
        where:
        id   | officialWebsiteEntity
        ""   | null
        "id" | null
        "id" | new OfficialWebsiteEntity()
    }

    @Unroll
    def "buildSourceNamePieChartData"() {
        given:
        when:
        officialWebsiteManager.buildSourceNamePieChartData([new CustomizeFormClueNumDTO(countName: "百度", count: 1), new CustomizeFormClueNumDTO(countName: "搜狗", count: 1),
                                                            new CustomizeFormClueNumDTO(countName: "360", count: 1), new CustomizeFormClueNumDTO(countName: "bing", count: 1),
                                                            new CustomizeFormClueNumDTO(countName: "神马", count: 1), new CustomizeFormClueNumDTO(countName: "今日头条", count: 1),
                                                            new CustomizeFormClueNumDTO(countName: "抖音", count: 1), new CustomizeFormClueNumDTO(countName: "腾讯", count: 1),
                                                            new CustomizeFormClueNumDTO(countName: "搜狐新闻", count: 1), new CustomizeFormClueNumDTO(countName: "凤凰网", count: 1),
                                                            new CustomizeFormClueNumDTO(countName: "网易新闻", count: 1), new CustomizeFormClueNumDTO(countName: "微信", count: 1),
                                                            new CustomizeFormClueNumDTO(countName: "公众号", count: 1), new CustomizeFormClueNumDTO(countName: "微博", count: 1),
                                                            new CustomizeFormClueNumDTO(countName: "UC", count: 1), new CustomizeFormClueNumDTO(countName: "知乎", count: 1),
                                                            new CustomizeFormClueNumDTO(countName: "EDM", count: 1), new CustomizeFormClueNumDTO(countName: "sms", count: 1),
                                                            new CustomizeFormClueNumDTO(countName: "email", count: 1), new CustomizeFormClueNumDTO(countName: "其他", count: 1),
                                                            new CustomizeFormClueNumDTO(countName: "hhh", count: 1)])
        then:
        noExceptionThrown()
    }

    @Unroll
    def "buildSourceNameLinearGraphData"() {
        given:
        def spy = Spy(officialWebsiteManager)
        spy.buildSourceNamePieChartData(*_) >> ["1": 1]
        when:
        spy.buildSourceNameLinearGraphData([new CustomizeFormClueNumDTO(simpleTime: "1", count: 1)])
        then:
        noExceptionThrown()
    }

    @Unroll
    def "buildSourceTypeHistogram"() {
        given:
        def spy = Spy(officialWebsiteManager)
        spy.buildSourceNamePieChartData(*_) >> ["1": 1]
        when:
        spy.buildSourceTypeHistogram([new CustomizeFormClueNumDTO(countName: "", count: 1), new CustomizeFormClueNumDTO(countName: "other", count: 1)])
        then:
        noExceptionThrown()
    }

    @Unroll
    def "appendLeadSource"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> fieldVos
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        when:
        officialWebsiteManager.appendLeadSource(ea)
        then:
        noExceptionThrown()
        where:
        ea   | fieldVos
        null | []
        "ea" | []
        "ea" | [new CrmUserDefineFieldVo(fieldName: "name")]
    }

    @Unroll
    def "appendLeadUtmField"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> fieldVos
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        when:
        officialWebsiteManager.appendLeadUtmField(ea)
        then:
        noExceptionThrown()
        where:
        ea   | fieldVos
        null | []
        "ea" | []
        "ea" | [new CrmUserDefineFieldVo(fieldName: "name")]
    }

    @Unroll
    def "resetUtmFieldName"() {
        given:
        campaignMergeDataResetManager.enterpriseStop(*_) >> isStop
        eieaConverter.enterpriseAccountToId(*_) >> 1
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> fieldVos
        objectDescribeCrmService.updateCustomFieldDescribe(*_) >> null
        when:
        officialWebsiteManager.resetUtmFieldName(ea)
        then:
        noExceptionThrown()
        where:
        ea   | isStop | fieldVos
        null | true   | []
        "ea" | true   | []
        "ea" | false  | []
        "ea" | false  | [new CrmUserDefineFieldVo(fieldName: "utm_medium__c"), new CrmUserDefineFieldVo(fieldName: "utm_source__c"),
                         new CrmUserDefineFieldVo(fieldName: "utm_campaign__c"), new CrmUserDefineFieldVo(fieldName: "utm_content__c"),
                         new CrmUserDefineFieldVo(fieldName: "utm_term__c")]
    }

    @Unroll
    def "appendLeadMarketingActivity"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> fieldVos
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        when:
        officialWebsiteManager.appendLeadMarketingActivity(ea)
        then:
        noExceptionThrown()
        where:
        ea   | fieldVos
        null | []
        "ea" | []
        "ea" | [new CrmUserDefineFieldVo(fieldName: "name")]
    }

    @Unroll
    def "appendLeadMarketingSpreadUser"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> fieldVos
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        when:
        officialWebsiteManager.appendLeadMarketingSpreadUser(ea)
        then:
        noExceptionThrown()
        where:
        ea   | fieldVos
        null | []
        "ea" | []
        "ea" | [new CrmUserDefineFieldVo(fieldName: "name")]
    }

    @Unroll
    def "getEventAttributesIdByCustomizeId"() {
        given:
        officialWebsiteEventAttributesDAO.getByEaCustomizeIdAndType(*_) >> officialWebsiteEventAttributesEntities
        officialWebsiteEventAttributesDAO.getByEaCustomizeIdAndType(*_) >> []
        when:
        officialWebsiteManager.getEventAttributesIdByCustomizeId(eventCustomizeId, attributesCustomizeId, "ea")
        then:
        noExceptionThrown()
        where:
        eventCustomizeId   | attributesCustomizeId | officialWebsiteEventAttributesEntities
        null               | null                  | []
        "eventCustomizeId" | null                  | []
        "eventCustomizeId" | null                  | [new OfficialWebsiteEventAttributesEntity()]
        "eventCustomizeId" | "id"                  | [new OfficialWebsiteEventAttributesEntity()]
    }

    @Unroll
    def "buildUserActionResult"() {
        given:
        officialWebsiteEventAttributesDAO.getDetailById(*_) >> officialWebsiteEventAttributesEntity
        when:
        officialWebsiteManager.buildUserActionResult(realActionType, new UserMarketingActionResult())
        then:
        noExceptionThrown()
        where:
        realActionType | officialWebsiteEventAttributesEntity
        112            | null
        112            | new OfficialWebsiteEventAttributesEntity(type: 0, content: new OfficialWebsiteEventContent())
        112            | new OfficialWebsiteEventAttributesEntity(type: 1, content: new OfficialWebsiteEventContent())
        112            | new OfficialWebsiteEventAttributesEntity(type: 1, parentId: "id", content: new OfficialWebsiteEventContent())
        111            | new OfficialWebsiteEventAttributesEntity(type: 1, parentId: "id", content: new OfficialWebsiteEventContent())
    }

    @Unroll
    def "getTotalWebsiteOnlineServiceLeads"() {
        given:
        metadataControllerServiceManager.getTotal(*_) >> 1
        when:
        officialWebsiteManager.getTotalWebsiteOnlineServiceLeads("ea", 1L, 1L)
        then:
        noExceptionThrown()
        where:
        realActionType | officialWebsiteEventAttributesEntity
        112            | null
        112            | new OfficialWebsiteEventAttributesEntity(type: 0, content: new OfficialWebsiteEventContent())
        112            | new OfficialWebsiteEventAttributesEntity(type: 1, content: new OfficialWebsiteEventContent())
        112            | new OfficialWebsiteEventAttributesEntity(type: 1, parentId: "id", content: new OfficialWebsiteEventContent())
        111            | new OfficialWebsiteEventAttributesEntity(type: 1, parentId: "id", content: new OfficialWebsiteEventContent())
    }

    @Unroll
    def "getOnlineServiceLeadsTotalMap"() {
        given:
        metadataControllerServiceManager.listResults(*_) >> result
        def leadsData1 = new LeadsData();
        leadsData1.put("marketing_event_id__r", "name")
        leadsData1.put("name", "name")

        def leadsData2 = new LeadsData();
        leadsData2.put("marketing_event_id__r", "name")
        leadsData2.put("name2", "name2")
        metadataControllerServiceManager.convert2LeadsDataList(*_) >> [leadsData1, leadsData2]

        when:
        officialWebsiteManager.getOnlineServiceLeadsTotalMap("ea", 1L, 1L)
        then:
        noExceptionThrown()
        where:
        result << [null, new Result(code: 0, data: new Page(total: 1001, dataList: [new ObjectData()]))]
    }

    @Shared
    def shareObjectData = new ObjectData()

    @Unroll
    def "getLeadsTotalByPromotionChannel"() {
        given:
        metadataControllerServiceManager.listResults(*_) >> result
        shareObjectData.put("create_time", System.currentTimeMillis())
        when:
        officialWebsiteManager.getLeadsTotalByPromotionChannel("ea", DateUtil.minusDay(new Date(), 3), new Date(), null)
        then:
        noExceptionThrown()
        where:
        result << [null, new Result(code: 0, data: new Page(total: 1001, dataList: [shareObjectData]))]
    }

    @Unroll
    def "appendLeadMarketingPartner"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        objectDescribeService.getDescribe(*_) >> result
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> fieldVos

        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        when:
        officialWebsiteManager.appendLeadMarketingPartner(ea)
        then:
        noExceptionThrown()
        where:
        ea   | result                      | fieldVos
        null | null                        | []
        "ea" | new Result(code: *********) | []
        "ea" | new Result(code: 0)         | []
        "ea" | new Result(code: 0)         | [new CrmUserDefineFieldVo(fieldName: "name")]
    }

    @Unroll
    def "updateMarketingObjStatus"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> fieldVos

        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        when:
        officialWebsiteManager.updateMarketingObjStatus(ea)
        then:
        noExceptionThrown()
        where:
        ea   | fieldVos
        null | []
        "ea" | []
        "ea" | [new CrmUserDefineFieldVo(fieldName: "status")]
    }

    @Unroll
    def "getById"() {
        given:
        officialWebsiteDAO.getOfficialWebsiteById(*_) >> null
        when:
        officialWebsiteManager.getById(id)
        then:
        noExceptionThrown()
        where:
        id << [null, "id"]
    }

    @Unroll
    def "isOfficialWebsite"() {
        given:
        officialWebsiteDAO.listOfficialWebsiteByEa(*_) >> officialWebsiteEntityList
        when:
        officialWebsiteManager.isOfficialWebsite("ea", url)
        then:
        noExceptionThrown()
        where:
        url               | officialWebsiteEntityList
        null              | []
        "www.fxiaoke.com" | []
        "www.fxiaoke.com" | [new OfficialWebsiteEntity(websiteUrl: "www.fxiaoke.com")]
        "www.baidu.com" | [new OfficialWebsiteEntity(websiteUrl: "www.fxiaoke.com")]
    }

}