package com.facishare.marketing.provider.manager.image.material

import com.facishare.marketing.provider.manager.FileV2Manager
import spock.lang.*

/**
 * Test for NormalCover
 * <AUTHOR>
 * @date 2024/6/26 14:24
 */
class NormalCoverTest extends Specification {

    def normalCover = new NormalCover()

    def fileV2Manager = Mock(FileV2Manager)

    def setup() {
        normalCover.fileV2Manager = fileV2Manager
    }

    @Unroll
    def "drawTest"() {
        given:
        fileV2Manager.uploadPic(*_) >> new FileV2Manager.FileManagerPicResult()
        fileV2Manager.downloadAFile(*_) >> []

        expect:
        normalCover.draw(params) == expectedResult

        where:
        params               || expectedResult
        ["uid": "params"] || "expectedResult"
    }

}