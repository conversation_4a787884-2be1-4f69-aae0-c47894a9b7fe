package com.facishare.marketing.provider.service.marketingAssistant

import com.facishare.marketing.api.arg.ExtendAccountArg
import com.facishare.marketing.api.arg.UpdateExtendAccountArg
import com.facishare.marketing.api.arg.marketingAssistant.AllSpreadWxTemplateNoticeSettingArg
import com.facishare.marketing.api.arg.marketingAssistant.UnionNoticeSettingArg
import com.facishare.marketing.api.result.marketingAssistant.AllSpreadWxTemplateNoticeSettingResult
import com.facishare.marketing.api.result.marketingAssistant.UnionNoticeSettingResult
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.provider.dao.EnterpriseExtendAccountConfigDao
import com.facishare.marketing.provider.dao.yxzs.YxzsAllSpreadWxTemplateNoticeSettingDAO
import com.facishare.marketing.provider.dao.yxzs.YxzsUnionMessageExtendSettingDAO
import com.facishare.marketing.provider.entity.EnterpriseExtendAccountConfigEntity
import com.facishare.marketing.provider.entity.marketingAssistant.YxzsAllSpreadWxTemplateNoticeSettingEntity
import com.facishare.marketing.provider.entity.marketingAssistant.YxzsUnionMessageExtendSettingEntity
import com.facishare.wechat.proxy.common.result.ModelResult
import com.facishare.wechat.union.core.api.model.result.BatchGetByWxAppIdsResult
import com.facishare.wechat.union.core.api.service.OuterServiceWechatService
import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.*

/**
 * Test for YxzsNoticeSendServiceImpl
 * <AUTHOR>
 * @date 2024/8/7 19:31
 */
class YxzsNoticeSendServiceImplSpecTest extends Specification {

    def yxzsNoticeSendServiceImpl = new YxzsNoticeSendServiceImpl()

    def yxzsUnionMessageExtendSettingDAO = Mock(YxzsUnionMessageExtendSettingDAO)
    def yxzsAllSpreadWxTemplateNoticeSettingDAO = Mock(YxzsAllSpreadWxTemplateNoticeSettingDAO)
    def enterpriseExtendAccountConfigDao = Mock(EnterpriseExtendAccountConfigDao)
    def outerServiceWechatService = Mock(OuterServiceWechatService)

    def setup() {
        yxzsNoticeSendServiceImpl.yxzsUnionMessageExtendSettingDAO = yxzsUnionMessageExtendSettingDAO
        yxzsNoticeSendServiceImpl.yxzsAllSpreadWxTemplateNoticeSettingDAO = yxzsAllSpreadWxTemplateNoticeSettingDAO
        yxzsNoticeSendServiceImpl.extendAccountConfigDao = enterpriseExtendAccountConfigDao
        yxzsNoticeSendServiceImpl.outerServiceWechatService = outerServiceWechatService

    }

    @Unroll
    def "queryUnionNoticeSettingTest"() {
        given:
        yxzsUnionMessageExtendSettingDAO.getByEa(*_) >> new YxzsUnionMessageExtendSettingEntity()

        expect:
        yxzsNoticeSendServiceImpl.queryUnionNoticeSetting(ea)

        where:
        ea   || expectedResult
        "ea" || new Result<UnionNoticeSettingResult>(0, "errMsg", new UnionNoticeSettingResult())
    }

    @Unroll
    def "updateUnionNoticeSettingTest"() {
        given:
        yxzsUnionMessageExtendSettingDAO.insert(*_) >> 0
        yxzsUnionMessageExtendSettingDAO.getByEa(*_) >> entity

        expect:
        yxzsNoticeSendServiceImpl.updateUnionNoticeSetting(arg)

        where:
        entity|arg                         || expectedResult
        new YxzsUnionMessageExtendSettingEntity()|new UnionNoticeSettingArg() || new Result<Void>(0, "errMsg", null)
        null|new UnionNoticeSettingArg() || new Result<Void>(0, "errMsg", null)

    }

    @Unroll
    def "queryAllSpreadWxTemplateNoticeSettingTest"() {
        given:
        yxzsAllSpreadWxTemplateNoticeSettingDAO.getByEa(*_) >> new YxzsAllSpreadWxTemplateNoticeSettingEntity()

        expect:
        yxzsNoticeSendServiceImpl.queryAllSpreadWxTemplateNoticeSetting(ea)

        where:
        ea   || expectedResult
        "ea" || new Result<AllSpreadWxTemplateNoticeSettingResult>(0, "errMsg", new AllSpreadWxTemplateNoticeSettingResult())
    }

    @Unroll
    def "updateAllSpreadWxTemplateNoticeSettingTest"() {
        given:
        yxzsAllSpreadWxTemplateNoticeSettingDAO.insert(*_) >> 0
        yxzsAllSpreadWxTemplateNoticeSettingDAO.getByEa(*_) >> entity

        expect:
        yxzsNoticeSendServiceImpl.updateAllSpreadWxTemplateNoticeSetting(arg)

        where:
        entity|arg                                       || expectedResult
        new YxzsAllSpreadWxTemplateNoticeSettingEntity()|new AllSpreadWxTemplateNoticeSettingArg() || new Result<Void>(0, "errMsg", null)
        null|new AllSpreadWxTemplateNoticeSettingArg() || new Result<Void>(0, "errMsg", null)

    }
    @Unroll
    def "queryExtendAccount"() {
        given:
        enterpriseExtendAccountConfigDao.getByEaAndAccountType(*_) >> entity
        def result = new ModelResult<List<BatchGetByWxAppIdsResult>>()
        result.setResult(Lists.newArrayList(new BatchGetByWxAppIdsResult(wxAppName: "wwww")))
        outerServiceWechatService.batchGetByWxAppIds(*_) >> result

        expect:
        def yxt666 =  yxzsNoticeSendServiceImpl.queryExtendAccount(arg)
        true

        where:
        entity|arg                                       || expectedResult
        null|new ExtendAccountArg(ea: "88146", accountType: "WeChatPublicAccount") || new Result<Void>(0, "errMsg", null)
        new EnterpriseExtendAccountConfigEntity()|new ExtendAccountArg(ea: "88146", accountType: "WeChatPublicAccount")  || new Result<Void>(0, "errMsg", null)

    }


    @Unroll
    def "updateExtendAccount"() {
        given:
        enterpriseExtendAccountConfigDao.getByEaAndAccountType(*_) >> entity
        def result = new ModelResult<List<BatchGetByWxAppIdsResult>>()
        result.setResult(Lists.newArrayList(new BatchGetByWxAppIdsResult(wxAppName: "wwww")))
        outerServiceWechatService.batchGetByWxAppIds(*_) >> result
        enterpriseExtendAccountConfigDao.insert(*_) >> 0
        enterpriseExtendAccountConfigDao.updateAccountIdByEaAndType(*_) >> 0


        expect:
        def yxt666 =  yxzsNoticeSendServiceImpl.updateExtendAccount(arg)
        true

        where:
        entity|arg                                       || expectedResult
        null|null || new Result<Void>(0, "errMsg", null)
        null|new UpdateExtendAccountArg(ea: "88146", accountType: "WeChatPublicAccount",accountId: "wwww") || new Result<Void>(0, "errMsg", null)
        new EnterpriseExtendAccountConfigEntity(accountId: "wwwwww")|new UpdateExtendAccountArg(ea: "88146", accountType: "WeChatPublicAccount",accountId: "www")  || new Result<Void>(0, "errMsg", null)
        new EnterpriseExtendAccountConfigEntity(accountId: "www")|new UpdateExtendAccountArg(ea: "88146", accountType: "WeChatPublicAccount",accountId: "www")  || new Result<Void>(0, "errMsg", null)


    }

}