package com.facishare.marketing.provider.manager

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.service.conference.ConferenceService
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.outapi.service.ClueDefaultSettingService
import com.facishare.marketing.provider.dao.*
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO
import com.facishare.marketing.provider.dao.conference.ConferenceDAO
import com.facishare.marketing.provider.dao.conference.ConferenceInvitationUserDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO
import com.facishare.marketing.provider.dao.member.MemberAccessibleCampaignDAO
import com.facishare.marketing.provider.dao.ticket.CustomizeTicketDAO
import com.facishare.marketing.provider.dao.ticket.WxTicketReceiveDAO
import com.facishare.marketing.provider.entity.ActivityEnrollDataEntity
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity
import com.facishare.marketing.provider.entity.ticket.CustomizeTicketReceiveEntity
import com.facishare.marketing.provider.entity.ticket.WxTicketReceiveEntity
import com.facishare.marketing.provider.manager.conference.ConferenceManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager
import com.facishare.marketing.provider.remote.CrmMetadataManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.CrmV2MappingManager
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager
import com.fxiaoke.crmrestapi.result.ActionBulkInvalidResult
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService
import spock.lang.Specification
import spock.lang.Unroll

/**
 * Test for CampaignMergeDataManager
 * <AUTHOR>
 * @date 2024/4/16 17:48
 */
/**
 * <AUTHOR>
 * @Date 2024/4/16
 * */
class CampaignMergeDataManagerTest extends Specification {

    def campaignMergeDataManager = new CampaignMergeDataManager()

    def customizeFormDataUserDAO = Mock(CustomizeFormDataUserDAO)
    def customizeFormDataDAO = Mock(CustomizeFormDataDAO)
    def customizeFormDataManager = Mock(CustomizeFormDataManager)
    def marketingEventManager = Mock(MarketingEventManager)
    def activityDAO = Mock(ActivityDAO)
    def activityManager = Mock(ActivityManager)
    def campaignMergeDataDAO = Mock(CampaignMergeDataDAO)
    def crmV2Manager = Mock(CrmV2Manager)
    def redisManager = Mock(RedisManager)
    def conferenceManager = Mock(ConferenceManager)
    def customizeTicketManager = Mock(CustomizeTicketManager)
    def conferenceDAO = Mock(ConferenceDAO)
    def activityEnrollDataDAO = Mock(ActivityEnrollDataDAO)
    def customizeTicketDAO = Mock(CustomizeTicketDAO)
    def wxTicketReceiveDAO = Mock(WxTicketReceiveDAO)
    def eieaConverter = Mock(EIEAConverter)
    def objectDescribeCrmService = Mock(ObjectDescribeCrmService)
    def conferenceInvitationUserDAO = Mock(ConferenceInvitationUserDAO)
    def metadataControllerServiceManager = Mock(MetadataControllerServiceManager)
    def memberAccessibleCampaignDAO = Mock(MemberAccessibleCampaignDAO)
    def campaignPayOrderDao = Mock(CampaignPayOrderDao)
    def marketingLiveDAO = Mock(MarketingLiveDAO)
    def triggerInstanceManager = Mock(TriggerInstanceManager)
    def userMarketingAccountDAO = Mock(UserMarketingAccountDAO)
    def userMarketingAccountManager = Mock(UserMarketingAccountManager)
    def userMarketingBrowserUserRelationDao = Mock(UserMarketingBrowserUserRelationDao)
    def userMarketingMiniappAccountRelationDao = Mock(UserMarketingMiniappAccountRelationDao)
    def userMarketingWxServiceAccountRelationDao = Mock(UserMarketingWxServiceAccountRelationDao)
    def fileV2Manager = Mock(FileV2Manager)
    def hexagonPageDAO = Mock(HexagonPageDAO)
    def marketingEventCommonSettingDAO = Mock(MarketingEventCommonSettingDAO)
    def clueDefaultSettingService = Mock(ClueDefaultSettingService)
    def crmV2MappingManager = Mock(CrmV2MappingManager)
    def crmMetadataManager = Mock(CrmMetadataManager)
    def conferenceService = Mock(ConferenceService)

    def setup() {
        campaignMergeDataManager.customizeFormDataUserDAO = customizeFormDataUserDAO
        campaignMergeDataManager.customizeFormDataDAO = customizeFormDataDAO
        campaignMergeDataManager.customizeFormDataManager = customizeFormDataManager
        campaignMergeDataManager.marketingEventManager = marketingEventManager
        campaignMergeDataManager.activityDAO = activityDAO
        campaignMergeDataManager.activityManager = activityManager
        campaignMergeDataManager.campaignMergeDataDAO = campaignMergeDataDAO
        campaignMergeDataManager.crmV2Manager = crmV2Manager
        campaignMergeDataManager.redisManager = redisManager
        campaignMergeDataManager.conferenceManager = conferenceManager
        campaignMergeDataManager.customizeTicketManager = customizeTicketManager
        campaignMergeDataManager.conferenceDAO = conferenceDAO
        campaignMergeDataManager.activityEnrollDataDAO = activityEnrollDataDAO
        campaignMergeDataManager.customizeTicketDAO = customizeTicketDAO
        campaignMergeDataManager.wxTicketReceiveDAO = wxTicketReceiveDAO
        campaignMergeDataManager.eieaConverter = eieaConverter
        campaignMergeDataManager.objectDescribeCrmService = objectDescribeCrmService
        campaignMergeDataManager.conferenceInvitationUserDAO = conferenceInvitationUserDAO
        campaignMergeDataManager.metadataControllerServiceManager = metadataControllerServiceManager
        campaignMergeDataManager.memberAccessibleCampaignDAO = memberAccessibleCampaignDAO
        campaignMergeDataManager.campaignPayOrderDao = campaignPayOrderDao
        campaignMergeDataManager.marketingLiveDAO = marketingLiveDAO
        campaignMergeDataManager.triggerInstanceManager = triggerInstanceManager
        campaignMergeDataManager.userMarketingAccountDAO = userMarketingAccountDAO
        campaignMergeDataManager.userMarketingAccountManager = userMarketingAccountManager
        campaignMergeDataManager.userMarketingBrowserUserRelationDao = userMarketingBrowserUserRelationDao
        campaignMergeDataManager.userMarketingMiniappAccountRelationDao = userMarketingMiniappAccountRelationDao
        campaignMergeDataManager.userMarketingWxServiceAccountRelationDao = userMarketingWxServiceAccountRelationDao
        campaignMergeDataManager.fileV2Manager = fileV2Manager
        campaignMergeDataManager.hexagonPageDAO = hexagonPageDAO
        campaignMergeDataManager.marketingEventCommonSettingDAO = marketingEventCommonSettingDAO
        campaignMergeDataManager.clueDefaultSettingService = clueDefaultSettingService
        campaignMergeDataManager.crmV2MappingManager = crmV2MappingManager
        campaignMergeDataManager.crmMetadataManager = crmMetadataManager
        campaignMergeDataManager.conferenceService = conferenceService
    }

    @Unroll
    def "deleteTest"() {
        given:
        customizeFormDataUserDAO.deleteById(*_) >> 0
        customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignId(*_) >> [new CustomizeFormDataUserEntity(saveCrmStatus: 0)]
        activityDAO.decEnrollCount(*_) >> 0
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> campaiginMergeDataResult
        campaignMergeDataDAO.deleteCampaignMergeData(*_) >> 0
        crmV2Manager.bulkInvalidWithResult(*_) >> new com.fxiaoke.crmrestapi.common.result.Result<ActionBulkInvalidResult>()
        activityEnrollDataDAO.getActivityEnrollDataById(*_) >> new ActivityEnrollDataEntity()
        activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(*_) >> [new ActivityEnrollDataEntity()]
        activityEnrollDataDAO.deleteById(*_) >> 0
        customizeTicketDAO.getTicketByAssociationAndDataUserId(*_) >> new CustomizeTicketReceiveEntity()
        customizeTicketDAO.deleteCustomizeTicketReceiveById(*_) >> 0
        wxTicketReceiveDAO.getWxTicketReceiveByFormDataUserId(*_) >> new WxTicketReceiveEntity()
        wxTicketReceiveDAO.deleteById(*_) >> 0

        expect:
        campaignMergeDataManager.delete(campaignId, ea, fsUserId) == expectedResult

        where:
        campaignId   | fsUserId | ea   || expectedResult || campaiginMergeDataResult
        null         | 0        | "ea" || Result.newSuccess() || null
        "campaignId" | 0        | "ea" || Result.newSuccess() || null
        "campaignId" | 0        | "ea" || Result.newSuccess() || new CampaignMergeDataEntity(campaignMembersObjId: 1)
    }

}