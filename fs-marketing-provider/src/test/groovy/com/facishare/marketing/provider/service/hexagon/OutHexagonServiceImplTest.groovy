package com.facishare.marketing.provider.service.hexagon

import com.facishare.marketing.api.result.hexagon.GetActivityCenterInfoResult
import com.facishare.marketing.api.result.hexagon.GetContentCenterInfoResult
import com.facishare.marketing.api.result.hexagon.GetPageDetailResult
import com.facishare.marketing.api.result.hexagon.HexagonFilePreviewResult
import com.facishare.marketing.api.result.hexagon.QueryEnterpriseCommerceInfoResult
import com.facishare.marketing.api.service.hexagon.HexagonService
import com.facishare.marketing.api.service.live.LiveService
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.outapi.arg.GetFilePreviewUrlByObjectArg
import com.facishare.marketing.provider.manager.kis.ObjectManager
import spock.lang.*

/**
 * Test for OutHexagonServiceImpl
 * <AUTHOR>
 * @date 2024/5/7 20:01
 */
class OutHexagonServiceImplTest extends Specification {

    def outHexagonServiceImpl = new OutHexagonServiceImpl()

    def hexagonService = Mock(HexagonService)
    def liveService = Mock(LiveService)
    def objectManager = Mock(ObjectManager)

    def setup() {
        outHexagonServiceImpl.hexagonService = hexagonService
        outHexagonServiceImpl.liveService = liveService
        outHexagonServiceImpl.objectManager = objectManager
    }

    @Unroll
    def "getHomepageDetailBySiteIdTest"() {
        given:
        hexagonService.getHomepageDetailBySiteId(*_) >> new Result<GetPageDetailResult>(0, "errMsg", new GetPageDetailResult())

        expect:
        outHexagonServiceImpl.getHomepageDetailBySiteId(type, siteId) 

        where:
        siteId   | type || expectedResult
        "siteId" | null    || new Result<com.facishare.marketing.outapi.result.GetPageDetailResult>(0, "errMsg", new com.facishare.marketing.outapi.result.GetPageDetailResult())
    }

    @Unroll
    def "getPageDetailTest"() {
        given:
        hexagonService.getPageDetail(*_) >> new Result<GetPageDetailResult>(0, "errMsg", new GetPageDetailResult())

        expect:
        outHexagonServiceImpl.getPageDetail(type, id) 

        where:
        id   | type || expectedResult
        "id" | null    || new Result<com.facishare.marketing.outapi.result.GetPageDetailResult>(0, "errMsg", new com.facishare.marketing.outapi.result.GetPageDetailResult())
    }

    @Unroll
    def "getFilePreviewUrlTest"() {
        given:
        hexagonService.getFilePreviewUrl(*_) >> new Result<HexagonFilePreviewResult>(0, "errMsg", new HexagonFilePreviewResult())

        expect:
        outHexagonServiceImpl.getFilePreviewUrl(siteId, npath, fileName) 

        where:
        npath   | fileName   | siteId   || expectedResult
        "npath" | "fileName" | "siteId" || new Result<com.facishare.marketing.outapi.result.HexagonFilePreviewResult>(0, "errMsg", new com.facishare.marketing.outapi.result.HexagonFilePreviewResult())
    }

    @Unroll
    def "getFilePreviewUrlByObjectTest"() {
        given:
        hexagonService.getFilePreviewUrlByObject(*_) >> new Result<HexagonFilePreviewResult>(0, "errMsg", new HexagonFilePreviewResult())

        expect:
        outHexagonServiceImpl.getFilePreviewUrlByObject(arg) 

        where:
        arg                                || expectedResult
        new GetFilePreviewUrlByObjectArg() || new Result<com.facishare.marketing.outapi.result.HexagonFilePreviewResult>(0, "errMsg", new com.facishare.marketing.outapi.result.HexagonFilePreviewResult())
    }

    @Unroll
    def "getLiveStatusTest"() {
        given:
        liveService.getLiveStatus(*_) >> new Result<Integer>(0, "errMsg", 0)

        expect:
        outHexagonServiceImpl.getLiveStatus(id) 

        where:
        id   || expectedResult
        "id" || new Result<Integer>(0, "errMsg", 0)
    }

    @Unroll
    def "queryEnterpriseCommerceInfoTest"() {
        given:
        hexagonService.queryEnterpriseCommerceInfo(*_) >> new Result<List<QueryEnterpriseCommerceInfoResult>>(0, "errMsg", [new QueryEnterpriseCommerceInfoResult()])

        expect:
        outHexagonServiceImpl.queryEnterpriseCommerceInfo(objectId, objectType, keyword) 

        where:
        keyword   | objectId   | objectType || expectedResult
        "keyword" | "objectId" | 0          || new Result<List<com.facishare.marketing.outapi.result.QueryEnterpriseCommerceInfoResult>>(0, "errMsg", [new com.facishare.marketing.outapi.result.QueryEnterpriseCommerceInfoResult()])
    }

    @Unroll
    def "getContentCenterInfoTest"() {
        given:
        hexagonService.getContentCenterInfo(*_) >> new Result<GetContentCenterInfoResult>(0, "errMsg", new GetContentCenterInfoResult())

        expect:
        outHexagonServiceImpl.getContentCenterInfo(ea, fsUserId, sync) 

        where:
        fsUserId | ea   | sync || expectedResult
        0        | "ea" | true || new Result<com.facishare.marketing.outapi.result.GetContentCenterInfoResult>(0, "errMsg", new com.facishare.marketing.outapi.result.GetContentCenterInfoResult())
    }

    @Unroll
    def "getActivityCenterInfoTest"() {
        given:
        hexagonService.getActivityCenterInfo(*_) >> new Result<GetActivityCenterInfoResult>(0, "errMsg", new GetActivityCenterInfoResult())

        expect:
        outHexagonServiceImpl.getActivityCenterInfo(ea, fsUserId, sync) 

        where:
        fsUserId | ea   | sync || expectedResult
        0        | "ea" | true || new Result<com.facishare.marketing.outapi.result.GetActivityCenterInfoResult>(0, "errMsg", new com.facishare.marketing.outapi.result.GetActivityCenterInfoResult())
    }

    @Unroll
    def "getEaByHexagonPageIdTest"() {
        given:
        objectManager.getObjectEa(*_) >> "getObjectEaResponse"

        expect:
        outHexagonServiceImpl.getEaByHexagonPageId(id) 

        where:
        id   || expectedResult
        "id" || new Result<String>(0, "errMsg", "data")
    }

}