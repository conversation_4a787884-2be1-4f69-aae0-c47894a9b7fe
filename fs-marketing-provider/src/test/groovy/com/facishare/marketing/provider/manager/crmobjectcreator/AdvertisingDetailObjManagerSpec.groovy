package com.facishare.marketing.provider.manager.crmobjectcreator

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.outapi.service.ClueDefaultSettingService
import com.facishare.marketing.provider.baidu.report.ReportApiManager
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesCampaignDAO
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDAO
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentCampaignDAO
import com.facishare.marketing.provider.dao.baidu.AdKeywordDAO
import com.facishare.marketing.provider.dao.baidu.AdObjectFieldMappingDAO
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignDAO
import com.facishare.marketing.provider.dto.AdReportDataDTO
import com.facishare.marketing.provider.entity.advertiser.AdCampaignEntity
import com.facishare.marketing.provider.entity.advertiser.headlines.AdLeadsMappingDataEntity
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentAdGroupEntity
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity
import com.facishare.marketing.provider.entity.baidu.AdKeywordEntity
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignEntity
import com.facishare.marketing.provider.innerArg.AdMarketingEventNameArg
import com.facishare.marketing.provider.innerArg.CreateAdvertisingDetailObjArg
import com.facishare.marketing.provider.manager.CustomizeFunctionManager
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager
import com.facishare.marketing.provider.manager.baidu.RefreshDataManager
import com.facishare.marketing.provider.remote.CrmMetadataManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager
import com.fxiaoke.crmrestapi.common.data.InnerPage
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe
import com.fxiaoke.crmrestapi.common.data.Page
import com.fxiaoke.crmrestapi.common.result.Result
import com.fxiaoke.crmrestapi.result.ActionAddResult
import com.fxiaoke.crmrestapi.result.ActionEditResult
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult
import com.fxiaoke.crmrestapi.result.DuplicateSearchResult
import com.fxiaoke.crmrestapi.result.DuplicatesearchQueryResult
import com.fxiaoke.crmrestapi.service.MetadataActionService
import com.fxiaoke.crmrestapi.service.ObjectDataService
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import com.google.common.collect.Maps
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

class AdvertisingDetailObjManagerSpec extends Specification {

    def crmMetadataManager = Mock(CrmMetadataManager)
    def crmV2Manager = Mock(CrmV2Manager)
    def baiduCampaignDAO = Mock(BaiduCampaignDAO)
    def objectDescribeCrmService = Mock(ObjectDescribeCrmService)
    def objectDescribeService = Mock(ObjectDescribeService)
    def eieaConverter = Mock(EIEAConverter)
    def adCommonManager = Mock(AdCommonManager)


    def advertisingDetailsObjManager = new AdvertisingDetailsObjManager(
            "crmMetadataManager": crmMetadataManager,
            "crmV2Manager": crmV2Manager,
            "objectDescribeCrmService": objectDescribeCrmService,
            "objectDescribeService": objectDescribeService,
            "eieaConverter": eieaConverter,
            "adCommonManager": adCommonManager,
            "baiduCampaignDAO": baiduCampaignDAO

    )


    @Shared
    def objectData = new ObjectData()

    @Unroll
    def "fixShenHuMarketingEventId"() {
        given:
        baiduCampaignDAO.queryCampaignByEa(*_) >> [new BaiduCampaignEntity(marketingEventId: "event1", campaignId: 1L)]
        crmV2Manager.countCrmObjectByFilterV3(*_) >> totalCount
        def objectData = new ObjectData()
        objectData.put("marketing_event_id", "event2")
        crmV2Manager.listCrmObjectScanByIdV3(*_) >> page
        crmV2Manager.editObjectData(*_) >> new Result(code: 1)
        when:
        advertisingDetailsObjManager.fixShenHuMarketingEventId("88146")
        then:
        noExceptionThrown()
        where:
        totalCount | page
        0          | null
        1          | null
        1          | new InnerPage(dataList: [objectData])
    }

    @Unroll
    def "addOCPCField"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 88146
        objectDescribeService.getDescribe(*_) >> describe
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        when:
        advertisingDetailsObjManager.addOCPCField("88146")
        then:
        noExceptionThrown()
        where:
        describe << [null, new com.fxiaoke.crmrestapi.common.result.Result(data: new ControllerGetDescribeResult(describe: new ObjectDescribe(fields: Maps.newHashMap())))]
    }

    @Unroll
    def "tryUpdateOrCreateObj"() {
        given:
        def objectData = new ObjectData();
        objectData.put("ad_account_id", "1")
        objectData.put("ad_campaign_group_id", "2")
        objectData.put("marketing_event_id", "3")
        objectData.put("_id", "3")
        def objectData2= new ObjectData();
        objectData2.put("ad_account_id", "1")
        objectData2.put("ad_campaign_group_id", "2")
        objectData2.put("marketing_event_id", "3")
        objectData2.put("_id", "4")
        crmMetadataManager.listV3(*_) >> new InnerPage(dataList: [objectData, objectData2])
        crmV2Manager.bulkInvalid(*_) >> []
        crmV2Manager.editObjectData(*_) >> null
        crmMetadataManager.addMetadata(*_) >>  map
        when:
        advertisingDetailsObjManager.tryUpdateOrCreateObj([new CreateAdvertisingDetailObjArg(ea: "ea", adAccountId: "1", launchDate: "2024-04-01", marketingEventId: "1", campaignOrAdGroupId: 1L), new CreateAdvertisingDetailObjArg(ea: "ea", adAccountId: "1", launchDate: "2024-04-01", marketingEventId: "3", campaignOrAdGroupId: 2L, click: 11L)])
        then:
        noExceptionThrown()
        where:
        map << [["_id":"dd"]]

    }

    @Unroll
    def "getApiName"() {
        given:
        when:
        advertisingDetailsObjManager.getApiName()
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getJsonData"() {
        given:
        when:
        advertisingDetailsObjManager.getJsonData()
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getJsonLayout"() {
        given:
        when:
        advertisingDetailsObjManager.getJsonLayout()
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getJsonListLayout"() {
        given:
        when:
        advertisingDetailsObjManager.getJsonListLayout()
        then:
        noExceptionThrown()
    }
}
