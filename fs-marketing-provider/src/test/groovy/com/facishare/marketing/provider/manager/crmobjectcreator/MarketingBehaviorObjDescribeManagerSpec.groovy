package com.facishare.marketing.provider.manager.crmobjectcreator


import com.facishare.converter.EIEAConverter
import com.facishare.marketing.provider.manager.ObjectServiceManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import spock.lang.Specification
import spock.lang.Unroll

class MarketingBehaviorObjDescribeManagerSpec extends Specification {


    def objectServiceManager = Mock(ObjectServiceManager)
    def eieaConverter = Mock(EIEAConverter)
    def objectDescribeService = Mock(ObjectDescribeService)

    def marketingBehaviorObjDescribeManager = new MarketingBehaviorObjDescribeManager(
            "objectServiceManager": objectServiceManager,
            "eieaConverter": eieaConverter,
            "objectDescribeService": objectDescribeService
    )

    @Unroll
    def "getOrCreateMarketingBehaviorObjDescribe"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        objectDescribeService.getDescribe(*_) >> getDescribeResultResult
        objectServiceManager.createObject(*_) >> result
        when:
        marketingBehaviorObjDescribeManager.getOrCreateMarketingBehaviorObjDescribe("ea")
        then:
        noExceptionThrown()
        where:
        getDescribeResultResult                                          | result
        new com.fxiaoke.crmrestapi.common.result.Result(code: *********) | null
        new com.fxiaoke.crmrestapi.common.result.Result(code: 0)         | null
        new com.fxiaoke.crmrestapi.common.result.Result(code: *********) | new com.fxiaoke.crmrestapi.common.result.InnerResult(errCode: -1)
        new com.fxiaoke.crmrestapi.common.result.Result(code: *********) | new com.fxiaoke.crmrestapi.common.result.InnerResult(errCode: 0)
    }


}
