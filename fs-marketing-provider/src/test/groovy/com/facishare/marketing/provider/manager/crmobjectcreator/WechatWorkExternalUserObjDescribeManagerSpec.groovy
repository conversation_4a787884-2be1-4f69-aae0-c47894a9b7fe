package com.facishare.marketing.provider.manager.crmobjectcreator

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.common.exception.OuterServiceRuntimeException
import com.facishare.marketing.provider.manager.ObjectServiceManager
import com.facishare.open.common.result.exception.BizException
import com.fxiaoke.crmrestapi.common.contants.CrmErrorCode
import com.fxiaoke.crmrestapi.common.data.FieldDescribe
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe
import com.fxiaoke.crmrestapi.common.result.InnerResult
import com.fxiaoke.crmrestapi.common.result.Result
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult
import com.fxiaoke.crmrestapi.result.CreateObjectResult
import com.fxiaoke.crmrestapi.result.FindTenantSceneListResult
import com.fxiaoke.crmrestapi.result.Scenes
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import com.fxiaoke.crmrestapi.service.TenantSceneService
import spock.lang.Specification

class WechatWorkExternalUserObjDescribeManagerSpec extends Specification {

    def eieaConverter = Mock(EIEAConverter)
    def objectDescribeService = Mock(ObjectDescribeService)
    def objectServiceManager = Mock(ObjectServiceManager)
    def objectDescribeCrmService = Mock(ObjectDescribeCrmService)
    def tenantSceneService = Mock(TenantSceneService)
    def wechatGroupUserObjDescribeManager = Mock(WechatGroupUserObjDescribeManager)
    def wechatGroupObjDescribeManager = Mock(WechatGroupObjDescribeManager)
    def wechatFriendsRecordObjDescribeManager = Mock(WechatFriendsRecordObjDescribeManager)

    WechatWorkExternalUserObjDescribeManager manager = new WechatWorkExternalUserObjDescribeManager(
            "eieaConverter": eieaConverter,
            "objectDescribeService": objectDescribeService,
            "objectServiceManager": objectServiceManager,
            "objectDescribeCrmService": objectDescribeCrmService,
            "tenantSceneService": tenantSceneService,
            "wechatGroupUserObjDescribeManager": wechatGroupUserObjDescribeManager,
            "wechatGroupObjDescribeManager": wechatGroupObjDescribeManager,
            "wechatFriendsRecordObjDescribeManager": wechatFriendsRecordObjDescribeManager
    )

    def "getOrCreateWxWorkExternalUserObjDescribe"() {
        given:
        manager.tryUpdateScene(*_) >> {}
        eieaConverter.enterpriseAccountToId(*_) >> 1
        objectDescribeService.getDescribe(*_) >> describeResult
        objectServiceManager.createObject(*_) >> createResult
        def mockFsEa = "mockFsEa"

        when:
        def result = manager.getOrCreateWxWorkExternalUserObjDescribe(mockFsEa)

        then:
        with(result) {
            result.id == id
        }

        where:
        describeResult                                                                                                                                 | createResult || id
        new Result<ControllerGetDescribeResult>(code: 0, message: "msg", data: new ControllerGetDescribeResult(describe: new ObjectDescribe(id: "1"))) | null         || "1"
    }

    def "getOrCreateWxWorkExternalUserObjDescribe with create"() {
        given:
        manager.tryUpdateScene(*_) >> {}
        eieaConverter.enterpriseAccountToId(*_) >> 1
        objectServiceManager.createObject(*_) >> createResult

        def tenantScenes = [new Scenes(api_name: "aaa__c"), new Scenes(api_name: "aaa")]
        tenantSceneService.findTenantSceneList(*_) >> new Result<FindTenantSceneListResult>(code: 0, data: new FindTenantSceneListResult(scenes: tenantScenes))
        tenantSceneService.updateTenantScene(*_) >> null

        def mockFsEa = "mockFsEa"

        when:
        def result = manager.getOrCreateWxWorkExternalUserObjDescribe(mockFsEa)

        then:
        1 * objectDescribeService.getDescribe(*_) >> new Result<ControllerGetDescribeResult>(code: CrmErrorCode.OBJECT_NOT_EXIST, message: "msg")
        1 * objectDescribeService.getDescribe(*_) >> new Result<ControllerGetDescribeResult>(code: 0, message: "msg", data: new ControllerGetDescribeResult(describe: new ObjectDescribe(id: "1")))

        with(result) {
            result.id == id
        }

        where:
        createResult                                                                                                                                    || id
        new InnerResult<CreateObjectResult>(errCode: 0, errMessage: "msg", result: new CreateObjectResult(objectDescribe: new ObjectDescribe(id: "1"))) || "1"
    }

    def "getOrCreateWxWorkExternalUserObjDescribe with create null"() {
        given:
        manager.tryUpdateScene(*_) >> {}
        eieaConverter.enterpriseAccountToId(*_) >> 1
        objectServiceManager.createObject(*_) >> createResult

        def tenantScenes = [new Scenes(api_name: "aaa__c"), new Scenes(api_name: "aaa")]
        tenantSceneService.findTenantSceneList(*_) >> new Result<FindTenantSceneListResult>(code: 0, data: new FindTenantSceneListResult(scenes: tenantScenes))
        tenantSceneService.updateTenantScene(*_) >> null

        def mockFsEa = "mockFsEa"

        when:
        def result = manager.getOrCreateWxWorkExternalUserObjDescribe(mockFsEa)

        then:
        1 * objectDescribeService.getDescribe(*_) >> new Result<ControllerGetDescribeResult>(code: CrmErrorCode.OBJECT_NOT_EXIST, message: "msg")
        1 * objectDescribeService.getDescribe(*_) >> new Result<ControllerGetDescribeResult>(code: 0, message: "msg", data: new ControllerGetDescribeResult(describe: null))

        with(result) {
            result.id == id
        }

        where:
        createResult                                                                                                                                    || id
        new InnerResult<CreateObjectResult>(errCode: 0, errMessage: "msg", result: new CreateObjectResult(objectDescribe: new ObjectDescribe(id: "1"))) || "1"
    }

    def "getOrCreateWxWorkExternalUserObjDescribe with throws"() {
        given:
        manager.tryUpdateScene(*_) >> {}
        eieaConverter.enterpriseAccountToId(*_) >> 1
        objectDescribeService.getDescribe(*_) >> describeResult
        objectServiceManager.createObject(*_) >> createResult
        def mockFsEa = "mockFsEa"

        when:
        manager.getOrCreateWxWorkExternalUserObjDescribe(mockFsEa)

        then:
        thrown(ex)

        where:
        describeResult                                                                                      | createResult                                                        || ex
        new Result<ControllerGetDescribeResult>(code: CrmErrorCode.NO_OPERATION_PERMISSION, message: "msg") | null                                                                || BizException
        new Result<ControllerGetDescribeResult>(code: CrmErrorCode.OBJECT_NOT_EXIST, message: "msg")        | new InnerResult<CreateObjectResult>(errCode: 20, errMessage: "msg") || OuterServiceRuntimeException
    }

    def "get"() {
        when:
        def apiName = manager.getApiName()
        def jsonData = manager.getJsonData()
        def jsonLayOut = manager.getJsonLayout()
        def jsonListLayOut = manager.getJsonListLayout()

        then:
        apiName == null
        jsonData == null
        jsonLayOut == null
        jsonListLayOut == null
    }

    def "tryUpdateWxWorkWxUnionId"() {
        given:
        objectDescribeService.getDescribe(*_) >> describe
        def mockEa = "ea"

        when:
        manager.tryUpdateWxWorkWxUnionId(mockEa)

        then:
        a == 1

        where:
        describe                                                                                                                                                                                                                         || a
        new Result<ControllerGetDescribeResult>(code: 0, message: "msg", data: new ControllerGetDescribeResult(describe: null))                                                                                                          || 1
        new Result<ControllerGetDescribeResult>(code: 0, message: "msg", data: new ControllerGetDescribeResult(describe: new ObjectDescribe(fields: new HashMap<String, FieldDescribe>(Collections.singletonMap("wx_union_id", null))))) || 1

    }

    def "tryAppendWxWorkAdderField"() {
        given:
        objectDescribeService.getDescribe(*_) >> describe
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        eieaConverter.enterpriseAccountToId(*_) >> 1
        def mockEa = "ea"

        when:
        manager.tryAppendWxWorkAdderField(mockEa)

        then:
        a == 1

        where:
        describe                                                                                                                                                                            || a
        new Result<ControllerGetDescribeResult>(code: 0, message: "msg", data: new ControllerGetDescribeResult(describe: null))                                                             || 1
        new Result<ControllerGetDescribeResult>(code: 0, message: "msg", data: new ControllerGetDescribeResult(describe: new ObjectDescribe(fields: new HashMap<String, FieldDescribe>()))) || 1

    }

    def "tryUpdateCustomFieldLabel"() {
        given:
        objectDescribeService.getDescribe(*_) >> describe
        objectDescribeCrmService.updateCustomFieldDescribe(*_) >> null
        eieaConverter.enterpriseAccountToId(*_) >> 1
        def mockEa = "ea"

        when:
        manager.tryUpdateCustomFieldLabel(mockEa)

        then:
        a == 1

        where:
        describe                                                                                                                                                                                                                                                        || a
        new Result<ControllerGetDescribeResult>(code: 0, message: "msg", data: new ControllerGetDescribeResult(describe: null))                                                                                                                                         || 1
        new Result<ControllerGetDescribeResult>(code: 0, message: "msg", data: new ControllerGetDescribeResult(describe: new ObjectDescribe(fields: new HashMap<String, FieldDescribe>(["wechat_work_create_time": null, "wx_work_adder": null, "add_source": null])))) || 1

    }

    def "addAppSourceField"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        objectDescribeService.getDescribe(*_) >> describe
        def mockEa = "ea"

        when:
        manager.addAppSourceField(mockEa)

        then:
        a == 1

        where:
        describe                                                                                                                                                                            || a
        null                                                                                                                                                                                || 1
        new Result<ControllerGetDescribeResult>(code: 2)                                                                                                                                    || 1
        new Result<ControllerGetDescribeResult>(code: 0, message: "msg", data: new ControllerGetDescribeResult(describe: new ObjectDescribe(fields: new HashMap<String, FieldDescribe>()))) || 1
    }

    def "addAllQywxObjectAppSourceField"() {
        given:
        wechatGroupUserObjDescribeManager.addAppSourceField(*_) >> {}
        wechatGroupObjDescribeManager.addAppSourceField(*_) >> {}
        wechatFriendsRecordObjDescribeManager.addAppSourceField(*_) >> {}

        when:
        manager.addAllQywxObjectAppSourceField("ea")

        then:
        1 == 1
    }
}
