package com.facishare.marketing.provider.service.wxthirdplatform

import com.alibaba.fastjson.JSON
import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.arg.CommitCodeAndSubmitAuditArg
import com.facishare.marketing.api.arg.DispatchRequestArg
import com.facishare.marketing.api.result.DispatchRequestResult
import com.facishare.marketing.api.result.QueryWxMiniListResult
import com.facishare.marketing.api.result.WxCodeTemplateResult
import com.facishare.marketing.api.result.WxTemplateVersionResult
import com.facishare.marketing.api.result.WxThirdPlatformDomainResult
import com.facishare.marketing.api.service.wxthirdplatform.WxThirdCloudInnerService
import com.facishare.marketing.common.enums.MiniappReleaseStatusEnum
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.provider.dao.MiniappReleaseRecordDao
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatEaBindDao
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatThirdPlatformConfigDao
import com.facishare.marketing.provider.entity.MiniappReleaseRecordEntity
import com.facishare.marketing.provider.entity.wxthirdplatform.EaWechatAccountBindEntity
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatThirdPlatformConfigEntity
import com.facishare.marketing.provider.entity.wxthirdplatform.WxMiniAppEntity
import com.facishare.marketing.provider.manager.RedisManager
import com.facishare.marketing.provider.manager.SettingManager
import com.facishare.marketing.provider.manager.miniAppSetting.MiniAppSettingManager
import com.facishare.marketing.provider.manager.qywx.HttpManager
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatThirdPlatformManager
import com.facishare.marketing.provider.manager.wxthirdplatform.WxCloudRestManager
import com.facishare.open.app.center.api.result.BaseResult
import com.facishare.open.app.center.api.service.OpenAppAdminService
import com.facishare.uc.api.model.enterprise.arg.BatchGetEnterpriseDataArg
import com.facishare.uc.api.model.enterprise.result.BatchGetEnterpriseDataResult
import com.facishare.uc.api.model.fscore.EnterpriseData
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.wechatrestapi.arg.GetAuditStatusArg
import com.fxiaoke.wechatrestapi.result.GetAuditStatusResult
import com.fxiaoke.wechatrestapi.result.GetCodeTemplateListResult
import com.fxiaoke.wechatrestapi.result.ModifyDomainResult
import com.fxiaoke.wechatrestapi.result.SetWebViewDomainResult
import com.fxiaoke.wechatrestapi.result.WechatBaseResult
import com.fxiaoke.wechatrestapi.service.miniapp.CodeManageService
import com.fxiaoke.wechatrestapi.service.miniapp.CodeTemplateService
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.TimeUnit

class WxThirdPlatformInnerSupportServiceImplCompleteTest extends Specification {

    def service = new WxThirdPlatformInnerSupportServiceImpl()

    // Mock all dependencies
    def eaWechatAccountBindDao = Mock(EaWechatAccountBindDao)
    def wechatThirdPlatformConfigDao = Mock(WechatThirdPlatformConfigDao)
    def wechatThirdPlatformManager = Mock(WechatThirdPlatformManager)
    def wechatAccountConfigDao = Mock(WechatAccountConfigDao)
    def wechatAccountManager = Mock(WechatAccountManager)
    def httpManager = Mock(HttpManager)
    def eieaConverter = Mock(EIEAConverter)
    def enterpriseEditionService = Mock(EnterpriseEditionService)
    def miniAppSettingManager = Mock(MiniAppSettingManager)
    def miniappReleaseRecordDao = Mock(MiniappReleaseRecordDao)
    def wxCloudRestManager = Mock(WxCloudRestManager)
    def settingManager = Mock(SettingManager)
    def codeTemplateService = Mock(CodeTemplateService)
    def codeManageService = Mock(CodeManageService)
    def redisManager = Mock(RedisManager)
    def wxThirdCloudInnerService = Mock(WxThirdCloudInnerService)
    def openAppAdminService = Mock(OpenAppAdminService)
    def wechatEaBindDao = Mock(WechatEaBindDao)

    def setup() {
        service.eaWechatAccountBindDao = eaWechatAccountBindDao
        service.wechatThirdPlatformConfigDao = wechatThirdPlatformConfigDao
        service.wechatThirdPlatformManager = wechatThirdPlatformManager
        service.wechatAccountConfigDao = wechatAccountConfigDao
        service.wechatAccountManager = wechatAccountManager
        service.httpManager = httpManager
        service.eieaConverter = eieaConverter
        service.enterpriseEditionService = enterpriseEditionService
        service.miniAppSettingManager = miniAppSettingManager
        service.miniappReleaseRecordDao = miniappReleaseRecordDao
        service.wxCloudRestManager = wxCloudRestManager
        service.settingManager = settingManager
        service.codeTemplateService = codeTemplateService
        service.codeManageService = codeManageService
        service.redisManager = redisManager
        service.wxThirdCloudInnerService = wxThirdCloudInnerService
        service.openAppAdminService = openAppAdminService
        service.wechatEaBindDao = wechatEaBindDao
        service.host = "https://test.com"
        service.marketingAppId = "testAppId"
    }

    @Unroll
    def "test getWxAppIdByFSPlatformIdAndEa with different scenarios"() {
        given:
        eaWechatAccountBindDao.getWxAppIdByEa(_, _) >> wxAppId

        when:
        def result = service.getWxAppIdByFSPlatformIdAndEa(fsPlatformId, ea)

        then:
        result.isSuccess() == expectedSuccess
        if (expectedSuccess) {
            result.getData() == wxAppId
        } else {
            result.getErrorCode() == SHErrorCode.EA_NOT_BIND_TO_MINIAPP
        }

        where:
        fsPlatformId | ea   | wxAppId    | expectedSuccess
        "platform1"  | "ea1"| "appId123" | true
        "platform1"  | "ea1"| null       | false
        "platform1"  | "ea1"| ""         | false
    }

    def "test getWxAppIdByFSPlatformIdAndEa with invalid parameters"() {
        when:
        service.getWxAppIdByFSPlatformIdAndEa(fsPlatformId, ea)

        then:
        thrown(IllegalArgumentException)

        where:
        fsPlatformId | ea
        null         | "ea1"
        ""           | "ea1"
        "platform1"  | null
        "platform1"  | ""
    }

    @Unroll
    def "test getEaByFSPlatformIdAndWxAppId with different scenarios"() {
        given:
        eaWechatAccountBindDao.listEaByPlatformIdAndWxAppId(_, _) >> eaList

        when:
        def result = service.getEaByFSPlatformIdAndWxAppId(fsPlatformId, wxAppId)

        then:
        result.isSuccess() == expectedSuccess
        if (expectedSuccess) {
            result.getData() == eaList[0]
        } else {
            result.getErrorCode() == SHErrorCode.EA_NOT_BIND_TO_MINIAPP
        }

        where:
        fsPlatformId | wxAppId   | eaList      | expectedSuccess
        "platform1"  | "appId1"  | ["ea1"]     | true
        "platform1"  | "appId1"  | []          | false
    }

    def "test getEaByFSPlatformIdAndWxAppId with multiple EAs should throw exception"() {
        given:
        eaWechatAccountBindDao.listEaByPlatformIdAndWxAppId(_, _) >> ["ea1", "ea2"]

        when:
        service.getEaByFSPlatformIdAndWxAppId("platform1", "appId1")

        then:
        thrown(IllegalStateException)
    }

    def "test getEaByFSPlatformIdAndWxAppId with invalid parameters"() {
        when:
        service.getEaByFSPlatformIdAndWxAppId(fsPlatformId, wxAppId)

        then:
        thrown(IllegalArgumentException)

        where:
        fsPlatformId | wxAppId
        null         | "appId1"
        ""           | "appId1"
        "platform1"  | null
        "platform1"  | ""
    }

    @Unroll
    def "test dispatchRequest with different scenarios"() {
        given:
        wechatThirdPlatformConfigDao.getWechatThirdPlatformConfig(_) >> thirdPlatformConfig
        wechatEaBindDao.getEaByAppId(_) >> eaByAppId
        wechatThirdPlatformManager.getThirdPlatformAccessToken(_) >> "accessToken"
        wechatAccountManager.getAccessTokenByWxAppId(_) >> "appAccessToken"
        httpManager.executeGetHttpReturnString(_) >> "GET response"
        httpManager.executePostByPlainBodyHttpReturnString(_, _) >> "POST response"

        when:
        def result = service.dispatchRequest(fsPlatformId, wxAppId, dispatchRequestArg)

        then:
        result.isSuccess() == expectedSuccess
        if (expectedSuccess) {
            assert result.getData().responseBody == expectedResponse
        } else {
            assert result.getErrorCode() == expectedErrorCode
        }

        where:
        fsPlatformId | wxAppId | dispatchRequestArg | thirdPlatformConfig | eaByAppId | expectedSuccess | expectedResponse | expectedErrorCode
        "platform1" | "appId1" | new DispatchRequestArg(requestMethod: "GET", requestUrl: "http://test.com") | new WechatThirdPlatformConfigEntity() | "ea1" | true | "GET response" | null
        "platform1" | "appId1" | new DispatchRequestArg(requestMethod: "POST", requestUrl: "http://test.com", requestBody: "body") | new WechatThirdPlatformConfigEntity() | "ea1" | true | "POST response" | null
        "platform1" | "appId1" | new DispatchRequestArg(requestMethod: "GET", requestUrl: "http://test.com") | null | "ea1" | false | null | SHErrorCode.PARAMS_ERROR
        "platform1" | "appId1" | new DispatchRequestArg(requestMethod: "GET", requestUrl: "http://test.com") | new WechatThirdPlatformConfigEntity() | null | false | null | SHErrorCode.WX_APP_ID_NOT_AUTH_TO_PLATFORM
    }

    def "test dispatchRequest with invalid parameters"() {
        when:
        service.dispatchRequest(fsPlatformId, wxAppId, arg)

        then:
        thrown(IllegalArgumentException)

        where:
        fsPlatformId | wxAppId | arg
        null | "appId1" | new DispatchRequestArg()
        "" | "appId1" | new DispatchRequestArg()
        "platform1" | null | new DispatchRequestArg()
        "platform1" | "" | new DispatchRequestArg()
        "platform1" | "appId1" | null
        "platform1" | "appId1" | new DispatchRequestArg(requestMethod: "INVALID", requestUrl: "url")
        "platform1" | "appId1" | new DispatchRequestArg(requestMethod: "GET", requestUrl: null)
        "platform1" | "appId1" | new DispatchRequestArg(requestMethod: "GET", requestUrl: "")
        "platform1" | "appId1" | new DispatchRequestArg(requestMethod: "POST", requestUrl: "url", requestBody: null)
        "platform1" | "appId1" | new DispatchRequestArg(requestMethod: "POST", requestUrl: "url", requestBody: "")
    }

    def "test getWxMiniAppList with valid input"() {
        given:
        def wxMiniAppEntity = new WxMiniAppEntity(wxAppId: "appId1", ea: "ea1", accessToken: "token1", accessTokenExpireTime: System.currentTimeMillis() + 3600000)
        wechatAccountConfigDao.listWxMiniApp(_, _, _) >> [wxMiniAppEntity]
        wechatAccountConfigDao.getByWxAppId(_) >> new WechatAccountConfigEntity()
        wechatAccountManager.getAccessTokenByWxAppId(_) >> "token1"
        eieaConverter.enterpriseAccountToId(_) >> ["ea1": 1]
        def enterpriseData = new EnterpriseData(enterpriseAccount: "ea1", enterpriseName: "Company1")
        enterpriseEditionService.batchGetEnterpriseData(_) >> new BatchGetEnterpriseDataResult(enterpriseDatas: [enterpriseData])
        miniAppSettingManager.batchGetVisitTotal(_) >> ["appId1": 100L]
        miniappReleaseRecordDao.batchGetEndReason(_) >> ["appId1": ["status": 1, "end_reason": "Success"]]

        when:
        def result = service.getWxMiniAppList("platform1", "1", "keyword")

        then:
        result.isSuccess()
        result.getData().size() == 1
        result.getData()[0].companyName == "Company1"
    }

    def "test getWxMiniAppList with invalid platform id"() {
        when:
        service.getWxMiniAppList(null, "1", "keyword")

        then:
        thrown(IllegalArgumentException)
    }

    def "test getWxMiniAppList with invalid keyword type"() {
        when:
        service.getWxMiniAppList("platform1", "invalid", "keyword")

        then:
        thrown(IllegalArgumentException)
    }

    def "test getWxMiniAppList with empty keyword but valid keywordType"() {
        given:
        wechatAccountConfigDao.listWxMiniApp(_, _, _) >> []
        eieaConverter.enterpriseAccountToId(_) >> [:]
        enterpriseEditionService.batchGetEnterpriseData(_) >> new BatchGetEnterpriseDataResult(enterpriseDatas: [])
        miniAppSettingManager.batchGetVisitTotal(_) >> [:]
        miniappReleaseRecordDao.batchGetEndReason(_) >> [:]

        when:
        def result = service.getWxMiniAppList("platform1", null, null)

        then:
        result.isSuccess()
        result.getData().isEmpty()
    }

    def "test getWxMiniAppList with audit status check"() {
        given:
        def wxMiniAppEntity = new WxMiniAppEntity(wxAppId: "appId1", ea: "ea1", accessToken: "token1", accessTokenExpireTime: System.currentTimeMillis() + 3600000)
        wechatAccountConfigDao.listWxMiniApp(_, _, _) >> [wxMiniAppEntity]
        wechatAccountConfigDao.getByWxAppId(_) >> new WechatAccountConfigEntity()
        wechatAccountManager.getAccessTokenByWxAppId(_) >> "token1"
        eieaConverter.enterpriseAccountToId(_) >> ["ea1": 1]
        def enterpriseData = new EnterpriseData(enterpriseAccount: "ea1", enterpriseName: "Company1")
        enterpriseEditionService.batchGetEnterpriseData(_) >> new BatchGetEnterpriseDataResult(enterpriseDatas: [enterpriseData])
        miniAppSettingManager.batchGetVisitTotal(_) >> ["appId1": 100L]
        miniappReleaseRecordDao.batchGetEndReason(_) >> ["appId1": ["status": MiniappReleaseStatusEnum.AUDITING.getStatus(), "audit_id": "audit123"]]
        def auditStatusResult = new GetAuditStatusResult(status: GetAuditStatusResult.AuditStatus.SUCCESS, reason: "Audit passed")
        codeManageService.getAuditStatus(_, _) >> auditStatusResult
        miniappReleaseRecordDao.auditSuccess(_, _) >> 1

        when:
        def result = service.getWxMiniAppList("platform1", "1", "keyword")

        then:
        result.isSuccess()
        1 * miniappReleaseRecordDao.auditSuccess("appId1", _)
    }

    def "test batchCommitCodeAndSubmitAudit with invalid parameters"() {
        when:
        def result = service.batchCommitCodeAndSubmitAudit(appIds, platformId)

        then:
        result.getErrorCode() == SHErrorCode.PARAMS_ERROR

        where:
        appIds | platformId
        null | "platform1"
        [] | "platform1"
        ["app1"] | null
        ["app1"] | ""
    }

    def "test batchCommitCodeAndSubmitAudit with no latest code template"() {
        given:
        wechatAccountManager.batchGetWechatAccountConfig(_) >> ["app1": new WechatAccountConfigEntity()]
        miniappReleaseRecordDao.batchGetStatus(_) >> [:]
        wxCloudRestManager.getDomainByPlatformId(_) >> new WxThirdPlatformDomainResult()
        wechatThirdPlatformManager.getThirdPlatformAccessToken(_) >> "token"
        codeTemplateService.getTemplateList(_) >> new GetCodeTemplateListResult(templateList: [])

        when:
        def result = service.batchCommitCodeAndSubmitAudit(["app1"], "platform1")

        then:
        !result.isSuccess()
        result.getErrorCode() == SHErrorCode.SYSTEM_ERROR
    }

    def "test batchCommitCodeAndSubmitAudit successful flow"() {
        given:
        def codeTemplate = new GetCodeTemplateListResult.CodeTemplate(templateId: "template1", userVersion: "1.0", userDescription: "Test", createTime: **********)
        def accountConfig = new WechatAccountConfigEntity(wxAppId: "app1", accessToken: "token1", currentCodeVersion: "0.9", nickName: "TestApp", headImg: "img.jpg")
        
        wechatAccountManager.batchGetWechatAccountConfig(_) >> ["app1": accountConfig]
        miniappReleaseRecordDao.batchGetStatus(_) >> [:]
        wxCloudRestManager.getDomainByPlatformId(_) >> new WxThirdPlatformDomainResult()
        wechatThirdPlatformManager.getThirdPlatformAccessToken(_) >> "token"
        codeTemplateService.getTemplateList(_) >> new GetCodeTemplateListResult(templateList: [codeTemplate])
        redisManager.lock(_, _) >> true
        redisManager.unLock(_) >> true
        settingManager.modifyMiniAppDomain(_, _) >> new ModifyDomainResult(errCode: 0)
        settingManager.setWebviewDomain(_, _) >> new SetWebViewDomainResult(errCode: 0)
        eaWechatAccountBindDao.getCustomizedByPlatformIdAndWxAppId(_, _) >> new EaWechatAccountBindEntity(ea: "ea1")
        openAppAdminService.getAppAdminIds(_, _) >> new BaseResult<List<Integer>>(result: [123])
        settingManager.setPrivacySetting(_, _, _) >> null
        codeManageService.commit(_, _) >> new WechatBaseResult(errCode: 0)
        wechatAccountManager.getAccessTokenByWxAppId(_) >> "token1"
        miniappReleaseRecordDao.submitAuditSuccess(_, _, _, _, _) >> 1
        settingManager.submitAuditDelay(_, _, _, _) >> null

        when:
        def result = service.batchCommitCodeAndSubmitAudit(["app1"], "platform1")

        then:
        result.isSuccess()
    }

    def "test batchReleaseCode with invalid parameters"() {
        when:
        def result = service.batchReleaseCode(appIds, platformId)

        then:
        result.getErrorCode() == SHErrorCode.PARAMS_ERROR

        where:
        appIds | platformId
        null | "platform1"
        [] | "platform1"
        ["app1"] | null
        ["app1"] | ""
    }

    def "test batchReleaseCode successful flow"() {
        given:
        wechatAccountManager.batchGetAccessToken(_) >> ["app1": "token1"]
        miniappReleaseRecordDao.batchGetStatus(_) >> ["app1": ["status": MiniappReleaseStatusEnum.AUDIT_SUCCESS.getStatus()]]
        redisManager.lock(_, _) >> true
        redisManager.unLock(_) >> true
        codeManageService.release(_, _) >> new WechatBaseResult(errCode: 0)
        miniappReleaseRecordDao.relaseSuccess(_, _) >> 1
        def releaseRecord = new MiniappReleaseRecordEntity("app1", null, "1.0", "Release", null)
        miniappReleaseRecordDao.getLatestReleaseSuccessRecord(_) >> releaseRecord
        wechatAccountConfigDao.releaseVersion(_, _, _) >> 1

        when:
        def result = service.batchReleaseCode(["app1"], "platform1")

        then:
        result.isSuccess()
    }

    def "test batchReleaseCode with audit not success"() {
        given:
        wechatAccountManager.batchGetAccessToken(_) >> ["app1": "token1"]
        miniappReleaseRecordDao.batchGetStatus(_) >> ["app1": ["status": MiniappReleaseStatusEnum.AUDITING.getStatus()]]
        redisManager.lock(_, _) >> true
        redisManager.unLock(_) >> true

        when:
        def result = service.batchReleaseCode(["app1"], "platform1")

        then:
        result.isSuccess()
        result.getData().contains("app1")
    }

    def "test getWxMiniAppNewVersion with valid platform"() {
        given:
        wechatThirdPlatformManager.getThirdPlatformAccessToken(_) >> "token"
        def codeTemplate = new GetCodeTemplateListResult.CodeTemplate(templateId: "template1", userVersion: "1.0", userDescription: "Test", createTime: **********)
        codeTemplateService.getTemplateList(_) >> new GetCodeTemplateListResult(templateList: [codeTemplate])

        when:
        def result = service.getWxMiniAppNewVersion("platform1")

        then:
        result.isSuccess()
        result.getData().templateId == "template1"
    }

    def "test getWxMiniAppNewVersion with invalid platform"() {
        when:
        service.getWxMiniAppNewVersion(null)

        then:
        thrown(IllegalArgumentException)
    }

    def "test getWxMiniAppNewVersion with invalid access token"() {
        given:
        wechatThirdPlatformManager.getThirdPlatformAccessToken(_) >> null

        when:
        service.getWxMiniAppNewVersion("platform1")

        then:
        thrown(IllegalArgumentException)
    }

    def "test getWxMiniAppNewVersion with no template"() {
        given:
        wechatThirdPlatformManager.getThirdPlatformAccessToken(_) >> "token"
        codeTemplateService.getTemplateList(_) >> new GetCodeTemplateListResult(templateList: [])

        when:
        def result = service.getWxMiniAppNewVersion("platform1")

        then:
        !result.isSuccess()
        result.getErrorCode() == SHErrorCode.SYSTEM_ERROR
    }

    def "test batchUndoCodeAudit with invalid parameters"() {
        when:
        def result = service.batchUndoCodeAudit(appIds, platformId)

        then:
        result.getErrorCode() == SHErrorCode.PARAMS_ERROR

        where:
        appIds | platformId
        null | "platform1"
        [] | "platform1"
        ["app1"] | null
        ["app1"] | ""
    }

    def "test batchUndoCodeAudit successful flow"() {
        given:
        wechatAccountManager.batchGetAccessToken(_) >> ["app1": "token1"]
        miniappReleaseRecordDao.batchGetStatus(_) >> ["app1": ["status": MiniappReleaseStatusEnum.AUDITING.getStatus(), "id": 1]]
        redisManager.lock(_, _) >> true
        redisManager.unLock(_) >> true
        httpManager.executeGetHttpReturnString(_) >> '{"errcode":0}'
        miniappReleaseRecordDao.undoAudit(_, _) >> 1

        when:
        def result = service.batchUndoCodeAudit(["app1"], "platform1")

        then:
        result.isSuccess()
    }

    def "test undoCodeAudit with lock failure"() {
        given:
        redisManager.lock(_, _) >> false

        when:
        def result = service.undoCodeAudit("app1", new StringBuffer(), [:], [:])

        then:
        !result
    }

    def "test undoCodeAudit with null status map"() {
        given:
        redisManager.lock(_, _) >> true
        redisManager.unLock(_) >> true

        when:
        def result = service.undoCodeAudit("app1", new StringBuffer(), [:], [:])

        then:
        !result
    }

    def "test undoCodeAudit with wrong status"() {
        given:
        redisManager.lock(_, _) >> true
        redisManager.unLock(_) >> true

        when:
        def result = service.undoCodeAudit("app1", new StringBuffer(), [:], ["app1": ["status": MiniappReleaseStatusEnum.AUDIT_SUCCESS.getStatus()]])

        then:
        !result
    }

    def "test undoCodeAudit successful"() {
        given:
        redisManager.lock(_, _) >> true
        redisManager.unLock(_) >> true
        httpManager.executeGetHttpReturnString(_) >> '{"errcode":0}'
        miniappReleaseRecordDao.undoAudit(_, _) >> 1

        when:
        def result = service.undoCodeAudit("app1", new StringBuffer(), ["app1": "token"], ["app1": ["status": MiniappReleaseStatusEnum.AUDITING.getStatus(), "id": 1]])

        then:
        result
    }

    def "test undoCodeAudit with API error"() {
        given:
        redisManager.lock(_, _) >> true
        redisManager.unLock(_) >> true
        httpManager.executeGetHttpReturnString(_) >> '{"errcode":1,"errmsg":"error"}'

        when:
        def result = service.undoCodeAudit("app1", new StringBuffer(), ["app1": "token"], ["app1": ["status": MiniappReleaseStatusEnum.AUDITING.getStatus(), "id": 1]])

        then:
        !result
    }

    def "test updateShowVersion with invalid parameters"() {
        when:
        def result = service.updateShowVersion(templateId, version, description, platformId)

        then:
        result.getErrorCode() == SHErrorCode.PARAMS_ERROR

        where:
        templateId | version | description | platformId
        null | "1.0" | "desc" | "platform1"
        "" | "1.0" | "desc" | "platform1"
        "template1" | null | "desc" | "platform1"
        "template1" | "" | "desc" | "platform1"
        "template1" | "1.0" | "desc" | null
        "template1" | "1.0" | "desc" | ""
    }

    def "test updateShowVersion with special template id"() {
        given:
        wechatThirdPlatformConfigDao.updateShowVersion(null, null, null, _) >> 1

        when:
        def result = service.updateShowVersion("--", "1.0", "desc", "platform1")

        then:
        result.isSuccess()
    }

    def "test updateShowVersion successful"() {
        given:
        wechatThirdPlatformConfigDao.updateShowVersion(_, _, _, _) >> 1

        when:
        def result = service.updateShowVersion("template1", "1.0", "desc", "platform1")

        then:
        result.isSuccess()
    }

    def "test updateShowVersion with database failure"() {
        given:
        wechatThirdPlatformConfigDao.updateShowVersion(_, _, _, _) >> 0

        when:
        def result = service.updateShowVersion("template1", "1.0", "desc", "platform1")

        then:
        result.getErrorCode() == SHErrorCode.OPERATE_DB_FAIL
    }

    def "test getVersionList with invalid platform"() {
        when:
        def result = service.getVersionList(null)

        then:
        result.getErrorCode() == SHErrorCode.PARAMS_ERROR
    }

    def "test getVersionList with invalid access token"() {
        given:
        wxThirdCloudInnerService.getComponentAccessTokenByPlatformId(_) >> Result.newError(SHErrorCode.PARAMS_ERROR)

        when:
        def result = service.getVersionList("platform1")

        then:
        result.getErrorCode() == SHErrorCode.PARAMS_ERROR
    }

    def "test getVersionList with template service error"() {
        given:
        wxThirdCloudInnerService.getComponentAccessTokenByPlatformId(_) >> Result.newSuccess("token")
        codeTemplateService.getTemplateList(_) >> new GetCodeTemplateListResult(errCode: 1)

        when:
        def result = service.getVersionList("platform1")

        then:
        result.getErrorCode() == SHErrorCode.SYSTEM_ERROR
    }

    def "test getVersionList successful"() {
        given:
        wxThirdCloudInnerService.getComponentAccessTokenByPlatformId(_) >> Result.newSuccess("token")
        def codeTemplate = new GetCodeTemplateListResult.CodeTemplate(templateId: "template1", userVersion: "1.0", userDescription: "Test", createTime: **********)
        codeTemplateService.getTemplateList(_) >> new GetCodeTemplateListResult(templateList: [codeTemplate], errCode: 0)
        wechatThirdPlatformConfigDao.getWechatThirdPlatformConfig(_) >> new WechatThirdPlatformConfigEntity(showCodeTemplateId: "template1")

        when:
        def result = service.getVersionList("platform1")

        then:
        result.isSuccess()
        result.getData().size() == 2 // One "use latest" + one template
        result.getData().find { it.showCodeTemplateId == "template1" }.checked
    }

    def "test replaceProperties with null or empty request URL"() {
        when:
        def result = service.replaceProperties("platform1", "app1", new WechatThirdPlatformConfigEntity(), requestUrl)

        then:
        result == requestUrl

        where:
        requestUrl << [null, ""]
    }

    def "test replaceProperties with component app id replacement"() {
        given:
        def config = new WechatThirdPlatformConfigEntity(componentAppId: "component123")

        when:
        def result = service.replaceProperties("platform1", "app1", config, "http://test.com?appid=\${wxComponentAppId}")

        then:
        result == "http://test.com?appid=component123"
    }

    def "test replaceProperties with all replacements"() {
        given:
        def config = new WechatThirdPlatformConfigEntity(componentAppId: "component123")
        wechatThirdPlatformManager.getThirdPlatformAccessToken(_) >> "componentToken"
        wechatAccountManager.getAccessTokenByWxAppId(_) >> "appToken"

        when:
        def result = service.replaceProperties("platform1", "app1", config, 
            "http://test.com?appid=\${wxComponentAppId}&token=\${wxComponentAccessToken}&apptoken=\${wxAppAccessToken}")

        then:
        result == "http://test.com?appid=component123&token=componentToken&apptoken=appToken"
    }

    def "test getValidAccessToken with invalid token"() {
        given:
        def entity = new WxMiniAppEntity(accessToken: "token", accessTokenExpireTime: System.currentTimeMillis() - 1000)
        wechatAccountConfigDao.getByWxAppId(_) >> new WechatAccountConfigEntity()
        wechatAccountManager.doGetAndUpdateAccessTokenFromWechat(_) >> "newToken"

        when:
        def result = service.getValidAccessToken(entity)

        then:
        result == "newToken"
    }

    def "test getValidAccessToken with need refresh token"() {
        given:
        def entity = new WxMiniAppEntity(accessToken: "token", accessTokenExpireTime: System.currentTimeMillis() + 1800000) // 30 minutes
        wechatAccountConfigDao.getByWxAppId(_) >> new WechatAccountConfigEntity()

        when:
        def result = service.getValidAccessToken(entity)

        then:
        result == "token"
    }

    def "test getValidAccessToken with valid token"() {
        given:
        def entity = new WxMiniAppEntity(accessToken: "token", accessTokenExpireTime: System.currentTimeMillis() + 3600000) // 1 hour

        when:
        def result = service.getValidAccessToken(entity)

        then:
        result == "token"
    }

    def "test strToList with null or empty string"() {
        when:
        def result = WxThirdPlatformInnerSupportServiceImpl.strToList(input, "|")

        then:
        result.isEmpty()

        where:
        input << [null, ""]
    }

    def "test strToList with valid string"() {
        when:
        def result = WxThirdPlatformInnerSupportServiceImpl.strToList("a|b|c", "|")

        then:
        result == ["a", "b", "c"]
    }

    def "test strToList with whitespace and null values"() {
        when:
        def result = WxThirdPlatformInnerSupportServiceImpl.strToList("a| |c|", "|")

        then:
        result == ["a", "c"]
    }

    def "test batchGetAppIdTokenMap with empty list"() {
        when:
        def result = service.batchGetAppIdTokenMap([])

        then:
        result.isEmpty()
    }

    def "test batchGetAppIdTokenMap with valid entities"() {
        given:
        def entity1 = new WxMiniAppEntity(wxAppId: "app1", accessToken: "token1", accessTokenExpireTime: System.currentTimeMillis() + 3600000)
        def entity2 = new WxMiniAppEntity(wxAppId: "app2", accessToken: "token2", accessTokenExpireTime: System.currentTimeMillis() + 3600000)

        when:
        def result = service.batchGetAppIdTokenMap([entity1, entity2])

        then:
        // Give some time for async operations
        Thread.sleep(100)
        result.size() == 2
        result["app1"] == "token1"
        result["app2"] == "token2"
    }

    def "test integration scenario with complete audit flow"() {
        given:
        def wxMiniAppEntity = new WxMiniAppEntity(wxAppId: "appId1", ea: "ea1", accessToken: "token1", accessTokenExpireTime: System.currentTimeMillis() + 3600000)
        wechatAccountConfigDao.listWxMiniApp(_, _, _) >> [wxMiniAppEntity]
        wechatAccountConfigDao.getByWxAppId(_) >> new WechatAccountConfigEntity()
        wechatAccountManager.getAccessTokenByWxAppId(_) >> "token1"
        eieaConverter.enterpriseAccountToId(_) >> ["ea1": 1]
        def enterpriseData = new EnterpriseData(enterpriseAccount: "ea1", enterpriseName: "Company1")
        enterpriseEditionService.batchGetEnterpriseData(_) >> new BatchGetEnterpriseDataResult(enterpriseDatas: [enterpriseData])
        miniAppSettingManager.batchGetVisitTotal(_) >> ["appId1": 100L]
        miniappReleaseRecordDao.batchGetEndReason(_) >> ["appId1": ["status": MiniappReleaseStatusEnum.AUDITING.getStatus(), "audit_id": "audit123"]]
        
        // Test different audit status scenarios
        def auditStatusResult = new GetAuditStatusResult(status: auditStatus, reason: "Test reason", screenShot: "shot1|shot2")
        codeManageService.getAuditStatus(_, _) >> auditStatusResult
        miniappReleaseRecordDao.auditSuccess(_, _) >> 1
        miniappReleaseRecordDao.auditFail(_, _, _, _) >> 1

        when:
        def result = service.getWxMiniAppList("platform1", "1", "keyword")

        then:
        result.isSuccess()
        if (auditStatus == GetAuditStatusResult.AuditStatus.SUCCESS) {
            1 * miniappReleaseRecordDao.auditSuccess("appId1", _)
        } else if (auditStatus in [GetAuditStatusResult.AuditStatus.REJECTED, GetAuditStatusResult.AuditStatus.CANCEL]) {
            1 * miniappReleaseRecordDao.auditFail("appId1", "Test reason", _, _)
        }

        where:
        auditStatus << [
            GetAuditStatusResult.AuditStatus.SUCCESS,
            GetAuditStatusResult.AuditStatus.REJECTED,
            GetAuditStatusResult.AuditStatus.CANCEL,
            GetAuditStatusResult.AuditStatus.AUDITING
        ]
    }
}
