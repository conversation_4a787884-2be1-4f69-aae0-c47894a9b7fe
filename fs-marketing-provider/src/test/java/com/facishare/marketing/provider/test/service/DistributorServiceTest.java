package com.facishare.marketing.provider.test.service;

import com.facishare.marketing.api.arg.distribution.QueryDistributorResultArg;
import com.facishare.marketing.api.arg.distribution.UpdateOperatorArg;
import com.facishare.marketing.api.result.distribution.DistributorResult;
import com.facishare.marketing.api.result.distribution.QueryDistributorApplyResult;
import com.facishare.marketing.api.result.distribution.QueryDistributorInfoResult;
import com.facishare.marketing.api.service.distribution.DistributorService;
import com.facishare.marketing.api.vo.QueryDistributorApplyVO;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class DistributorServiceTest extends BaseTest {
    @Autowired
    private DistributorService distributorService;

    @Test
    public void queryDistributorResult() {
        String ea = "74164";
        Integer fsUserId = 1071;
        QueryDistributorResultArg vo = new QueryDistributorResultArg();
        vo.setPageNum(0);
        vo.setPageSize(10);
        vo.setEa(ea);
        vo.setFsUserId(fsUserId);
        vo.setIsAppAdmin(true);
        vo.setIsOperator(true);
        vo.setStatus(3);
        vo.setPlanId("5206037dbef24ff4abd97b498d05d43a");
        Result<PageResult<DistributorResult>> result = distributorService.queryDistributorResult(vo);
        Assert.assertEquals(result.getData().getResult().size(), 1);
    }

    @Test
    public void updateOperator() {
        String ea = "55487";
        Integer fsUserId = 1030;
        UpdateOperatorArg.UpdateOperator vo = new UpdateOperatorArg.UpdateOperator();
        vo.setId("300eb2527d90465a8da8e3e523ee6666");
        vo.setOperatorId("6b63da1f548b4d4184ee716b13626a66");
        UpdateOperatorArg arg = new UpdateOperatorArg();
        arg.setUpdateOperators(Lists.newArrayList(vo));
        Result result = distributorService.updateOperator(ea, fsUserId, arg);
        Assert.assertEquals(result.isSuccess(), Boolean.TRUE);
    }

    @Test
    public void queryDistributeInfoByOperator(){
        String operatorId = "c867801ae9b34701b277b54e5c1a69f4";
        String distributorId = "dc73cb42582147638c8fef0d633b59c1";
        Result<QueryDistributorInfoResult> result = distributorService.queryDistributeInfoByOperator(operatorId, distributorId);
        Assert.assertEquals(result.isSuccess(), Boolean.TRUE);
    }

    @Test
    public void queryDistributorApplyInfo(){
        QueryDistributorApplyVO vo = new QueryDistributorApplyVO();
        vo.setDistributorApplicationId("02b47c84d10048058e4d99eba0997396");
        Result<QueryDistributorApplyResult> result = distributorService.queryDistributorApplyInfo(vo);
        Assert.assertEquals(result.isSuccess(), Boolean.TRUE);
    }
}