package com.facishare.marketing.provider.test.manager;


import com.facishare.marketing.api.arg.marketingSpreadSource.MarketingPromotionSourceArg;
import com.facishare.marketing.provider.entity.QrCodeIdentifySpreadSourceRelationEntity;
import com.facishare.marketing.provider.manager.MarketingPromotionSourceArgObjectRelationManager;
import com.facishare.marketing.provider.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class MarketingPromotionSourceArgObjectRelationManagerTest extends BaseTest {

    @Autowired
    private MarketingPromotionSourceArgObjectRelationManager marketingPromotionSourceArgObjectRelationManager;

    @Test
    public void test() {
        MarketingPromotionSourceArg arg = new MarketingPromotionSourceArg();
        arg.setMarketingActivityId("dddddddd");
        marketingPromotionSourceArgObjectRelationManager.createEntity("74164", "qqqqq", arg);
        QrCodeIdentifySpreadSourceRelationEntity entity = marketingPromotionSourceArgObjectRelationManager.getByQrCodeIdentifyId("74164", "qqqqq");
        log.info("结果: {}", entity);
    }

}
