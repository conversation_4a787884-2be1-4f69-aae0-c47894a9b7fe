package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.dao.qywx.QywxContactMeConfigDAO;
import com.facishare.marketing.provider.entity.qywx.QywxContactMeConfigEntity;
import com.facishare.marketing.provider.test.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class QywxContactMeConfigDAOTest extends BaseTest {
    @Autowired
    private QywxContactMeConfigDAO qywxContactMeConfigDAO;

    @Test
    public void insert(){
        QywxContactMeConfigEntity entity = new QywxContactMeConfigEntity();
        entity.setUid("1111");
        entity.setConfigId("2222");

        qywxContactMeConfigDAO.insert(entity);
    }
}
