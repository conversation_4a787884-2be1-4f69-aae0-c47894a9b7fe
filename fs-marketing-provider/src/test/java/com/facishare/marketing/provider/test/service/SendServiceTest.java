/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.marketing.api.arg.sms.*;
import com.facishare.marketing.api.data.sms.SmsPhoneData;
import com.facishare.marketing.api.result.account.AccountIsApplyForKISResult;
import com.facishare.marketing.api.result.sms.GroupSendResult;
import com.facishare.marketing.api.result.sms.QuerySMSSendResult;
import com.facishare.marketing.api.result.sms.SMSSendDetailResult;
import com.facishare.marketing.api.result.sms.mw.PhoneContentResult;
import com.facishare.marketing.api.service.sms.SendService;
import com.facishare.marketing.common.enums.sms.ChannelTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.MwTemplateActionEnum;
import com.facishare.marketing.common.enums.sms.mw.SaveOrSendTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.SmsGroupTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSendDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsTemplateDao;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsTemplateEntity;
import com.facishare.marketing.provider.manager.sms.mw.MwSendManager;
import com.facishare.marketing.provider.manager.sms.mw.MwSmsManager;
import com.facishare.marketing.provider.manager.sms.mw.MwTemplateManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/9/23 16:17
 * @Version 1.0
 */
@Slf4j
public class SendServiceTest extends BaseTest {

    @Autowired
    private SendService sendService;
    @Autowired
    private MwSmsSendDao sendDao;
    @Autowired
    private MwSendManager mwSendManager;
    @Autowired
    private MwTemplateManager mwTemplateManager;
    @Autowired
    private MwSmsTemplateDao templateDAO;
    @Autowired
    private MwSmsTemplateDao mwSmsTemplateDao;

    @Test
    public void getSMSSendDetail() {
        QuerySMSSendDetailArg arg = new QuerySMSSendDetailArg();
        arg.setEa("74164");
        arg.setFsUserId(1177);
        arg.setPageNum(1);
        arg.setPageSize(5);
        arg.setStatus(3);
        arg.setSmsSendId("38b8a1522ac0418eb337763384105ab7");
        Result<PageResult<SMSSendDetailResult>> smsSendDetail = sendService.getSMSSendDetail(arg);
        log.info("sms detail result : {}", smsSendDetail);
    }

    @Test
    public void sendGroupSms() {
        GroupSenderArg arg = new GroupSenderArg();
        arg.setEventType(2);
        Map<String, String> params = new HashMap<>();
        params.put("客户名称", "张三");
        params.put("互动行为", "营销");
        params.put("互动内容", "吃饭");
        params.put("互动时间", "昨晚");
        arg.setPhones(Lists.newArrayList(new PhoneContentResult("15216146853", params)));
        arg.setEa("74164");
        arg.setUserId(-5000);
        arg.setGroupType(5);
        String content = "客户名称：{客户名称} 互动行为：{互动行为} 互动内容：{互动内容} 互动时间：{互动时间}";
        MwSmsTemplateEntity presetTemplatesByContent = mwSmsTemplateDao.getPresetTemplatesByContent(content);
        if (null != presetTemplatesByContent) {
            arg.setTemplateId(presetTemplatesByContent.getId());
        } else {
            arg.setTemplateContent(content);
        }
        arg.setChannelType(3);
        arg.setShowTemplate(false);
        arg.setReal(true);
        arg.setType(1);
        Result<GroupSendResult> groupSendResultResult = sendService.sendGroupSms(arg);
        System.out.println(groupSendResultResult);
    }

    // 测试保存草稿  74164非敏感行业 市场活动：5f9fd40c78e9dc0001db58a4   77741敏感行业 市场活动：5fb369cf98616500017d489b
    @Test
    public void sendGroupSmsUsingSave() {
        GroupSenderArg arg = new GroupSenderArg();
        arg.setEventType(1);
//        List<String> userIds = new ArrayList<>();
//        userIds.add("63d017f905a640dca8af5ea8d347eff4");
//        arg.setUserGroupIds(userIds);
        arg.setEa("77741");
        arg.setUserId(1000);
        arg.setGroupType(5);
//        arg.setTemplateId("35d77ab77a1c43cd98a215a0953b041f");
        arg.setChannelType(ChannelTypeEnum.CONFERENCE_NOTIFICATION.getType());
        arg.setShowTemplate(true);
//        arg.setType(1);
        arg.setTemplateName("11月30日敏感生产类账号");
        arg.setTemplateContent("11月30日敏感生产类账号，发送的内容");
        arg.setMarketingEventId("5fb369cf98616500017d489b");
        arg.setType(3);
        List<PhoneContentResult> phones = Lists.newArrayList(new PhoneContentResult("15627861090"), new PhoneContentResult("13662621353"));
        arg.setPhones(phones);
        Result<GroupSendResult> groupSendResultResult = sendService.sendGroupSms(arg);
        System.out.println(groupSendResultResult.getData());
    }

    @Test
    public void uploadOrModifyTemplate(){
        String ea = "74164";
        String templateId = "faaf6bf235464aebb95a0c5ffcb6db3f";
        int actionType = MwTemplateActionEnum.UPLOAD_TEMPLATE.getType();
        MwSmsTemplateEntity templateEntity = templateDAO.getTemplateById(templateId);
        Result<String>  result = mwTemplateManager.uploadOrModifyTemplate(ea, templateEntity, actionType);
        templateDAO.setTemplateTplid(templateEntity.getId(), result.getData());
        System.out.println("uploadOrModifyTemplate result:" + result);

        templateId = "eaae6b92f5464aebb95a0c5ffcb6dbff";
        actionType = MwTemplateActionEnum.UPLOAD_TEMPLATE.getType();
        templateEntity = templateDAO.getTemplateById(templateId);
        Result<String>  result2 = mwTemplateManager.uploadOrModifyTemplate(ea, templateEntity, actionType);
        templateDAO.setTemplateTplid(templateEntity.getId(), result2.getData());
        System.out.println("uploadOrModifyTemplate result:" + result2);
    }


    // 测试发送草稿
    @Test
    public void sendDraftTask () {
        mwSendManager.queryMwTemplateStatus(); // 模板审核
        SendDraftTaskArg arg = new SendDraftTaskArg();
        arg.setEa("74164");
        arg.setFsUserId(1112);
        arg.setSmsSendId("96a95f8f0f1d4ed0b85c5efdb80d9c98");
        arg.setType(1);
        Result<Void> voidResult = sendService.sendDraftTask(arg);
        System.out.println(voidResult);
    }

    // 拉取审核状态
    @Test
    public void queryMwTemplateStatus () {
        // 模板状态
        mwSendManager.queryMwTemplateStatus();
        // 发送状态
        mwSendManager.getRpt(MwSmsManager.RETSIZE_MAX);

    }

    // 测试获取短信发送历史
    @Test
    public void getSMSSendHistory() {
        String ea = "74164";
        Integer fsUserId = 1071;
        QuerySMSSendArg querySMSSendArg = new QuerySMSSendArg();
        querySMSSendArg.setEa("74164");
        querySMSSendArg.setPageNum(1);
        querySMSSendArg.setPageSize(10);
        querySMSSendArg.setChannelType(ChannelTypeEnum.MARKETING.getType());
        Result<PageResult<QuerySMSSendResult>> result = sendService.getSMSSendHistory(ea, fsUserId, querySMSSendArg);
        System.out.println(result);
    }

    // 验证码
    @Test
    public void sendVerificationCode () {
        SendVerificationCodeArg arg = new SendVerificationCodeArg();
        arg.setEa("88146");
        arg.setPhone("18988781928");
        Result<Void> voidResult = sendService.sendVerificationCode(arg);
        System.out.println(voidResult);
    }

    // 测试服务通
    @Test
    public void test() {
        GroupSenderArg groupSenderArg = new GroupSenderArg();
        groupSenderArg.setGroupType(null);
        groupSenderArg.setEventType(2);
        groupSenderArg.setChannelType(2);
        groupSenderArg.setSmsSendId(null);
        groupSenderArg.setTemplateId(null);
        groupSenderArg.setTemplateContent("77741同事你好77741，早睡早起身体健康！！！");
        groupSenderArg.setTaPath(null);
        groupSenderArg.setEa("77741");
        groupSenderArg.setUserId(-5000);
        groupSenderArg.setType(1);
        groupSenderArg.setTemplateName("服务通测试");
        groupSenderArg.setPhones(Lists.newArrayList(new PhoneContentResult("15627861090"), new PhoneContentResult("13662621353")));
        groupSenderArg.setShowTemplate(false);
        Result<GroupSendResult> groupSendResultResult = sendService.sendGroupSms(groupSenderArg);
        System.out.println(groupSendResultResult);
    }

    @Test
    public void testSendSms(){
        GroupSenderArg arg = new GroupSenderArg();
        arg.setTemplateId("e1b04f51cb7a4eb49460915390ebee33"); // 0e523fbb91dc45efa421b09597b5a701
        arg.setGroupType(SmsGroupTypeEnum.PHONE_LIST.getType());
        arg.setEventType(SaveOrSendTypeEnum.SEND.getType());
        arg.setChannelType(ChannelTypeEnum.GENERAL.getType());
        List<PhoneContentResult> phones = Lists.newArrayList();
        phones.add(new PhoneContentResult("18988781928", Maps.newHashMap()));
        //phones.add(new PhoneContentResult("15768966642", Maps.newHashMap()));
        arg.setPhones(phones);
        arg.setTenantId(74164);
        arg.setUserId(-5000);
        arg.setReal(false);
        arg.setType(1);
        arg.setObjectId("63bcd943e7575500015dc62f"); // 63bcd943e7575500015dc62f
        Result<GroupSendResult> result = sendService.sendGroupSms(arg);
        System.out.println("yes:" + result);
    }

    @Test
    public void testSendSmsWithOutTpl(){
        GroupSenderArg arg = new GroupSenderArg();
        arg.setGroupType(SmsGroupTypeEnum.PHONE_LIST.getType());
        arg.setEventType(SaveOrSendTypeEnum.SEND.getType());
        arg.setChannelType(ChannelTypeEnum.WORK_FLOW.getType());
        List<PhoneContentResult> phones = Lists.newArrayList();
        phones.add(new PhoneContentResult("18988781928", Maps.newHashMap()));
        arg.setPhones(phones);
        arg.setTenantId(74164);
        arg.setUserId(-5000);
        arg.setReal(false);
        arg.setType(1);
        arg.setTemplateContent("感谢您的咨询，已为您安排产品工程师李阳对接，电话18915909012，请注意接听！");
        //arg.setObjectId("63bcd943e7575500015dc62f");
        Result<GroupSendResult> result = sendService.sendGroupSms(arg);
        System.out.println("yes:" + result);
    }

    @Test
    public void updateDetailTemplateIdAndChannelType() {
        String ea = "74164";
        sendDao.updateDetailTemplateIdAndChannelType(ea, "8b9547543b1f46a3b8b60e52bb0d1ae8", null, null);
        sendDao.updateDetailTemplateIdAndChannelType(ea, "b74595bdf8e645f9b0d2c5cb646c6ff6", "1111", null);
        sendDao.updateDetailTemplateIdAndChannelType(ea, "0f824c6c60754a4a93e71a21403bfb72", null, 2);
        sendDao.updateDetailTemplateIdAndChannelType(ea, "8fa6bb7dd7ba46369aa463bcb2b16ba9", "2222", 3);
    }
}
