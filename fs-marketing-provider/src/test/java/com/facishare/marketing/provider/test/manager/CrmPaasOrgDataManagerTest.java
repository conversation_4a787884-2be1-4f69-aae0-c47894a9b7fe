package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.provider.manager.CrmPaasOrgDataManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class CrmPaasOrgDataManagerTest extends BaseTest {
    @Autowired
    private CrmPaasOrgDataManager crmPaasOrgDataManager;

    @Test
    public void queryUserIdsByUserGroups(){
        List<String> groupIds = Lists.newArrayList("5c349872319d19f76d727f6a", "5b6bfe33319d1962cc731262");
        List<Integer> userIds = crmPaasOrgDataManager.queryUserIdsByUserGroups("74164", groupIds);
        log.info("userIds:{}", userIds);
    }
}
