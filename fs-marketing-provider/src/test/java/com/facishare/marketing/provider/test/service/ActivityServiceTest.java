/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.facishare.marketing.api.arg.DeleteActivityArg;
import com.facishare.marketing.api.arg.ListActivitiesArg;
import com.facishare.marketing.api.arg.ListActivityEnrollsArg;
import com.facishare.marketing.api.arg.ListActivityEnrollsCountArg;
import com.facishare.marketing.api.arg.MergeActivityArg;
import com.facishare.marketing.api.arg.UpdateActivityStatusArg;
import com.facishare.marketing.api.arg.WebListActivitiesByMultiConditionArg;
import com.facishare.marketing.api.arg.conference.SignInArg;
import com.facishare.marketing.api.arg.marketingactivity.ActivityListArg;
import com.facishare.marketing.api.arg.marketingactivity.ActivityVO;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.ActivityCountResult;
import com.facishare.marketing.api.result.ActivityEnrollListUnitResult;
import com.facishare.marketing.api.result.ActivityResult;
import com.facishare.marketing.api.result.MergeActivityResult;
import com.facishare.marketing.api.result.WebActivityListUnitResult;
import com.facishare.marketing.api.result.moment.GetDailyPosterInfoResult;
import com.facishare.marketing.api.result.permission.DataPermissionResult;
import com.facishare.marketing.api.service.ActivityService;
import com.facishare.marketing.api.service.MomentPosterService;
import com.facishare.marketing.api.service.wxcallback.QywxSelfBuildAppCallbackService;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.FieldInfo;
import com.facishare.marketing.common.typehandlers.value.FieldInfoList;
import com.facishare.marketing.common.typehandlers.value.FieldValueList;
import com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.test.BaseTest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

//@Ignore
@Slf4j

public class ActivityServiceTest extends BaseTest {
    @Autowired
    private ActivityService activityService;

    @Autowired
    private com.facishare.marketing.api.service.kis.ActivityService  kisActivityService;

    @Autowired
    private MomentPosterService momentPosterService;

    @Autowired
    private QywxSelfBuildAppCallbackService qywxSelfBuildAppCallbackService;

    @Test
    public void getDailyPosterInfo() {
        Result<GetDailyPosterInfoResult>  result = momentPosterService.getDailyPosterInfo("88146",300000002);
        log.info("结果: {}", result);
    }

    @Test
    public void updateActivityStatus() {
        String ea = "2";
        Integer fsUserId = 2795;
        UpdateActivityStatusArg updateActivityStatusArg = new UpdateActivityStatusArg();
        updateActivityStatusArg.setId("a4a0583578de4f4bb2e8760a7703f09a");
        updateActivityStatusArg.setStatus(1);
        Result<Boolean> result = activityService.updateActivityStatus(ea, fsUserId, updateActivityStatusArg);
    }

    @Test
    public void webListActivities() {
        String ea = "55487";
        Integer fsUserId = 1022;
        ListActivitiesArg vo = new ListActivitiesArg();
        vo.setPageNum(1);
        vo.setPageSize(10);
        vo.setState(1);
        vo.setTitle("");
        Result<PageResult<WebActivityListUnitResult>> result = activityService.webListActivities(ea, fsUserId, vo);
    }

    @Test
    public void mergeActivity() {
        MergeActivityArg mergeActivityArg = new MergeActivityArg();
        FieldValueList fieldValues1 = new FieldValueList();
        mergeActivityArg.setActivityDetail(fieldValues1);
        FieldInfoList fieldInfos = new FieldInfoList();
        FieldInfo fieldInfo = new FieldInfo();
        fieldInfo.setType("text");
        fieldInfo.setLabel("姓名");
        fieldInfo.setApiName("name");
        fieldInfo.setHelpText("");
        fieldInfo.setDefineType("custom");
        fieldInfo.setDateFormat("yyyy-MM-dd HH:mm");
        fieldInfo.setIsRequired(true);
        fieldInfos.add(fieldInfo);
        mergeActivityArg.setActivityEnrollDescribe(fieldInfos);
        mergeActivityArg.setBackgroundImageTAPath("");
        mergeActivityArg.setIsNeedSign(1);
        mergeActivityArg.setConsultRedirectEnabled(false);
        mergeActivityArg.setCoverImageTAPath("TA_21635035d47d4876b70923ca204b8353.jpg");
        Date date = new Date();
        mergeActivityArg.setEndTimestamp(Long.valueOf(date.getTime()) + 200000);
        mergeActivityArg.setFormTitle("邀请函");
        mergeActivityArg.setIsNeedSign(2);
        mergeActivityArg.setIsUpdateBackgroundImage(false);
        mergeActivityArg.setIsUpdateCoverImage(false);
        mergeActivityArg.setIsUpdatePublicAccountUrl(false);
        mergeActivityArg.setLocation("大冲国际中心");
        mergeActivityArg.setProductRedirectEnabled(false);
        mergeActivityArg.setProductRedirectUrl("");
        mergeActivityArg.setPublicAccountName("");
        mergeActivityArg.setPublicAccountRedirectEnabled(false);
        mergeActivityArg.setPublicAccountTAPath("");
        mergeActivityArg.setScale(1212);
        mergeActivityArg.setSiteRedirectEnabled(false);
        mergeActivityArg.setSiteRedirectUrl("");
        mergeActivityArg.setStartTimestamp(Long.valueOf(date.getTime()));
        mergeActivityArg.setSubmitSuccessMsg("感谢您参加本次活动，我司将在24小时内与您联系！");
        mergeActivityArg.setTitle("自建活动");
        mergeActivityArg.setWelcomeMsg("欢迎参加此次会议！ 请留下相关信息以便我们更好地联系！");
        mergeActivityArg.setNetworkDiskEnabled(true);
        mergeActivityArg.setNetworkDiskUrl("");
        mergeActivityArg.setJumpLinkEnabled(false);
        mergeActivityArg.setJumpLinkUrl("");
        mergeActivityArg.setButtonText("");
        Result<MergeActivityResult> result = activityService.mergeActivity("55487", 1016, mergeActivityArg);

//        CreateQRCodeArg createQRCodeArg = new CreateQRCodeArg();
//        createQRCodeArg.setType(QRCodeTypeEnum.ACTIVITY.getType());
//        createQRCodeArg.setValue(null);
//        ModelResult<CreatQRCodeForApathAndUrlResult> createQRCodeResult = outQRCodeService.createQRCodeForApathAndUrl(createQRCodeArg);
//        System.out.println("122");
    }

    @Test
    public void getById() {
        Result<ActivityResult> result = activityService.getById("55487", 1016, "aea9c09c534f46b6bc39011b6b7366b9");
    }

    @Test
    public void deleteActivity() {
        DeleteActivityArg deleteActivityArg = new DeleteActivityArg();
        deleteActivityArg.setId("aea9c09c534f46b6bc39011b6b7366b9");
        Result<Boolean> result = activityService.deleteActivity("55487", 1016, deleteActivityArg);
    }

    @Test
    public void listActivityEnrolls() {
        String ea = "2";
        Integer fsUserId = 2795;
        ListActivityEnrollsArg vo = new ListActivityEnrollsArg();
        vo.setActivityId("7a96c6a4dd3f4c07a93643184ec62550");
        vo.setPageNum(0);
        vo.setPageSize(10);
        Result<PageResult<ActivityEnrollListUnitResult>> result = activityService.listActivityEnrolls(ea, fsUserId, vo);
    }

    @Test
    public void applyActivityPeopleCount() {
        String ea = "2";
        Integer fsUserId = 2795;
        ListActivityEnrollsCountArg listActivityEnrollsCountArg = new ListActivityEnrollsCountArg();
        listActivityEnrollsCountArg.setActivityId("7a96c6a4dd3f4c07a93643184ec62550");
        Result<ActivityCountResult> result = activityService.applyActivityPeopleCount(ea, fsUserId, listActivityEnrollsCountArg);
    }

    @Test
    public void webListActivitiesByMultiCondition() {
        String ea = "2";
        Integer fsUserId = 2795;
        WebListActivitiesByMultiConditionArg webListActivitiesByMultiConditionArg = new WebListActivitiesByMultiConditionArg();
        webListActivitiesByMultiConditionArg.setPageNum(0);
        webListActivitiesByMultiConditionArg.setPageSize(10);
        List<Integer> stateList = new ArrayList<>();
        stateList.add(1);
        webListActivitiesByMultiConditionArg.setState(stateList);
        List<Integer> statusList = new ArrayList<>();
        statusList.add(1);
        webListActivitiesByMultiConditionArg.setState(statusList);
        Result<PageResult<WebActivityListUnitResult>> result = activityService.webListActivitiesByMultiCondition(ea, fsUserId, webListActivitiesByMultiConditionArg);
    }

    @Test
    public void count() {
        Result<Integer> result = activityService.getMaterialsWeeklyStatistics(DateUtil.getLastWeekWeekday(), DateUtil.getLastWeekWeekday());
    }

    @Test
    public void signIn() {
        SignInArg signInArg = new SignInArg();
        signInArg.setId("4a982371266040548e0d837013ee036c");
        signInArg.setPhone("13754562000");
        signInArg.setFingerPrint("bb9801f3f1e3c0945842c709bf3c3ed3_10.113.19.2");
        kisActivityService.signIn(signInArg);
    }

    @Test
    public void listActivitiesTest() {

        ActivityListArg arg = new ActivityListArg();

        FilterData filterData = new FilterData();
        filterData.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.addFilter("is_mobile_display", FilterOperatorEnum.EQ.getValue(), Collections.singletonList("0"));
        query.addFilter("life_status", FilterOperatorEnum.EQ.getValue(), Collections.singletonList("normal"));
        //添加数据权限筛选条件

        filterData.setQuery(query);
        arg.setFilterData(filterData);

        arg.setFsEa("88146");
        arg.setFsUserId(1000);
        arg.setPageNum(1);
        arg.setPageSize(3);
        arg.setTitle("数据");
        arg.setMenuId("8cd65867bd4b434f908ab362676efd47");
        Result<PageResult<ActivityVO>> result = kisActivityService.listActivities(arg);
        log.info("结果： {}", JsonUtil.toJson(result));

    }
}