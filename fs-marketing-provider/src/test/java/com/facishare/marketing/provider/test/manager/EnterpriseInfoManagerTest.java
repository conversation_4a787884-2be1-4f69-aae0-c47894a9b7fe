package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.provider.manager.EnterpriseInfoManager;
import com.facishare.marketing.provider.test.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class EnterpriseInfoManagerTest extends BaseTest {

    @Autowired
    private EnterpriseInfoManager enterpriseInfoManager;

    @Test
    public void loadCache(){
        boolean enterpriseStopAndVersionExpired = enterpriseInfoManager.isEnterpriseStopAndVersionExpired("88146");
        System.out.println(enterpriseStopAndVersionExpired);
    }
}
