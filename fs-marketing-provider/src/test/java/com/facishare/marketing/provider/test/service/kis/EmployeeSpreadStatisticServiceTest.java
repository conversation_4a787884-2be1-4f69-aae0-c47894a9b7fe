package com.facishare.marketing.provider.test.service.kis;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.kis.GetClueContributionListArg;
import com.facishare.marketing.api.arg.kis.GetEmployeeMarketingActivityObjectDetailsArg;
import com.facishare.marketing.api.result.kis.GetEmployeeMarketingActivityObjectDetailsResult;
import com.facishare.marketing.api.result.kis.GetLastestEmployeeStatisticUnitResult;
import com.facishare.marketing.api.service.EmployeeSpreadStatisticService;
import com.facishare.marketing.common.enums.AccountTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.dao.kis.MarketingActivityDayStatisticDAO;
import com.facishare.marketing.provider.dto.kis.GetActivityDayStatisticDTO;
import com.facishare.marketing.provider.service.EmployeeSpreadStatisticServiceImpl;
import com.facishare.marketing.provider.test.BaseTest;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.crmrestapi.common.util.SelectOptionsUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * @ClassName EmployeeSpreadStatisticServiceTest
 * @Description
 * <AUTHOR>
 * @Date 2019/3/6 2:29 PM
 */
@Slf4j
public class EmployeeSpreadStatisticServiceTest extends BaseTest {
    @Autowired
    private EmployeeSpreadStatisticService employeeSpreadStatisticService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MarketingActivityDayStatisticDAO marketingActivityDayStatisticDAO;

    @Test
    public void getLastestEmployeeStatisticTest() {
       // employeeSpreadStatisticService.getLastestEmployeeStatistic("fsceshi019", 1038, eieaConverter.enterpriseAccountToId("fsceshi019"), 20, 1, null);
        Result<PageResult<GetLastestEmployeeStatisticUnitResult>> result =
                employeeSpreadStatisticService.getLastestEmployeeStatistic("88146", *********, 88146, 10, 1, null);
        System.out.print("11result:"+ JsonUtil.toJson(result));
    }

    @Test
    public void getClueContributionList() {
        GetClueContributionListArg arg = new GetClueContributionListArg();
        arg.setFsUserId(1038);
        arg.setMarketingActivityId("5c9de8977cfed9f4e8f91bbc");
        arg.setRecentDay(null);
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setEa("fsceshi019");
        employeeSpreadStatisticService.getClueContributionList(arg);
    }

    @Test
    public void getEmployeeMarketingActivityObjectDetails() {
        GetEmployeeMarketingActivityObjectDetailsArg arg = new GetEmployeeMarketingActivityObjectDetailsArg();
        arg.setMarketingActivityId("6791de2240714d000160331e");
       // arg.setRecentDay(30);
//        arg.setEndDate(1673625600000L);
//        arg.setStartDate(1672156800000L);
        Result<GetEmployeeMarketingActivityObjectDetailsResult> result = employeeSpreadStatisticService.getEmployeeMarketingActivityObjectDetails(arg, "88146", 400000014);
        log.info("结果：{}", JsonUtil.toJson(result));
    }

    @Test
    public void getActivityDayStatistic() {

        GetActivityDayStatisticDTO dto = marketingActivityDayStatisticDAO.getActivityDayStatistic("88146", "665ebaba8a75cb000136145a",  new Date(1714891099000L), new Date(),true);
        System.out.println(1);
    }

}
