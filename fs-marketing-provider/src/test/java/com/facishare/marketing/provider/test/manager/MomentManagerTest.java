/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.api.arg.ListQywxMarketingActivityEmployeeRankingArg;
import com.facishare.marketing.api.result.qywx.ListQywxMarketingActivityEmployeeRankingResult;
import com.facishare.marketing.common.enums.qywx.QywxMomentPulishStatusEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.provider.manager.qywx.MomentManager;
import com.facishare.marketing.provider.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class MomentManagerTest extends BaseTest {
    @Autowired
    private MomentManager momentManager;

    @Test
    public void listQywxMomentTaskEmployeeRanking(){
        String ea = "83668";
        ListQywxMarketingActivityEmployeeRankingArg arg = new ListQywxMarketingActivityEmployeeRankingArg();
        arg.setMarketingActivityId("63886707c80cab00017db65d");
        arg.setPageNum(1);
        arg.setPageSize(10);
//        arg.setStatus(0);
        Result<PageResult<ListQywxMarketingActivityEmployeeRankingResult>> result = momentManager.listQywxMomentTaskEmployeeRanking(ea, arg);
        log.info("结果： {}", result);
//        arg.setStatus(1);
//        result = momentManager.listQywxMomentTaskEmployeeRanking(ea, arg);
//        log.info("结果： {}", result);
    }

    @Test
    public void getQywxTagList(){
        List<TagName> tagList = new ArrayList<>();
        TagName tagName1 = new TagName();
        tagName1.setFirstTagName("客户等级");
        tagName1.setSecondTagName("重要");
        TagName tagName2 = new TagName();
        tagName2.setFirstTagName("客户等级");
        tagName2.setSecondTagName("测试");
        tagList.add(tagName1);
        tagList.add(tagName2);
        List<String> qywxTagIdList = momentManager.getQywxTagIdList("74164", tagList);
        System.out.println(qywxTagIdList);
    }

    @Test
    public void sendMomentPublishUserMessage(){
        String ea = "88146";
        String taskId = "c125d2b2bc7347d08ba8c93443e9268d";
        String qywxUserid = "wowx1mDAAAQJ2kIMBVbdNuHIcY_liMmw";
        String externalUserId = "wmwx1mDAAAyIoprZHfHHJNX87SIlRKgQ";
        momentManager.sendMomentPublishUserMessage(ea, taskId, qywxUserid, externalUserId);
    }
}
