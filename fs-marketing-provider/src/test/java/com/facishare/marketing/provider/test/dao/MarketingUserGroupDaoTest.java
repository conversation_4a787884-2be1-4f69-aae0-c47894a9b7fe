package com.facishare.marketing.provider.test.dao;

import com.facishare.marketing.provider.dao.MarketingUserGroupDao;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.open.common.storage.mysql.dao.BaseDao;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.omg.CORBA.PUBLIC_MEMBER;
import org.springframework.beans.factory.annotation.Autowired;

public class MarketingUserGroupDaoTest extends BaseTest {
    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;

    @Test
    public void batchQueryUserNumberByIds(){
        System.out.println("batchQueryUserNumberByIds:"+marketingUserGroupDao.batchQueryUserNumberByIds("88146", Lists.newArrayList("02c11c45bdf54311a05a403e130e706a", "0403e1c2724f417d968002700d27759f")));
    }

    @Test
    public void batchGet(){
        System.out.println("batchGet:"+marketingUserGroupDao.batchGet("88146", Lists.newArrayList("02c11c45bdf54311a05a403e130e706a", "0403e1c2724f417d968002700d27759f")));
    }

    @Test
    public void getTotalUserPhoneNumByMarketingUserGroupIds(){
            System.out.println("getTotalUserPhoneNumByMarketingUserGroupIds:"+marketingUserGroupDao.getTotalUserPhoneNumByMarketingUserGroupIds("88146", Lists.newArrayList("02c11c45bdf54311a05a403e130e706a", "0403e1c2724f417d968002700d27759f")));
    }

    @Test
    public void queryMarketingUserGroupTop5(){
        System.out.println("queryMarketingUserGroupTop5:"+marketingUserGroupDao.queryMarketingUserGroupTop5("88146", Lists.newArrayList("6441037376f9500001ed14f9", "643cbfc276f9500001fac2ec")));
    }
}
