package com.facishare.marketing.provider.test;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.service.CustomizeFormDataService;
import com.facishare.marketing.api.service.MarketingEventStatisticsService;
import com.facishare.marketing.api.service.MarketingUserGroupService;
import com.facishare.marketing.api.service.StatisticService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.service.StatisticServiceImpl;
import com.facishare.marketing.statistic.outapi.constants.NewActionTypeEnum;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

//@Ignore
@Slf4j

public class StatisticServiceTest extends BaseTest {
    @Autowired
    private StatisticService statisticService;

    @Autowired
    private CustomizeFormDataService customizeFormDataService;

    @Autowired
    private MarketingEventStatisticsService marketingEventStatisticsService;
    @Autowired
    private MarketingUserGroupService marketingUserGroupService;
    @Autowired
    private StatisticServiceImpl statisticServiceImpl;
    @Test
    public void testListObjectStatistic(){
        ListObjectStatisticDataArg arg = new ListObjectStatisticDataArg();
        arg.setPageNo(1);
        arg.setPageSize(50);
        statisticService.listObjectStatisticData("74164", 1000, arg);
    }

    @Test
    public void testGetObjectStatisticDetail(){
        GetObjectStatisticDetailArg getObjectStatisticDetailArg = new GetObjectStatisticDetailArg();
        getObjectStatisticDetailArg.setObjectType(6);
        getObjectStatisticDetailArg.setObjectId("bdaa7ebb18754a7db89d395ba506c7a3");
        getObjectStatisticDetailArg.setTrendDataStartDate(1650297600000L);
        getObjectStatisticDetailArg.setTrendDataEndDate(1650988799000L);
        statisticService.getObjectStatisticDetail("74164", 1135, getObjectStatisticDetailArg);
    }

    @Test
    public void testGetAllEmployeeSpreadStatisticData(){
        GetAllEmployeeSpreadStatisticDataArg arg = new GetAllEmployeeSpreadStatisticDataArg();
        arg.setStartDate(1738684800000L);
        arg.setEndDate(1741276799000L);
        arg.setPartner(true);
     //   arg.setObjectId("");
      //  arg.setMarketingEventId("");
//        arg.setObjectId("10e4718876d4412b8f9d7a079ed12619");
        Result r = statisticService.getAllEmployeeSpreadStatisticData("88146", 1000, arg);
        arg.setPartner(false);
        r = statisticService.getAllEmployeeSpreadStatisticData("88146", 1000, arg);
        log.info("结果: {}",r);
    }

    @Test
    public void getEmployeeSpreadStatisticDataWithSubordinate(){

        String ea = "88146";
        GetEmployeeSpreadStatisticDataWithSubordinateArg arg = new GetEmployeeSpreadStatisticDataWithSubordinateArg();
        arg.setStartDate(DateUtil.minusDay(new Date(), 30).getTime());
        arg.setEndDate(System.currentTimeMillis());
        Result<GetEmployeeSpreadStatisticDataWithSubordinateResult> result = statisticService.getEmployeeSpreadStatisticDataWithSubordinate(ea, 400000000, arg);
        System.out.print("getEmployeeSpreadStatisticDataWithSubordinate result:"+ result);
    }

    @Test
    public void getEmployeeSpreadRankWithSubordinate(){
        String ea = "88146";
        Integer fsUserId = 400000000;
//        GetAllEmployeeSpreadEmployeeRankingDataArg arg = new GetAllEmployeeSpreadEmployeeRankingDataArg();
//        arg.setMarketingEventId("5fb39fb83f1535000114345c");
//        List<Integer> employeeIds = Lists.newArrayList();
//        employeeIds.add(1017);
//        employeeIds.add(1020);
//        employeeIds.add(1022);
//        arg.setEmployeeRange(employeeIds);
//        arg.setStartDate(1601481600000L);
//        arg.setEndDate(1606752000000L);
//        arg.setSearchType(1);
//        arg.setPageSize(10);
//        arg.setPageNum(1);

        GetAllEmployeeSpreadEmployeeRankingDataArg arg = new GetAllEmployeeSpreadEmployeeRankingDataArg();
        arg.setPageNum(1);
        arg.setPageSize(20);
        arg.setStartDate(DateUtil.minusDay(new Date(), 30).getTime());
        arg.setEndDate(System.currentTimeMillis());

        Result<com.facishare.marketing.api.result.PageResult<EmployeeRankingDataResult>> result = statisticService.getEmployeeSpreadRankWithSubordinate(ea, fsUserId, arg);
        log.info("结果: {}", JsonUtil.toJson(result));
    }

    @Test
    public void test() {
//        ObjectUserMarketingStatisticsArg arg = new ObjectUserMarketingStatisticsArg();
//        arg.setUserId(1177);
//        arg.setEa("83668");
//        arg.setMarketingActivityId("637387a7c80cab0001b6fa94");
//        Result<ObjectUserMarketingStatisticsResult> result = statisticService.getObjectMarketingActivityStatistics(arg);
//        log.info("结果： result : {}", result);
        ObjectUserMarketingListArg listArg = new ObjectUserMarketingListArg();
        listArg.setPageSize(10);
        listArg.setPageNum(1);
        listArg.setOperateType(1);
        listArg.setMarketingActivityId("641aab4e136a180001f84d6b");
        listArg.setEa("88146");
        listArg.setUserId(100000008);
        listArg.setEmployeeId(100000008);
        Result<com.facishare.marketing.common.result.PageResult<ObjectUserMarketingListResult>> page = statisticService.getObjectUserMarketingList(listArg);
        log.info("结果： result : {}", page);
        listArg.setMarketingActivityId("640adb972668190001b6e988");
        Result<com.facishare.marketing.common.result.PageResult<ObjectUserMarketingListResult>> page1 = statisticService.getObjectUserMarketingList(listArg);
        log.info("结果： result : {}", page1);

    }

    @Test
    public void enterpriseSpreadUserMarketingList() {
        EnterpriseSpreadUserMarketingListArg arg = new EnterpriseSpreadUserMarketingListArg();
        arg.setEa("83668");
        arg.setMarketingActivityId("6391b1049367c00001227d39");
        arg.setPageNum(1);
        arg.setPageSize(10);
        Result<com.facishare.marketing.common.result.PageResult<EnterpriseSpreadUserMarketingListResult>> pageResultResult = statisticService.enterpriseSpreadUserMarketingList(arg);
        log.info("结果 : {}", JSON.toJSONString(pageResultResult));
    }

    @Test
    public void StatisticSpreadRanking() {
        StatisticSpreadRankingArg arg = new StatisticSpreadRankingArg();
        arg.setEa("88146");
        arg.setMarketingActivityId("640aa4aa26681900017a72ce");
        arg.setSpreadFsUid(1004);
//        arg.setPageNum(1);
//        arg.setPageSize(10);
        Result<List<StatisticSpreadRankingResult>> listResult = statisticService.StatisticSpreadRanking(arg);
        List<String> ids  = listResult.getData().stream().map(StatisticSpreadRankingResult::getFromUserMarketingId).collect(Collectors.toList());
        log.info("结果 : {}", JSON.toJSONString(listResult));
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setFromUserMarketingIds(ids);
        arg.setActionType(2000001);
        Result<PageResult<pageUserMarketingActionCountResult>> pageResultResult = statisticService.pageFromUserMarketingActionStatisticSpreadRanking(arg);
        log.info("结果 : {}", JSON.toJSONString(pageResultResult));
        QueryFormUserDataForMarketingActivityIdAndSpreadFsUidArg arg1 = new QueryFormUserDataForMarketingActivityIdAndSpreadFsUidArg();
        arg1.setMarketingActivityId("618cbdc7d12c9900013430b7");
//        arg.setKeyword();
//        arg.setFsUid();
        arg1.setStartDate(1648523823000L);
        arg1.setEndDate(1680059823000L);
        arg1.setPageNum(1);
        arg1.setPageSize(10);
        Result<com.facishare.marketing.common.result.PageResult<QueryFormUserDataResult>> pageResultResult1 = customizeFormDataService.queryFormUserDataForMarketingActivityIdAndSpreadFsUid(arg1);
        log.info("结果：{}", pageResultResult1);
//        MarketingPromotionSourceArg marketingPromo

    }
    @Test
    public void listObjectStatisticTop() {
        Result<List<ObjectStatisticResult>> listResult = statisticService.listObjectStatisticTop("88146", 1000, 30, 5);
        log.info("结果 : {}", JSON.toJSONString(listResult));
    }

    @Test
    public void listMarketingEventStatisticTop() {
        Result<List<MarketingEventStatisticResult>> listResult = marketingEventStatisticsService.listMarketingEventStatisticTop("88146", 1000, 60, 4);
        log.info("结果 : {}", JSON.toJSONString(listResult));
    }

    @Test
    public void listMarketingUserGroupTop5() {
        Result<List<MarketingUserGroupResult>> listResult = marketingUserGroupService.listMarketingUserGroupTop5("88146", 1000);
        log.info("结果 : {}", JSON.toJSONString(listResult));
    }

    @Test
    public void spreadStatisticDetailPage() {
        GetEmployeeSpreadStatisticDetailsDataArg arg = new GetEmployeeSpreadStatisticDetailsDataArg();
        arg.setActionType(NewActionTypeEnum.LOOK_UP.getActionType());
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setStartDate(1644203853000L);
        arg.setEndDate(1686107894945L);
        Result<com.facishare.marketing.common.result.PageResult<ObjectUserMarketingListResult>> detailsData = statisticService.getEmployeeSpreadStatisticDetailsData("zhenju0111", 1019, arg);
        System.out.println(detailsData);
    }

    @Test
    public void spreadStatisticClueDetailPage() {
        GetEmployeeSpreadStatisticDetailsDataArg arg = new GetEmployeeSpreadStatisticDetailsDataArg();
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setStartDate(1688019816579L);
        arg.setEndDate(1688624616579L);
        Result<com.facishare.marketing.common.result.PageResult<QueryFormUserDataResult>> detailsData = statisticService.getEmployeeSpreadClueDetailsData("88146", 1000, arg);
        System.out.println(detailsData);
    }

    @Test
    public void getQywxGroupSendStatisticData() {
        GetQywxStatisticDataArg arg = new GetQywxStatisticDataArg();
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setStartDate(1687881600000L);
        arg.setEndDate(1687967999000L);
        arg.setMarketingEventId("649bcf4ab83c3e00019e4442");
        Result<QywxGroupSendStatisticDataResult> result = statisticService.getQywxGroupSendStatisticData("88146", 1000, arg);
        System.out.println("result-->" + result);
    }

    @Test
    public void getQywxGroupSendEmployeeRankingData() {
        GetQywxStatisticDataArg arg = new GetQywxStatisticDataArg();
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setStartDate(1717344000000L);
        arg.setEndDate(1719935999000L);
        arg.setDepartmentRange(Lists.newArrayList(10011));
        Result result = statisticService.getQywxGroupSendEmployeeRankingData("88146", 1015, arg);
        System.out.println("result-->" + result);
    }

    @Test
    public void getQywxMomentSendStatisticData() {
        GetQywxStatisticDataArg arg = new GetQywxStatisticDataArg();
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setStartDate(1685376000000L);
        arg.setEndDate(1687967999000L);
        Result<QywxGroupSendStatisticDataResult> result = statisticService.getQywxMomentSendStatisticData("88146", 1000, arg);
        System.out.println("result-->" + result);
    }

    @Test
    public void getQywxMomentSendEmployeeRankingData() {
        GetQywxStatisticDataArg arg = new GetQywxStatisticDataArg();
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setStartDate(1685376000000L);
        arg.setEndDate(new Date().getTime());
        arg.setEmployeeRange(Lists.newArrayList("1000"));
        arg.setDepartmentRange(Lists.newArrayList(10003));
        Result result = statisticService.getQywxMomentSendEmployeeRankingData("88146", 1000, arg);
        System.out.println("result-->" + result);
    }

    @Test
    public void officialWebsiteStatistic() {

        statisticServiceImpl.officialWebsiteStatistic("88146", "5a40dfe4f6014a94bcc29e05fd963b75");
        System.out.println(1);
    }

    @Test
    public void getPartnerMarketingActivityStatisticsRankingDate() {
        GetAllEmployeeSpreadEmployeeRankingDataArg arg = new GetAllEmployeeSpreadEmployeeRankingDataArg();
        arg.setPartner(true);
        arg.setStartDate(1735747200000L);
        arg.setEndDate(1736351999000L);
        arg.setTenantGroupIdList(Lists.newArrayList("64bdea54c1913500012cfa85"));
        arg.setPageNum(1);
        arg.setPageSize(10);
        Result<com.facishare.marketing.common.result.PageResult<EmployeeRankingDataResult>>  result = statisticService.getPartnerMarketingActivityStatisticsRankingDate("88146", 1000, arg);
        log.info("结果: {}", result);
    }
}