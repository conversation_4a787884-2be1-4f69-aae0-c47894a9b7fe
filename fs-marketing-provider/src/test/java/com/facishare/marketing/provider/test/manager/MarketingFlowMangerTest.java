package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.provider.dto.MarketingFlowCompositeActivityData;
import com.facishare.marketing.provider.manager.MarketingFlowManager;
import com.facishare.marketing.provider.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class MarketingFlowMangerTest extends BaseTest {
    @Autowired
    private MarketingFlowManager marketingFlowManager;

    @Test
    public void testGetMarketingFlowActivities(){
        List<MarketingFlowCompositeActivityData> dataList = marketingFlowManager.getMarketingFlowCompositeActivites(2, "5dbba9a43db71d3c557aa405");
        System.out.println(dataList);
    }
}
