/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.marketingSpreadSource.MarketingPromotionSourceArg;
import com.facishare.marketing.api.arg.qywx.customizeFormData.CustomizeFormDataEnrollArg;
import com.facishare.marketing.api.service.qywx.CustomizeFormDataService;
import com.facishare.marketing.common.enums.MarketingPromotionDataFromEnum;
import com.facishare.marketing.common.enums.MarketingPromotionSourceLeadModuleEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.provider.dao.EnterpriseMetaConfigDao;
import com.facishare.marketing.provider.manager.EnterpriseInfoManager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class MarketingPromotionSourceObjManagerTest extends BaseTest {

    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;

    @Autowired
    private EnterpriseInfoManager enterpriseInfoManager;

    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;

    @Autowired
    private CustomizeFormDataService customizeFormDataService;

    @Autowired
    private RedisManager redisManager;

    @Test
    public void  test() {
        marketingPromotionSourceObjManager.updateLeadsModuleAndDatFromOptions("88146");
//        marketingPromotionSourceObjManager.createObjDescAndAddObjField("83668");
//        marketingPromotionSourceObjManager.createObjDescAndAddObjField("74164");
       // 82525.1000
//        marketingPromotionSourceObjManager.createObjDescAndAddObjField("76301");
//        marketingPromotionSourceObjManager.createObjDescAndAddObjField("83781");
//
//        List<String> eas = enterpriseMetaConfigDao.findEaAll();
//        eas = eas.stream()
//                .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
//                .collect(Collectors.toList());
//        Map<String, Exception> re = new HashMap<>();
//        for (String ea : eas) {
//            try {
//                if ("74164".equals(ea) || "76301".equals(ea)) {
//                    continue;
//                }
//                marketingPromotionSourceObjManager.createObjDescAndAddObjField(ea);
//            } catch (Exception e) {
//                re.put(ea, e);
//            }
//            log.info("结果 ea:{}", ea);
//        }
//        log.info("结果:1 {}", re);
//        objectDescribe = marketingPromotionSourceObjManager.getOrCreateObjDescribe("74164");
//
//        log.info("结果:2 {}", objectDescribe);
    }

    @Test
    public void  addFieldTest() {
//        MarketingPromotionSourceObjManager a = SpringContextUtil.getBean("marketingPromotionSourceObjManager");
//       marketingPromotionSourceObjManager.addMarketingPromotionSourceFiledToOtherObj("83668");
//       marketingPromotionSourceObjManager.addCTAFieldAndUpdateFieldLabel("88146");
//       marketingPromotionSourceObjManager.addClientAndOtherField("83781");
//        marketingPromotionSourceObjManager.createObjDescAndAddObjField("82558");
       marketingPromotionSourceObjManager.addLandingPageAndOtherField("88146");
//       marketingPromotionSourceObjManager.addCTAFieldAndUpdateFieldLabel("83668");
//        marketingPromotionSourceObjManager.addFiledToOtherObj("83885");
    }

    @Test
    public void  updateWechatFriendRecordLayout() {
        marketingPromotionSourceObjManager.updateWechatFriendRecordLayout("83885");
    }

    @Test
    public void createObjTest() {
        MarketingPromotionSourceArg arg = new MarketingPromotionSourceArg();
        arg.setEa("88146");
        arg.setFsUserId(1000);

        arg.setObjectId("objectId");
        arg.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
//        arg.setMarketingActivityId("63e36cead19ba400015bfc3e");
//        arg.setMarketingEventId("63e200634ede0b0001b19661");
        arg.setSpreadFsUid(1000);
//        arg.setOuterTenantId("300016575");
//        arg.setOuterUid(300383752);
        arg.setChannelValue("wechat");
        arg.setUtmMedium("utm_mediumrrr");
        arg.setUtmSource("utm_source");
        arg.setUtmCampaign("utm_campaign");
        arg.setUtmContent("utm_content");
        arg.setUtmTerm("365勤策");
        arg.setSpreadQywxUserId("wowx1mDAAAiRVtHMRHh5SRe7bLHqlECg");
//        arg.setSpreadMemberId("63d9dee0d19ba40001341544");
//        arg.setUserMarketingId("usermarketing");
//        arg.setSourceUrl("www.baidu.com");
        arg.setLandingPageName("纷享销客");
//        arg.setConvertPageUrl("www.fixoake.com?page=12");
//        arg.setConvertPageName("纷享销客试用2");
        arg.setClient(1);
        arg.setOperateSystem("window");
        arg.setBrowser("google chrome");
        arg.setIpAddr("127.0.0.1");
        arg.setSourceUrlType("百度");
//        arg.setChannelValue("other:6666");
//        arg.setMarketingActivityId("656db718951ca700013c5772");

//        arg.setDataFrom(MarketingPromotionDataFromEnum.YXT.getDataSource());
//        arg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.FORM.getLeadModule());
//        arg.setLandingPageUrl("http://aisite.wejianzhan.com");
//        marketingPromotionSourceObjManager.tryGetOrCreateObj(arg);

        arg.setDataFrom(MarketingPromotionDataFromEnum.QYWX.getDataSource());
        arg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.TEL.getLeadModule());
        arg.setLandingPageUrl("http://www.chengzijianzhan.com");
        arg.setUnitId("***********");
        arg.setAccountId("********");
        arg.setKeywordId("***********");
        marketingPromotionSourceObjManager.tryGetOrCreateObj(arg);

//
//        arg.setDataFrom(MarketingPromotionDataFromEnum.FX_ONLINE_SERVICE.getDataSource());
//        arg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.LOTTERY.getLeadModule());
//        arg.setLandingPageUrl("http://www.qq.com");
//        marketingPromotionSourceObjManager.tryGetOrCreateObj(arg);

//        arg.setDataFrom(MarketingPromotionDataFromEnum.BAIDU_AD.getDataSource());
//        arg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.COUPON.getLeadModule());
//        arg.setLandingPageUrl("http://qq.com");
//        marketingPromotionSourceObjManager.tryGetOrCreateObj(arg);
//
//        arg.setDataFrom(MarketingPromotionDataFromEnum.HEADLINE_AD.getDataSource());
//        arg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.CALLBACK.getLeadModule());
//        arg.setLandingPageUrl("http://crm.ceshi112.com");
//        marketingPromotionSourceObjManager.tryGetOrCreateObj(arg);
//
//        arg.setDataFrom(MarketingPromotionDataFromEnum.TENCENT_AD.getDataSource());
//        arg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.WECHAT.getLeadModule());
//        arg.setLandingPageUrl("http://crm.ceshi112.com");
//        marketingPromotionSourceObjManager.tryGetOrCreateObj(arg);
//
//        arg.setDataFrom(MarketingPromotionDataFromEnum.OTHER.getDataSource());
//        arg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.OTHER.getLeadModule());
//        arg.setLandingPageUrl("http://www.baidu.com");
//        marketingPromotionSourceObjManager.tryGetOrCreateObj(arg);


    }

    @Test
    public void updateUserMarketingIdByMergeEventTest() {
        marketingPromotionSourceObjManager.updateUserMarketingIdByMergeEvent("74164", "usermarketing3", "usermarketing4");
    }

    @Test
    public void isEnterpriseStopAndVersionExpired() {
        String ea = "8814633";
        redisManager.setEnterpriseStatus(ea, "1");
        boolean enterpriseStopAndVersionExpired = enterpriseInfoManager.isEnterpriseStopAndVersionExpired(ea);
        log.info("结果： {}", enterpriseStopAndVersionExpired);
    }

    @Test
    public void customizeFormDataEnrollTest() {
        String json = "{\n" +
                "  \"formId\": \"ffa9e325eaaf4e758b90b8470359d234\",\n" +
                "  \"objectType\": 27,\n" +
                "  \"objectId\": \"adee6f07f49349d09cd76b15a6c1d784\",\n" +
                "  \"formUsage\": 1,\n" +
                "  \"marketingEventId\": \"6566edfedc62af00011f9c5b\",\n" +
                "  \"needSignIn\": false,\n" +
                "  \"qrCodeId\": \"4889e549c98349cbbc4c1548cc69650e\",\n" +
                "  \"submitContent\": {\n" +
                "    \"name\": \"二维码测试2\",\n" +
                "    \"phone\": \"18971083112\"\n" +
                "  },\n" +
                "  \"landingPageUrl\": \"https://crm.ceshi112.com/proj/page/marketing-page?marketingEventId=6566edfedc62af00011f9c5b&ea=88146&id=b377a24a0989418bad417b4735c7f769&type=1&qrCodeId=4889e549c98349cbbc4c1548cc69650e&userMarketingId=f29280999061481fa41e96275a4943a4\",\n" +
                "  \"landingPageName\": \"首页\",\n" +
                "  \"convertPageUrl\": \"https://crm.ceshi112.com/proj/page/marketing-page?marketingEventId=6566edfedc62af00011f9c5b&ea=88146&id=b377a24a0989418bad417b4735c7f769&type=1&qrCodeId=4889e549c98349cbbc4c1548cc69650e&userMarketingId=f29280999061481fa41e96275a4943a4\",\n" +
                "  \"fromUserMarketingId\": \"f29280999061481fa41e96275a4943a4\",\n" +
                "  \"client\": 1,\n" +
                "  \"browser\": \"WeChat 8.0.44\",\n" +
                "  \"operateSystem\": \"iOS 17.1.2\",\n" +
                "  \"landingUrl\": \"https://crm.ceshi112.com/proj/page/marketing-page?marketingEventId=6566edfedc62af00011f9c5b&ea=88146&id=b377a24a0989418bad417b4735c7f769&type=1&qrCodeId=4889e549c98349cbbc4c1548cc69650e&userMarketingId=f29280999061481fa41e96275a4943a4\",\n" +
                "  \"userAgent\": \"Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.44(0x18002c27) NetType/WIFI Language/zh_CN\",\n" +
                "  \"referer\": \"https://crm.ceshi112.com/proj/page/marketing-page?marketingEventId=6566edfedc62af00011f9c5b&ea=88146&id=b377a24a0989418bad417b4735c7f769&type=1&qrCodeId=4889e549c98349cbbc4c1548cc69650e&userMarketingId=f29280999061481fa41e96275a4943a4\",\n" +
                "  \"timestamp\": 1701930312051,\n" +
                "  \"nonce\": \"1e422a6c50a729d5\",\n" +
                "  \"sign\": \"42799b18a9f95cb6444dc71c740a20e6\"\n" +
                "}";
        CustomizeFormDataEnrollArg customizeFormDataEnrollArg = JSONObject.parseObject(json, CustomizeFormDataEnrollArg.class);
        customizeFormDataEnrollArg.setPartner(false);
        customizeFormDataService.customizeFormDataEnroll(customizeFormDataEnrollArg);
    }

    @Test
    public void addSpreadTypeOptionsTest() {
        marketingActivityRemoteManager.addSpreadTypeOptions("88146");
    }

    @Test
    public void addWhatsAppSpreadTypeOptionsTest() {
        marketingActivityRemoteManager.addWhatsAppSpreadTypeOptions("82846");
    }

    @Test
    public void addMemberMarketingSpreadTypeOptionsTest() {
        marketingActivityRemoteManager.addMemberMarketingSpreadTypeOptions("88146");
    }
}
