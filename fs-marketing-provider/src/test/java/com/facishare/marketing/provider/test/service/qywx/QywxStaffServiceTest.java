package com.facishare.marketing.provider.test.service.qywx;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.qywx.department.QueryQywxDepartmentArg;
import com.facishare.marketing.api.arg.qywx.staff.QueryQywxStaffPageArg;
import com.facishare.marketing.api.arg.qywx.staff.QueryQywxStaffaArg;
import com.facishare.marketing.api.arg.qywx.staff.QueryUserCardDetailArg;
import com.facishare.marketing.api.arg.qywx.staff.UserCardStatisticArg;
import com.facishare.marketing.api.result.qywx.department.QueryQywxDepartmentResult;
import com.facishare.marketing.api.result.qywx.staff.QueryQywxStaffPageResult;
import com.facishare.marketing.api.service.qywx.QyWxDepartmentService;
import com.facishare.marketing.api.service.qywx.QywxStaffService;
import com.facishare.marketing.common.enums.qywx.UserCardOpenStatusEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created  By zhoux 2020/04/10
 **/
@Slf4j
public class QywxStaffServiceTest extends BaseTest {

    @Autowired
    private QywxStaffService qywxStaffService;

    @Autowired
    private QyWxDepartmentService qyWxDepartmentService;
    @Autowired
    private DataPermissionManager dataPermissionManager;


    @Test
    public void queryQywxStaff() {
        QueryQywxStaffaArg arg = new QueryQywxStaffaArg();
        arg.setFsEa("88146");
        arg.setWarmUpData(true);
        qywxStaffService.queryQywxStaff(arg);
    }

    @Test
    public void userCardStatistic() {
        UserCardStatisticArg arg = new UserCardStatisticArg();
        arg.setFsEa("74164");
        qywxStaffService.userCardStatistic(arg);
    }

    @Test
    public void queryUserCardDetail() {
        QueryUserCardDetailArg arg = new QueryUserCardDetailArg();
        arg.setFsEa("74860");
        arg.setIsOpen(UserCardOpenStatusEnum.ALL.getStatus());
        arg.setEmployeeName("iiiiiiiiiiiii");
        arg.setPageNum(1);
        arg.setPageSize(3);
        qywxStaffService.queryUserCardDetail(arg);
    }

    @Test
    public void queryQywxDepartment() {
        QueryQywxDepartmentArg arg = new QueryQywxDepartmentArg();
        arg.setFsEa("88146");
        Result<List<QueryQywxDepartmentResult>> listResult = qyWxDepartmentService.queryQywxDepartment(arg);
        List<QueryQywxDepartmentResult> results = dataPermissionManager.queryAccessibleOrgStructure(listResult.getData(), Sets.newHashSet(10008, 10005,10003));
        log.info("结果: {}", JSON.toJSONString(results));
    }

    @Test
    public void initQywxActivatedAccount() {
        List<String> eaList = Lists.newArrayList("88146");
        qywxStaffService.initQywxActivatedAccount(null);

    }


    @Test
    public void queryQywxStaffPage() {
        QueryQywxStaffPageArg queryQywxStaffPageArg = new QueryQywxStaffPageArg();
        queryQywxStaffPageArg.setFsEa("88146");
        queryQywxStaffPageArg.setFsUserId(1000);
        queryQywxStaffPageArg.setPageNum(1);
        queryQywxStaffPageArg.setPageSize(10);
        queryQywxStaffPageArg.setActivatedStatus(0);
        queryQywxStaffPageArg.setOpenCardStatus(0);
        queryQywxStaffPageArg.setBindCrmStatus(0);
        Result<PageResult<QueryQywxStaffPageResult>> resultResult =  qywxStaffService.queryQywxStaffPage(queryQywxStaffPageArg);
        qywxStaffService.exportQywxStaff(queryQywxStaffPageArg);
        log.info("结果: {}", JSON.toJSONString(resultResult));

    }

}
