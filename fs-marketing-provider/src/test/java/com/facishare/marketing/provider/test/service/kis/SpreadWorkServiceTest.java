package com.facishare.marketing.provider.test.service.kis;

import com.facishare.marketing.api.result.kis.CreateWXQRCodeByFeedResult;
import com.facishare.marketing.api.result.kis.GetCardPosterInfoResult;
import com.facishare.marketing.api.service.kis.SpreadWorkService;
import com.facishare.marketing.common.enums.AccountTypeEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/3/18 11:31 AM
 */
@Slf4j
public class SpreadWorkServiceTest extends BaseTest {
    @Autowired
    private SpreadWorkService spreadWorkService;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Test
    public void addFeedAndMarketingActivityTest() {

        spreadWorkService.addFeedAndMarketingActivity("88146", 1000, null, ObjectTypeEnum.OUT_LINK, "23069c7796dd4a97a7f0d81aaabe053e", null, true, false,false);
    }

    @Test
    public void getMaterialPosterTest() {
        spreadWorkService.getMaterialPoster("e7fezb92b88b7a3a077b2aa", ObjectTypeEnum.ACTIVITY, "9f6e2cc31d93406b8f50559c78419712", "74164", 1071, null, null);
    }

    @Test
    public void getCardQrCodeWithAvatar() {
        spreadWorkService.getCardQrCodeWithAvatar(null,null, "1eb5b6bdaac649a9a7838bdf9d05011a");
    }

    @Test
    public void createWxQrCodeForPartner(){
        Result<CreateWXQRCodeByFeedResult> result = spreadWorkService.createWXQRCodeByFeedForPartner("88146", "993439456", "300097773", ObjectTypeEnum.getByType(26), "93d6d50cf30e4b40aa2d83b15ec51991", "bb4alddfn4o56e2", "64dc404de1eac00001a10f7c", true, "{\"marketingActivityId\":\"64dc404de1eac00001a10f7c\",\"marketingEventId\":\"64d598270b983400017eb9f9\",\"hostType\":\"fs\",\"byShare\":\"1\",\"ea\":\"88146\",\"spreadChannel\":\"\",\"objectId\":\"93d6d50cf30e4b40aa2d83b15ec51991\",\"spreadFsEa\":\"88146\",\"objectType\":\"26\",\"fsUserId\":300097773,\"spreadFsUid\":300097773,\"ispartner\":true,\"outerTenantId\":\"\",\"outerUid\":\"\",\"upstreamEa\":\"\"}");
        System.out.println(result);
    }
    @Test
    public void getUrlByPath() {
        String ea=null;
        String url = fileV2Manager.getUrlByPath("TA_d7d40a04b3ee4d6eaa2bdf749a659f9c.jpg", ea, false);
        System.out.println(url);

    }

    @Test
    public void getCardPosterInfo() {
        Result<GetCardPosterInfoResult> result = spreadWorkService.getCardPosterInfo("88146", null, null);
        log.info("结果: {}", result);
    }

}
