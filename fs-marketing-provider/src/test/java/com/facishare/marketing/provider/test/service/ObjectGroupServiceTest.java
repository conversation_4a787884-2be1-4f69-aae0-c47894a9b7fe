/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.beust.jcommander.internal.Lists;
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.GetObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SaveObjectGroupArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.objectGroup.GetObjectGroupResult;
import com.facishare.marketing.api.service.ObjectGroupService;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.UserMarketingActionClientEnum;
import com.facishare.marketing.common.enums.objectgroup.ObjectGroupVisibleTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.rest.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class ObjectGroupServiceTest extends BaseTest {

    private final static String EA = "88146";
    private final static Integer USER_ID = 1000;

    @Autowired
    private ObjectGroupService objectGroupService;

    @Test
    public void testListGroup() {
        ListGroupArg arg = new ListGroupArg();
        arg.setObjectType(ObjectTypeEnum.ARTICLE.getType());
        arg.setUseType(0);
        arg.setMenuId("029d079e8c5f4796ac7fb8e38a402868");
        // 2138
        Result<ObjectGroupListResult> result = objectGroupService.listGroup("88146", 400000001, arg);
        log.info("结果1: {}", JsonUtil.toJson(result));
    }

    @Test
    public void testGetGroup(){
        GetObjectGroupArg arg = new GetObjectGroupArg();
        arg.setId("924173973a5246c38c6812a2e41fea91");
        arg.setEa("88146");
        arg.setFsUserId(1000);
        Result<GetObjectGroupResult> result = objectGroupService.getGroup(arg);
        System.out.println("yes:" + result);
    }

    @Test
    public void testSaveGroup(){
        SaveObjectGroupArg arg = new SaveObjectGroupArg();
        arg.setEa(EA);
        arg.setFsUserId(USER_ID);
        arg.setName("测试二级分组");
        arg.setObjectType(ObjectTypeEnum.ARTICLE.getType());
        arg.setParentId("53f73bcb219343b2bec8ce2c4affee16");
        arg.setSeqNo(1);
        SaveObjectGroupVisibleArg visibleArg = new SaveObjectGroupVisibleArg();
        visibleArg.setDepartmentIds(Lists.newArrayList(1011,1004));
        visibleArg.setInnerVisible(ObjectGroupVisibleTypeEnum.PART.getValue());
        arg.setVisible(visibleArg);
        Result<EditObjectGroupResult> result = objectGroupService.saveGroup(arg);
        System.out.println("yes:" + result);
    }
}
