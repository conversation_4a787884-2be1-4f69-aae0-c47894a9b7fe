package com.facishare.marketing.provider.test.mq;

import com.facishare.marketing.api.result.UserMarketingAccountMergeSenderModel;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.mq.sender.UserMarketingAccountMergeSender;
import com.facishare.marketing.provider.test.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class UserMarketingAccountMergeSenderTest extends BaseTest {

    @Autowired
    private UserMarketingAccountMergeSender userMarketingAccountMergeSender;

    @Test
    public void send() throws InterruptedException {
        ThreadPoolUtils.executeWithNewThread(() -> {
            for (int i = 0; i < 20; i++) {
                UserMarketingAccountMergeSenderModel.UserMarketingAccountMergeSenderVO associationChangeSenderVO = new UserMarketingAccountMergeSenderModel.UserMarketingAccountMergeSenderVO();
                associationChangeSenderVO.setEa("88146");
                associationChangeSenderVO.setNewUserMarketingId("this_is_new_" + i);
                associationChangeSenderVO.setOldUserMarketingId("this_is_old_" + i);
                associationChangeSenderVO.setActionTime(System.currentTimeMillis());
                userMarketingAccountMergeSender.send(associationChangeSenderVO);
            }
        });
        ThreadPoolUtils.executeWithNewThread(() -> {
            for (int i = 0; i < 20; i++) {
                UserMarketingAccountMergeSenderModel.UserMarketingAccountMergeSenderVO associationChangeSenderVO = new UserMarketingAccountMergeSenderModel.UserMarketingAccountMergeSenderVO();
                associationChangeSenderVO.setEa("74164");
                associationChangeSenderVO.setNewUserMarketingId("this_is_new_" + i);
                associationChangeSenderVO.setOldUserMarketingId("this_is_old_" + i);
                associationChangeSenderVO.setActionTime(System.currentTimeMillis());
                userMarketingAccountMergeSender.send(associationChangeSenderVO);
            }
        });
        //Thread.sleep(2000L);

    }

}
