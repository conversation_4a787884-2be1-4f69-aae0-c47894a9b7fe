0``METHOD``com.facishare.marketing.provider.manager.SettingManager.queryEnterpriseInfo
1``METHOD``com.facishare.marketing.provider.manager.PhotoManager.queryPhoto
2``METHOD``com.github.zhxing.ghosttest.plugin.method.AssertResultTestCase.assertResult
***0``METHOD``com.facishare.marketing.provider.manager.SettingManager.queryEnterpriseInfo***
{"@type":"java.util.HashMap",
	"request":{
		"@type":"com.github.zhxing.ghosttest.plugin.method.MethodRequest",
		"args":[
			"2",
			1123
		],
		"className":"com.facishare.marketing.provider.manager.SettingManager",
		"methodName":"queryEnterpriseInfo",
		"paramClassNames":[
			"java.lang.String",
			"java.lang.Integer"
		],
		"returnClassName":"com.facishare.marketing.common.result.Result"
	},
	"response":{
		"@type":"com.facishare.marketing.common.result.Result",
		"data":{
			"@type":"com.facishare.marketing.api.result.EnterpriseInfoResult",
			"createTime":1548215556802,
			"ea":"2",
			"fullName":"蜂小蜜",
			"iconUrl":"https://www.ceshi113.com/FSC/N/FileShare/ShowImage?fileId=E856D8C119D3427599F7277A5403498EC3CB551A98FA1DC354950A5D115D55F6D1EAE502CEE8BD714B38C4C89EDC5EF06BFC0BA674DA4362C75A4EAB1DDB48E8EB4A28A3EE763CFA",
			"id":"cbf66f7ecb5f488ea351c317acec5c71",
			"shortName":"纷享易动",
			"thumbnailUrl":"https://www.ceshi113.com/FSC/N/FileShare/ShowImage?fileId=E856D8C119D3427599F7277A5403498EC3CB551A98FA1DC354950A5D115D55F6D1EAE502CEE8BD714B38C4C89EDC5EF06BFC0BA674DA4362C75A4EAB1DDB48E8EB4A28A3EE763CFA",
			"updateTime":1562747645010,
			"userId":1000
		},
		"errCode":0,
		"errMsg":"成功"
	}
}
***1``METHOD``com.facishare.marketing.provider.manager.PhotoManager.queryPhoto***
{"@type":"java.util.HashMap",
	"request":{
		"@type":"com.github.zhxing.ghosttest.plugin.method.MethodRequest",
		"args":[
			2,
			"72319f69562c4878a69c2bd386f0209f"
		],
		"className":"com.facishare.marketing.provider.manager.PhotoManager",
		"methodName":"queryPhoto",
		"paramClassNames":[
			"int",
			"java.lang.String"
		],
		"returnClassName":"java.util.List"
	},
	"response":[
		{
			"@type":"com.facishare.marketing.provider.entity.PhotoEntity",
			"createTime":1568881262562,
			"id":"c7ef3430ea0e4f3f9809647a2ce4d010",
			"path":"A_201909_19_27e526b8475e4ba4984eda7aef64ff71.png",
			"seqNum":0,
			"targetId":"72319f69562c4878a69c2bd386f0209f",
			"targetType":2,
			"thumbnailPath":"A_201909_19_27e526b8475e4ba4984eda7aef64ff71.png",
			"thumbnailUrl":"https://www.ceshi113.com/FSC/N/FileShare/ShowImage?fileId=E856D8C119D3427599F7277A5403498EC3CB551A98FA1DC3B37A53EDE2D5E1AC20481465B3E033E8C94B73149539F308B3340A20418F6C793335D6D3B3F19EA5292219DEA1AC53BB",
			"updateTime":1568881262562,
			"url":"https://www.ceshi113.com/FSC/N/FileShare/ShowImage?fileId=E856D8C119D3427599F7277A5403498EC3CB551A98FA1DC3B37A53EDE2D5E1AC20481465B3E033E8C94B73149539F308B3340A20418F6C793335D6D3B3F19EA5292219DEA1AC53BB"
		}
	]
}
***2``METHOD``com.github.zhxing.ghosttest.plugin.method.AssertResultTestCase.assertResult***
{"@type":"java.util.HashMap",
	"request":{
		"@type":"com.github.zhxing.ghosttest.plugin.method.MethodRequest",
		"args":[
			{
				"@type":"com.facishare.marketing.common.result.Result",
				"data":{
					"@type":"com.facishare.marketing.common.result.PageResult",
					"pageNum":1,
					"pageSize":1,
					"result":[
						{
							"@type":"com.facishare.marketing.api.result.EnterpriseFeedResult",
							"activeCount":0,
							"commentCount":0,
							"companyAvatar":"https://www.ceshi113.com/FSC/N/FileShare/ShowImage?fileId=E856D8C119D3427599F7277A5403498EC3CB551A98FA1DC354950A5D115D55F6D1EAE502CEE8BD714B38C4C89EDC5EF06BFC0BA674DA4362C75A4EAB1DDB48E8EB4A28A3EE763CFA",
							"companyName":"纷享易动",
							"createTime":1568886981610,
							"createTimeStamp":1568886981610,
							"createTimeStr":"7天前",
							"ea":"2",
							"enterpriseFeedVO":{
								"discountPrice":"",
								"endTimestamp":0,
								"name":"123",
								"photoUrl":"https://www.ceshi113.com/FSC/N/FileShare/ShowImage?fileId=E856D8C119D3427599F7277A5403498EC3CB551A98FA1DC3B37A53EDE2D5E1AC20481465B3E033E8C94B73149539F308B3340A20418F6C793335D6D3B3F19EA5292219DEA1AC53BB",
								"price":"",
								"startTimestamp":0,
								"summary":"1232"
							},
							"forwardCount":0,
							"id":"ee027dac7e6b44019748120a7d445b96",
							"lookUpCount":0,
							"objectId":"72319f69562c4878a69c2bd386f0209f",
							"objectType":4,
							"recommendation":"14",
							"userId":3013
						}
					],
					"time":0,
					"totalCount":25
				},
				"errCode":0,
				"errMsg":"成功"
			}
		],
		"className":"com.github.zhxing.ghosttest.plugin.method.AssertResultTestCase",
		"methodName":"assertResult",
		"paramClassNames":[
			"java.lang.Object"
		],
		"returnClassName":"void"
	},
	"response":{
		"@type":"com.facishare.marketing.common.result.Result",
		"data":{
			"@type":"com.facishare.marketing.common.result.PageResult",
			"pageNum":1,
			"pageSize":1,
			"result":[
				{
					"@type":"com.facishare.marketing.api.result.EnterpriseFeedResult",
					"activeCount":0,
					"commentCount":0,
					"companyAvatar":"https://www.ceshi113.com/FSC/N/FileShare/ShowImage?fileId=E856D8C119D3427599F7277A5403498EC3CB551A98FA1DC354950A5D115D55F6D1EAE502CEE8BD714B38C4C89EDC5EF06BFC0BA674DA4362C75A4EAB1DDB48E8EB4A28A3EE763CFA",
					"companyName":"纷享易动",
					"createTime":1568886981610,
					"createTimeStamp":1568886981610,
					"createTimeStr":"7天前",
					"ea":"2",
					"enterpriseFeedVO":{
						"discountPrice":"",
						"endTimestamp":0,
						"name":"123",
						"photoUrl":"https://www.ceshi113.com/FSC/N/FileShare/ShowImage?fileId=E856D8C119D3427599F7277A5403498EC3CB551A98FA1DC3B37A53EDE2D5E1AC20481465B3E033E8C94B73149539F308B3340A20418F6C793335D6D3B3F19EA5292219DEA1AC53BB",
						"price":"",
						"startTimestamp":0,
						"summary":"1232"
					},
					"forwardCount":0,
					"id":"ee027dac7e6b44019748120a7d445b96",
					"lookUpCount":0,
					"objectId":"72319f69562c4878a69c2bd386f0209f",
					"objectType":4,
					"recommendation":"14",
					"userId":3013
				}
			],
			"time":0,
			"totalCount":25
		},
		"errCode":0,
		"errMsg":"成功"
	}
}
***END***
