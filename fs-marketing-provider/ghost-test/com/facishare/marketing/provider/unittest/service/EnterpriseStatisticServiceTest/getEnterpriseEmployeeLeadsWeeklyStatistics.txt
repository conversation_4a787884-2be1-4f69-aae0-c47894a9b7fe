0``METHOD``com.github.zhxing.ghosttest.plugin.method.AssertResultTestCase.assertResult
***0``METHOD``com.github.zhxing.ghosttest.plugin.method.AssertResultTestCase.assertResult***
{"@type":"java.util.HashMap",
	"request":{
		"@type":"com.github.zhxing.ghosttest.plugin.method.MethodRequest",
		"args":[
			{
				"@type":"com.facishare.marketing.common.result.Result",
				"data":0,
				"errCode":0,
				"errMsg":"成功"
			}
		],
		"className":"com.github.zhxing.ghosttest.plugin.method.AssertResultTestCase",
		"methodName":"assertResult",
		"paramClassNames":[
			"java.lang.Object"
		],
		"returnClassName":"void"
	},
	"response":{
		"@type":"com.facishare.marketing.common.result.Result",
		"data":0,
		"errCode":0,
		"errMsg":"成功"
	}
}
***END***
