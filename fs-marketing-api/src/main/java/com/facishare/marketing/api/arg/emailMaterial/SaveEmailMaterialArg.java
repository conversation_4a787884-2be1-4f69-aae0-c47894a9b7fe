package com.facishare.marketing.api.arg.emailMaterial;

import com.facishare.marketing.api.vo.BaseVO;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2025/2/25
 * @Desc
 **/
@Data
public class SaveEmailMaterialArg extends BaseVO {

    @ApiModelProperty("id，更新时传入")
    private String id;

    @ApiModelProperty("邮件标题")
    private String title;

    @ApiModelProperty("邮件内容")
    private String content;

    @Override
    public Result checkParam() {
        if (StringUtils.isBlank(title)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), "title is null");
        }
        if (StringUtils.isBlank(content)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), "content is null");
        }
        return super.checkParam();
    }
}
