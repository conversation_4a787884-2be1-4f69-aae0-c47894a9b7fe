package com.facishare.marketing.api.arg.advertiser;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@ApiModel("上报规则创建参数")
public class OCPCUploadRuleCreateArg implements Serializable {

    @ApiModelProperty(value = "ea", hidden = true)
    private String ea;

    @ApiModelProperty(value = "userId", hidden = true)
    private Integer userId;

    @ApiModelProperty(value = "广告账户来源，可填： 1-百度、2-腾讯、3-巨量引擎、", required = true)
    private String adAccountSource;

    @ApiModelProperty(value = "规则详情", required = true)
    private List<OCPCUploadRuleDetailArg> ruleDetailList;

    @ApiModelProperty("转化类型: 73:回访电话接通 74:回访-信息确认 75:回访-发现意向 76:回访-高潜成交 77:回访-成单客户")
    private Integer conversionType;

    @Data
    @ApiModel("上报规则详情创建参数")
    public static class OCPCUploadRuleDetailArg implements Serializable {

        @ApiModelProperty("规则，targetObjApiName为LeadConvertTo格式为：[1,2,3]-- 线索转换的目标 1：客户 2：联系人 3：商机")
        private List<Object> rules = Lists.newArrayList();

        @ApiModelProperty(value = "CRM目标对象： 线索转换-LeadConvertTo 销售线索-LeadsObj 合同-ContractObj 报价单-QuoteObj 销售订单-SalesOrderObj ", required = true)
        private String targetObjApiName;
    }
}
