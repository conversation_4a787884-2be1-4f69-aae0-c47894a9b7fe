package com.facishare.marketing.api.result.photoLibrary;

import com.facishare.marketing.api.result.MaterialTagResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ListPhotoByGroupResult implements Serializable{
    private String id;
    private String photoName;
    private String photoPath;
    private Long photoSize;
    private String groupName;
    private String url;
    private String thumbnailUrl;
    private String ext;
    private String photoForwardUrl;
    private String createBy;
    private Long createTime;
    private boolean top;
    private String cdnUrl;
    @ApiModelProperty("内容标签")
    private List<MaterialTagResult> materialTags;
}
