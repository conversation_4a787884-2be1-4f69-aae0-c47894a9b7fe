package com.facishare.marketing.api.arg;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
public class PageWebHookIdArg implements Serializable {
    @ApiModelProperty("WebHook ID")
    private String webHookId;
    @ApiModelProperty(value = "要查询的页号", allowableValues = "range[1, infinity]")
    private Integer pageNo;
    @ApiModelProperty(value = "分页大小", allowableValues = "range[1, 50]")
    private Integer pageSize;
}
