package com.facishare.marketing.api.service;

import com.facishare.marketing.api.arg.ListMarketingFlowInstanceArg;
import com.facishare.marketing.api.data.FinishMarketingFlowIdData;
import com.facishare.marketing.api.result.GetMarektingFlowInstanceResult;
import com.facishare.marketing.api.result.ListMarketingFlowInstanceResult;
import com.facishare.marketing.common.result.Result;

import java.util.List;

public interface MarketingFlowInstanceService {
    Result<Void> finishTasks(List<FinishMarketingFlowIdData> marketingFlowIdData);

    Result<ListMarketingFlowInstanceResult> list(String ea, Integer fsUserId, ListMarketingFlowInstanceArg arg);

    Result<GetMarektingFlowInstanceResult> get(String ea, Integer fsUserId, String instanceId);

    Result<Void> cancel(String ea, Integer fsUserId, String instanceId);
}
