package com.facishare.marketing.api.vo.qywx;

import com.facishare.marketing.common.typehandlers.value.TagName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 18:36 2020/2/9
 * @ModifyBy
 */
@Data
public class MomentMessageVO implements Serializable {
    /**
     * 当前操作人的员工ID
     */
    protected Integer userId;

    @ApiModelProperty("营销活动名字")
    private String name;

    @ApiModelProperty("通知发送客户")
    private List<String> userIdList;

    @ApiModelProperty("通知员工标签")
    private List<Integer> tagIds;

    @ApiModelProperty("通知发送部门")
    private List<Integer> departmentIds;

    @ApiModelProperty(value = "文本消息")
    private String text;

    @ApiModelProperty(value = "图片消息")
    private List<Image> image;

    @ApiModelProperty(value = "图文消息")
    private Link link;
    @ApiModelProperty(value = "视频id")
    private String videoMediaId;
    @ApiModelProperty(value = "视频名称")
    private String videoName;

    @ApiModelProperty(value = " 消息类型 1 h5， 2：图片， 3:视频, 10:文件")
    private Integer msgType;

    @ApiModelProperty("发送类型 1：立即发送 2：定时发送")
    private Integer type;
    @ApiModelProperty("定时推送时间, 仅type=2时有值")
    private Long fixedTime;
    @ApiModelProperty("企微标签列表")
    private List<TagName> tags;

    //新增msgType 文件
    private String fileName;
    //文件id
    private String fileId;
    //文件类型
    private String fileExt;
    //页面地址
    private String fileUrl;

    @Data
    public static class Image implements Serializable {
        @ApiModelProperty("图片路径")
        private String imagePath;
        @ApiModelProperty("图片名称")
        private String name;
        @ApiModelProperty("图片大小")
        private Integer size;
        @ApiModelProperty("下载地址")
        private String downLoadUrl;
    }

    @Data
    public static class Link implements Serializable {
        @ApiModelProperty("图文消息标题")
        private String title;
        @ApiModelProperty("文消息封面的TN path")
        private String picPath;
        @ApiModelProperty("封面的下载地址")
        private String picUrl;
        @ApiModelProperty("图文消息的链接")
        private String url;//图文消息的链接
        @ApiModelProperty("图文消息的链接")
        private int linkType;//图文消息类型
        @ApiModelProperty("物料名称")
        private String objectName;
    }


}
