package com.facishare.marketing.api.result.memberCenter;

import com.facishare.marketing.common.util.GsonUtil;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhengh on 2021/1/4.
 */
@Data
public class QueryMemberInfoResult implements Serializable{
    @ApiModelProperty(name = "是否为会员")
    private boolean checkIsMember;

    @ApiModelProperty(name = "会员信息")
    private MemberInfo memberInfo;

    private String ea;

    private Integer ei;
    
    @Data
    public static class MemberInfo implements Serializable{
        @ApiModelProperty(name = "会员id")
        private String memberId;

        @ApiModelProperty(name = "会员头像")
        private String avatar;

        @ApiModelProperty(name = "会员名称")
        private String name;

        @ApiModelProperty(name = "会员等级")
        private String grade;

        @ApiModelProperty(name = "会员积分")
        private String integralValue;
    }

    @Data
    public static class AvatarObject implements Serializable{
        private String path;
        private String ext;
        private String filename;
        private double size;
    }
}
