package com.facishare.marketing.api.arg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Created on 2021-03-09.
 */
@Data
public class PageTriggerInstancesArg extends PageArg implements Serializable {
	private String triggerId;
	private String sceneId;
	/** @see com.facishare.marketing.common.enums.TriggerTaskTypeEnum */
	@ApiModelProperty("为空查全部 send_wx_msg(微信消息) send_sms_msg(短信) send_email_msg(邮件) add_tag(添加标签) add_board(看板) send_union_msg（互动通知）")
	private String taskType;
	private Long queryTime;
	private String triggerRecordId; //触发记录id，外部触发才有这个参数
	private String triggerSnapshotId;
	private String triggerTaskSnapshotId;
}
