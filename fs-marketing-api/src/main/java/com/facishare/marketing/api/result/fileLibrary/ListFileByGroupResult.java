package com.facishare.marketing.api.result.fileLibrary;

import com.facishare.marketing.api.result.MaterialTagResult;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

@Data
public class ListFileByGroupResult implements Serializable{
    private String id;
    private String fileName;
    private String filePath;
 //   private String fileNPath;
    private Long fileSize;
    private String groupName;
    private String ext;
    private String createBy;
    private Long createTime;
    private String previewUrl;
    private String downloadUrl;
    private TagNameList tagNames;
    private boolean top;
    private String coverUrl;
    @ApiModelProperty("内容标签")
    private List<MaterialTagResult> materialTags;
}
