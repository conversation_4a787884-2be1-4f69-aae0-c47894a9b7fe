package com.facishare.marketing.api.result.live;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhengh on 2020/3/30.
 */
@Data
public class LiveBriefStatisticsResult implements Serializable{
    private int spreadCount;      //企业推广数
    private int uv;               //访问物料人数
    private int enrollCount;      //报名人数
    private int leadCount;        //线索数量
    // 互动
    private int chatUserCount;        //互动人数
    private int chatTimes;            //互动次数
    // 直播
    private int viewCount;        //观看直播人数
    private int viewTimes;        //观看直播次数
    private Integer viewDuration;     //观看直播时长
    private Float perViewDuration = 0f; // 平均观看时长
    private Float perViewTimes = 0f; // 平均观看次数
    // 回放
    private int viewRecordUserCount;  //观看回放人数
    private int viewRecordTimes;      //观看回访次数
    private Integer perRecordDuration; // 人均点播时长
    // 驻留
    private Integer stayCount; //驻留人数
    private Integer stayTimes; //驻留次数
    private Integer stayDuration; //驻留时长
}
