package com.facishare.marketing.api.result.conference;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Map;
import lombok.Data;

/**
 * Created  By zhoux 2019/07/22
 **/
@Data
public class GetConferenceStatisticDataResult implements Serializable {

    @ApiModelProperty("报名数")
    private Integer enrollCount;

    @ApiModelProperty("签到数")
    private Integer signInCount;

    @ApiModelProperty("新增线索数")
    private Integer newLeadCount;

    @ApiModelProperty("签到率")
    private String signInRatio;

    @ApiModelProperty("待审核人数")
    private Integer reviewCount;

    @ApiModelProperty("邀约人数")
    private Integer inviteCount;

    @ApiModelProperty("待邀约人数")
    private Integer notInviteCount;

    @ApiModelProperty("异常数据")
    private Integer errorDataCount;

    @ApiModelProperty("渠道数据")
    private Map<String, Integer> channelStatistic;

    @ApiModelProperty("uv")
    private Integer uv;
}
