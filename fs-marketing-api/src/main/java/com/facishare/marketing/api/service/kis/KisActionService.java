package com.facishare.marketing.api.service.kis;

import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.arg.qywx.record.MiniAppRecordArg;
import com.facishare.marketing.api.arg.qywx.record.UpdateLookUpArg;
import com.facishare.marketing.common.result.Result;

/**
 * Created by ranluch on 2019/3/11.
 */
public interface KisActionService {
    Result record(RecordActionArg arg);

    Result miniAppRecord(MiniAppRecordArg arg);

    Result generateID();

    Result<Void> updateLookUpStatus(String uid,Integer fsUserId,String ea,String marketingActivityId);
}
