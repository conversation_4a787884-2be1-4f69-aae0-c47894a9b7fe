package com.facishare.marketing.api.arg.qywx.media;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class ListFileByGroupIdArg extends QYWXBaseArg {
    @ApiModelProperty(value = "文件分组id")
    private String groupId;

    @ApiModelProperty(value = "搜索关键词")
    private String keyword;

    @ApiModelProperty(value = "当前页码", required = true)
    private Integer pageNum;
    @ApiModelProperty(value = "每页数量", required = true)
    private Integer pageSize;
    @ApiModelProperty(value = "第一页请求时间戳")
    private Long time;

    private String menuId;
}
