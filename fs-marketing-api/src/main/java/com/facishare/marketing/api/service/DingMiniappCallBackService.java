package com.facishare.marketing.api.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.common.result.Result;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021/10/8 10:22 上午
 */
public interface DingMiniappCallBackService {
    /**
     * 钉钉小程序回调事件
     * @param signature       签名
     * @param msg_signature   消息体签名
     * @param timeStamp       时间戳
     * @param nonce           随机字符串
     * @param json            加密消息
     * @return
     */
    Map<String, String> dingCallBack(String signature, String msg_signature, String timeStamp, String nonce, JSONObject json);
}
