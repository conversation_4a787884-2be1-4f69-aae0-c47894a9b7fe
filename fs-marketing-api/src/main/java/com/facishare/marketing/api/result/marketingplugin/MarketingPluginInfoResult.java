package com.facishare.marketing.api.result.marketingplugin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MarketingPluginInfoResult implements Serializable {

    @ApiModelProperty("在营销通中的id")
    private String id;
    @ApiModelProperty("企业ea")
    private String ea;
    @ApiModelProperty("营销插件类型")
    private Integer pluginType;
    @ApiModelProperty("营销插件名称")
    private String pluginName;
    @ApiModelProperty("营销插件状态")
    private Boolean status;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
}
