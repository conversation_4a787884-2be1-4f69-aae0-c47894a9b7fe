package com.facishare.marketing.api.result.wxcoupon;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/8 14:22
 */
@Data
public class CouponOrgResult implements Serializable {

    @SerializedName("_id")
    private String weChatCouponId;

    @SerializedName("stock_name")
    private String stockName; //优惠券名称

    @SerializedName("comment")
    private String comment; //备注

    @SerializedName("stock_type")
    private String stockType; //优惠券类型

    @SerializedName("type")
    private Integer type;

    @SerializedName("goods_name")
    private String goodsName; //适用商品名称

    @SerializedName("transaction_minimum")
    private Integer transactionMinimum; //消费门槛

    @SerializedName("discount_amount")
    private Integer discountAmount; // 优惠金额

    @SerializedName("discount_percent")
    private Integer discountPercent;//折扣比例

    @SerializedName("exchange_price")
    private Integer exchangePrice;//单品换购价

    @SerializedName("description")
    private String description; //使用说明

    @SerializedName("hide_link")
    private Integer hideLink; //是否隐藏链接 0:隐藏 1:不隐藏

    @SerializedName("expired_tip")
    private Integer expiredTip; //是否开启过期提醒  0:开启 1: 不开启

    @SerializedName("expired_days")
    private Integer expiredDays; // 过期前多少天提醒

    @SerializedName("merchant_name")
    private String merchantName; //商户名称

    @SerializedName("merchant_logo_url")
    private String merchantLogoUrl; //商户logo

    @SerializedName("background_color")
    private String backgroundColor;//背景颜色

    @SerializedName("coupon_image_url")
    private String couponImageUrl; //券详情图片

    @SerializedName("coupon_code_mode")
    private String couponCodeMode;//券code 模式

    @SerializedName("status")
    private Integer status; // 模板状态  0:正常 1:删除

    @SerializedName("template_id")
    private String templateId;

    @SerializedName("marketing_event_id")
    private String marketingEventId;

    @SerializedName("channel")
    private String channel;

    @SerializedName("max_coupons")
    private Integer maxCoupons;

    @SerializedName("max_coupons_per_user")
    private Integer maxCouponsPerUser;

    @SerializedName("is_member")
    private Integer isMember;

    @SerializedName("available_begin_time")
    private Long availableBeginTime;

    @SerializedName("available_end_time")
    private Long availableEndTime;

    @SerializedName("available_day_after_receive")
    private Integer availableDayAfterReceive;

    @SerializedName("week_day")
    private String weekDay;

    @SerializedName("wait_days_after_receive")
    private Integer waitDaysAfterReceive;

    @SerializedName("available_day_time")
    private String availableDayTime;

    @SerializedName("use_method")
    private String useMethod;

    @SerializedName("tags")
    private String tags;

    @SerializedName("out_request_no")
    private String outRequestNo;

    @SerializedName("stock_id")
    private String stockId;

    @SerializedName("outer_tenant_range")
    private String sendScope;
}
