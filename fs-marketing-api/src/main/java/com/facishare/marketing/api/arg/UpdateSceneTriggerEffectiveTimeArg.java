package com.facishare.marketing.api.arg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Created on 2021-02-26.
 */
@Data
public class UpdateSceneTriggerEffectiveTimeArg implements Serializable {
	private String sceneTriggerId;
	/** @see com.facishare.marketing.common.enums.EffectiveTimeType */
	@ApiModelProperty("可选：forever range")
	private String effectiveTimeType;
	private Long startEffectiveTime;
	private Long endEffectiveTime;
}
