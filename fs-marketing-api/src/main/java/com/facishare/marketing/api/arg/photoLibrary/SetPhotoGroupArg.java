package com.facishare.marketing.api.arg.photoLibrary;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

@Data
public class SetPhotoGroupArg implements Serializable{
    @ApiModelProperty(value = "分组id")
    private String groupId;

    @ApiModelProperty(value = "图片id列表")
    private List<String> photoIds;

    public boolean checkParamValid(){
        if (StringUtils.isEmpty(groupId) || CollectionUtils.isEmpty(photoIds)){
            return false;
        }

        return true;
    }
}
