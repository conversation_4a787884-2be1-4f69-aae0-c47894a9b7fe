package com.facishare.marketing.api.arg.whatsapp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("直接发送信息参数")
public class DirectSendMessageArg implements Serializable {

    private String ea;

    @ApiModelProperty("发送的商户的WhatsApp号码，需要带国码")
    private String businessPhone;

    @ApiModelProperty("号码列表")
    private List<String> phoneList;

    @ApiModelProperty("模板名称")
    private String templateName;

    @ApiModelProperty
    private String templateLanguage;

    @ApiModelProperty("模板参数")
    private List<String> parameterList;
}
