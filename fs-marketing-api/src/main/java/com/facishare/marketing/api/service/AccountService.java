package com.facishare.marketing.api.service;

import com.facishare.marketing.api.arg.AccountIsApplyForKISArg;
import com.facishare.marketing.api.result.account.*;
import com.facishare.marketing.api.result.qywx.QywxBaseInfoResult;
import com.facishare.marketing.api.result.qywx.ResetAppUserDataResult;
import com.facishare.marketing.common.result.Result;

public interface AccountService {

    Result<AccountIsApplyForKISResult> isApplyForKIS(AccountIsApplyForKISArg arg);


    Result<AccountIsApplyForKISResult> isApplyForQyWxKIS(String ea, Integer userId, String uid, String appId);


    Result<GetFsUserInfoResult> getFsUserInfo(String ea, Integer fsUserId, String uid);


    Result sendSMCode(String phone, Integer objectType, String objectId);

    Result checkSMCode(String phone, String verifyCode);

    Result<String> bindToWxWorkExternalUser(String ea, String uid, String wxWorkExternalUserId);

    Result<QywxBaseInfoResult> qywxBaseInfo(String ea, String appId);

    Result<ResetAppUserDataResult> resetAppUserData(String ea, Integer userId);

    Result<GetQywxBaseInfoFromWxResult> getQywxBaseInfoFromWx(String targetUid);

    Result<String> getApplyInfoKeyForWx(String ea, Integer fsUserId);


    Result<GetDownstreamEmployeeInfoResult> getDownstreamEmployeeInfo(String erUpstreamEa, String erOuterTenantId, String erOuterUid);


    Result<QywxEmployeeBindWxUserResult> queryQywxH5UserBindWxUserInfo(String ea, Integer fsUserId, String qywxCorpId, String qywxUserId, String appId);

    public Result<GetFsUserInfoResult> queryEmployeeByOfficeAccountOpenId(String officeAccountAppId, String officeAccountOpenId);

    Result<EmployeeRelationInfo> getEmployeeRelationInfoByFsUserId(String ea, Integer fsUserId);

    Result<Integer> checkEnterpriseRelationHasCrmAccount(String ea, String outerUid, String outerTenantId);
}
