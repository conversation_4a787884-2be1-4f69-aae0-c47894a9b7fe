package com.facishare.marketing.api.arg.marketingactivity;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/2/25.
 */
@Data
public class GetMarketingActivityArg  implements Serializable {
    @ApiModelProperty(value = "营销活动Id")
    private String id;
    @ApiModelProperty(value = "是否伙伴营销")
    private boolean partner;
    @ApiModelProperty(value = "互联服务号")
    private String appid;
}