package com.facishare.marketing.api.arg;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ListActivityListArg extends QYWXBaseArg {
    private Integer pageNum;
    private Integer pageSize;
    private String objectId;
    private Integer objectType;
    private Boolean isAllActivityType;
    private List<String> activityTypes;
    private List<String> marketingEventIds;

    @ApiModelProperty("市场活动选择器")
    private FilterData filterData;

    @ApiModelProperty("是否根据市场活动id顺序排序")
    private Boolean orderByIds;
    // 标签过滤
    private MaterialTagFilterArg materialTagFilter;

    @ApiModelProperty("关键字")
    private String keyword;

    /** @see com.facishare.marketing.common.enums.marketingactivity.ActivityOrderTypeEnum */
    @ApiModelProperty("排序规则") // 0:默认<活动创建时间> 1:活动开始时间 2:活动结束时间
    private Integer orderType;

    private List<String> eventFormList;
}
