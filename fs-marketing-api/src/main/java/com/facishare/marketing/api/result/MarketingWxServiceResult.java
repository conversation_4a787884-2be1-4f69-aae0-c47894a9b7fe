package com.facishare.marketing.api.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel
public class MarketingWxServiceResult implements Serializable{
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("企业账号")
    private String ea;
    @ApiModelProperty("服务号id")
    private String appId;
    @ApiModelProperty("服务号名称")
    private String appName;
    @ApiModelProperty("微信服务号id")
    private String wxAppId;
    @ApiModelProperty("微信服务号名称")
    private String wxAppName;
    @ApiModelProperty("服务号logo")
    private String appLogoUrl;
    @ApiModelProperty("服务号描述")
    private String appDesc;
    @ApiModelProperty("纷享服务号logo")
    private String appLogoFxiaokeUrl;
    @ApiModelProperty("纷享服务号logo文件id")
    private String appLogoFxiaokeFileId;
    @ApiModelProperty("微信粉丝数")
    private Integer wxFansNumber;
    @ApiModelProperty("排序码")
    private Integer orderCode;
    @ApiModelProperty("创建者")
    private Integer createBy;
}
