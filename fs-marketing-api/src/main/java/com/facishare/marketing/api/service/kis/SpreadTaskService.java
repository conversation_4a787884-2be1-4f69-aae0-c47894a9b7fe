package com.facishare.marketing.api.service.kis;

import com.facishare.marketing.api.result.SpreadTaskNormalResult;
import com.facishare.marketing.api.result.kis.KisNoticeDetailResult;
import com.facishare.marketing.api.result.kis.QuerySpreadTaskListResult;
import com.facishare.marketing.common.result.Result;

/**
 * Created by zhengh on 2019/2/21.
 */
public interface SpreadTaskService {
    /**
     * 查询推广任务
     * @param ea
     * @param userId
     * @return
     */
    Result<QuerySpreadTaskListResult> querySpreadTaskList(String ea, Integer userId, boolean isQywx,String upstreamEa);

    /**
     * 从全员推广通知中完成推广任务
     * @param ea
     * @param userId
     * @param noticeId
     * @return
     */
    Result<Void> spreadTaskByNotice(String ea, Integer userId, String noticeId);

    /**
     * 查询推广通知详情，KIS侧推广通知中间跳转页调用
     * @param noticeId
     * @return
     */
    Result<KisNoticeDetailResult> getNoticeDetail(String noticeId);

    Result<Void> qywxTaskSendRecord(String fsEa, Integer fsUserId, String externalUserId, String qyUserId, String marketingActivityId, String triggerTaskInstanceId, Integer type);

    Result<Boolean> spreadTaskIsRevocation(String id);

    Result<SpreadTaskNormalResult> spreadTaskIsNormal(String objectId,Integer objectType,String marketingActivityId,String marketingEventId, String fsEa);
}
