package com.facishare.marketing.api;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("获取签到成功活动成员数据")
public class GetCampaignDataArg extends QYWXBaseArg implements Serializable {

    @ApiModelProperty("会议id")
    private String conferenceId;

    @ApiModelProperty("市场活动id")
    private String marketingEventId;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty(hidden = true)
    private String fingerPrint;

}
