package com.facishare.marketing.api.arg;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class AccountIsApplyForKISArg implements Serializable {

    private String ea;

    private Integer fsUserId;

    private String outerTenantId;

    private String outerUid;

    private String upstreamEa;


    public boolean isPartner(){
        return StringUtils.isNotBlank(this.getOuterTenantId()) && StringUtils.isNotBlank(this.getOuterUid());
    }
}
