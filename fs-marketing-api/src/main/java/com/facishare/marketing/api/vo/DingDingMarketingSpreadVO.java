package com.facishare.marketing.api.vo;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.vo.qywx.QywxAddExtenalUserVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/24 16:35
 */
@Data
public class DingDingMarketingSpreadVO implements Serializable {
    private static final long serialVersionUID = -1345411025949396563L;
    public static final String TAG = "FS_MK_DING_SPREAD_SEND_TAG";
    private Map<String,String> userSpreadUrlMap;
    private String title;
    private String content;
    private String ea;
    private String singleTitle;

    public  static DingDingMarketingSpreadVO fromToDingDingMarketingSpreadVO(byte[] bytes){
        DingDingMarketingSpreadVO spreadVO = JSON.parseObject(new String(bytes), DingDingMarketingSpreadVO.class);
        return spreadVO;
    }
}
