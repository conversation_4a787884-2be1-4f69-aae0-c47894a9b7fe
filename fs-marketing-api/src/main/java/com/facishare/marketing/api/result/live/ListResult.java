package com.facishare.marketing.api.result.live;

import com.facishare.marketing.api.result.MaterialTagResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/3/23.
 */
@Data
public class ListResult implements Serializable{
    @ApiModelProperty("数据id")
    private String id;

    @ApiModelProperty("直播标题")
    private String title;

    @ApiModelProperty("直播简介")
    private String description;

    @ApiModelProperty("直播开始时间")
    private Long startTime;

    @ApiModelProperty("直播结束时间")
    private Long endTime;

    @ApiModelProperty("直播观看链接-短链")
    private String viewUrl;

    @ApiModelProperty("直播封面")
    private String cover;

    @ApiModelProperty("直播状态")
    private Integer status;

    @ApiModelProperty("访问次数")
    private int pv;

    @ApiModelProperty("访问人数")
    private int uv;

    @ApiModelProperty("直播互动次数")
    private Integer chatTimes;

    @ApiModelProperty("报名人数")
    private int enrollCount;

    @ApiModelProperty("观看直播人数")
    private int totalViewUsers;

    @ApiModelProperty("获取线索数")
    private int leads;

    @ApiModelProperty("营销活动id")
    private String marketingEventId;

    @ApiModelProperty("直播平台")
    private Integer platform;

    @ApiModelProperty("是否回放 false:没有 true：有")
    private boolean hasRecord;

    @ApiModelProperty("报名表单微页面id")
    private String submitHexagonId;

    @ApiModelProperty("报名表单微页面名称")
    private String submitHexagonName;

    @ApiModelProperty(value = "生命状态")
    private String lifeStatus;

    @ApiModelProperty(value = "目睹主活动id")
    private String muduParentId;

    @ApiModelProperty(value = "是否为目睹子活动")
    private Integer subEvent;

    @ApiModelProperty(value = "驻留人数")
    private Integer stayCount;

    @ApiModelProperty("内容标签")
    private List<MaterialTagResult> materialTags;
}
