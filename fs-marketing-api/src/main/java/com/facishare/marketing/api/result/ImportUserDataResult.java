package com.facishare.marketing.api.result;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * Created  By zhoux 2019/10/16
 **/
@Data
public class ImportUserDataResult implements Serializable {

    @ApiModelProperty(value = "错误文件apath")
    private String errorFileApath;

    @ApiModelProperty(value = "成功数")
    private Long successNum;

    @ApiModelProperty(value = "失败数")
    private Long failNum;

}
