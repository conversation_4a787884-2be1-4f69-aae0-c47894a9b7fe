package com.facishare.marketing.api.result.live;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/7/5
 **/
@Data
public class QueryThirdLiveResult implements Serializable {
    private String eventId; // 活动ID
    private String eventNumber; //活动编号
    private String eventName; //活动名称
    private String startTime; //活动开始时间
    private String endTime; //活动结束时间
    private Integer eventStatus; //活动状态
    private String coverUrl; //封面图链接
}
