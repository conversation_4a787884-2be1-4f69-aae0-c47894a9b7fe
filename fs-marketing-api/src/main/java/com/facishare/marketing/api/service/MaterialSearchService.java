package com.facishare.marketing.api.service;

import com.facishare.marketing.api.arg.SearchMaterialVO;
import com.facishare.marketing.api.result.SearchKnowledgeResult;
import com.facishare.marketing.api.result.SearchMaterialResult;
import com.facishare.marketing.common.result.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/4/6
 * @Desc 营销物料
 **/
public interface MaterialSearchService {

    Result<List<SearchMaterialResult>> search(SearchMaterialVO vo);

    Result<List<SearchKnowledgeResult>> knowledgeSearch(SearchMaterialVO vo);
}
