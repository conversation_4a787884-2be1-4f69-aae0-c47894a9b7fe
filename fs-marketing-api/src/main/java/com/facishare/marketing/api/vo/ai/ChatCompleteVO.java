package com.facishare.marketing.api.vo.ai;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class ChatCompleteVO extends QYWXBaseArg {
    private String sessionId;
    private String recordId;
    private String type;
    private String prompt;
    private String cardType;//预留
    private Property property;

    @Data
    public static class Property implements Serializable {
        private String defaultHelperName;//默认助手
        private String templateId;//提示模板id
        private String sceneId;//ai场景
        private List<CustomizeObject> objects;

        private String bizSessionId;
        private String sessionId;
        private String buttonApiName;
        private List<Object> variables;

        private boolean isStreaming;
        private String channel;

        private boolean debug;
        private String businessName;

        private boolean isNewApi;

        private String referenceText;
        private List<Attachments> contentList = new ArrayList<>();

        public boolean isPaasAgent() {
            return StringUtils.isNotEmpty(defaultHelperName) && defaultHelperName.startsWith("Copilot_");
        }
    }

    @Data
    public static class CustomizeObject implements Serializable {
        @SerializedName("object_api_name")
        private String objectApiName;
        @SerializedName("object_id")
        private String objectId;
    }

    @Data
    public static class Attachments implements Serializable {
        private String type;
        private String subType;
        private Source source;
        private String text;

        public static Attachments ofTextAttachments(String text) {
            Attachments attachments = new Attachments();
            attachments.type = "text";
            attachments.text = text;
            return attachments;
        }
    }

    @Data
    public static class Source implements Serializable {
        private String url;
        private String uploadTime;
        private String sizeText;
        private Long size;
        private String path;
        private String name;
        private String ext;
    }

    public Property getProperty() {
        return this.property != null ? this.property : new Property();
    }

    public String getDefaultHelperName() {
        return getProperty().getDefaultHelperName();
    }

    public String getTemplateId() {
        return getProperty().getTemplateId();
    }

    public String getSceneId() {
        return getProperty().getSceneId();
    }

    public String getBizSessionId() {
        return getProperty().getBizSessionId();
    }
}
