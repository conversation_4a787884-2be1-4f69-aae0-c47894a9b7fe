package com.facishare.marketing.api.arg.qywx.wxContact;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2020/03/13
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class QueryQywxGroupSendFsUserInfoArg extends QYWXBaseArg {

    @ApiModelProperty("营销活动id")
    private String marketingActivityId;

    @ApiModelProperty("推广类型")
    private Integer spreadType;    //枚举：QywxSpreadTypeEnum

    public boolean isWrongParam() {
        return StringUtils.isBlank(marketingActivityId);
    }

}
