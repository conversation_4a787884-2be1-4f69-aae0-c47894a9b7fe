package com.facishare.marketing.api.result.kis;

import com.facishare.marketing.api.result.qywx.QywxGroupSendMessageDetailResult;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.ToString;

/**
 * @创建人 zhengliy
 * @创建时间 2019/2/28 13:46
 * @描述
 */
@Data
@ToString
public class TempListAllMarketingActivityResult implements Serializable {
    private static final long serialVersionUID = 8096180677480468261L;
    @ApiModelProperty(value = "活动id")
    String marketingActivityId;
    @ApiModelProperty(value = "推广时间")
    private Long createTime;
    @ApiModelProperty(value = "推广类型: 1->全员推广 2->服务号推广 3->短信推广 5 -> 企业微信全员推广 12-> whatsapp推广")
    Integer spreadType;
    @ApiModelProperty(value = "全员推广推广信息")
    private MarketingActivityNoticeSendStatisticResult marketingActivityNoticeSendStatisticResult;
    @ApiModelProperty(value = "公众号推广信息")
    private MarketingActivityWeChatServiceStatisticResult marketingActivityWeChatServiceStatisticResult;
    @ApiModelProperty(value = "短信推广信息")
    private MarketingActivitySmsSendStatisticResult marketingActivitySmsSendStatisticResult;
    @ApiModelProperty(value = "企业微信群发消息")
    private QywxGroupSendMessageDetailResult qywxGroupSendMessageDetailResult;
    @ApiModelProperty(value = "企业微信群发消息(统计数据)")
    private MarketingActivityQywxGroupSendMessageResult marketingActivityQywxGroupSendMessageResult;
    @ApiModelProperty(value = "邮件营销信息")
    private MarketingMailGroupSendResult marketingMailGroupSendResult;
    @ApiModelProperty(value = "伙伴推广信息")
    private MarketingActivityPartnerSendStatisticResult marketingActivityPartnerSendStatisticResult;

    @ApiModelProperty(value = "WhatsApp推广信息")
    private MarketingActivityWhatsAppSpreadResult marketingActivityWhatsAppSpreadResult;

    @ApiModelProperty(value = "推广标题")
    private String spreadContent;
    @ApiModelProperty(value = "浏览次数")
    private Integer pv;
    @ApiModelProperty(value = "浏览人数")
    private Integer uv;
    @ApiModelProperty(value = "线索数")
    private Integer leadCount;
    @ApiModelProperty("是否可以取消发送")
    private Boolean sendCancelable;
    @ApiModelProperty(value = "图文消息标题")
    private String graphicMessageTitle;
    @ApiModelProperty(value = "图文消息图片")
    private String graphicMessagePic;

    @ApiModelProperty("审核状态")
    private String auditStatus;

    @ApiModelProperty("发送状态")
    private Integer status;

    @ApiModelProperty("推广状态")
    private Integer spreadStatus;
}
