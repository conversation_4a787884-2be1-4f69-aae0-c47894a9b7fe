package com.facishare.marketing.api.result;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CustomerSaveSettingResult implements Serializable {
    private static final long serialVersionUID = 1L;
    private boolean isSaveCustomerToCrmEnabled;
    private List<MankeepFieldResult> mankeepFieldsForCrmCustomerSave;
    private List<FieldMappingConfigResult> customerFieldMappingList;
    private List<MankeepFieldResult> mankeepFieldsForCrmContactSave;
    private List<FieldMappingConfigResult> contactFieldMappingList;

    public CustomerSaveSettingResult() {
        this.isSaveCustomerToCrmEnabled = true;
        this.mankeepFieldsForCrmCustomerSave = new ArrayList<>(0);
        this.customerFieldMappingList = new ArrayList<>(0);
        this.mankeepFieldsForCrmContactSave = new ArrayList<>(0);
        this.contactFieldMappingList = new ArrayList<>(0);
    }
}
