package com.facishare.marketing.api.result;

import com.facishare.marketing.common.typehandlers.value.Filter;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhengh on 2018/7/25.
 */
@Data
public class NoticeDetailResult implements Serializable {
    private String uid;
    private String contentId;
    private Integer status;
    /** 通知标题 **/
    private String title;
    /** 推广物料 见 com.facishare.mankeep.common.enums.NoticeContentTypeEnum **/
    private Integer contentType;
    /** 推广内容 **/
    private String content;
    /** 物料详情 **/
    private ContentDetail contentDetail;
    /** 宣传语 **/
    private String description;
    /** 发送类型： 1：立即发送 2：定时发送 **/
    private Integer sendType;
    /** 发送时间 **/
    private Long sendTime;
    /** 发送范围 **/
    private NoticeVisibilityVO noticeVisibilityVO;
    /**推广封面url**/
    private String coverUrl;
    /**推广封面apath**/
    private String coverPath;
    /** 定时时间 **/
    private Long timingDate;
    /** 推广开始时间 **/
    private Long startTime;
    /** 推广结束时间 **/
    private Long endTime;
    /** 创建时间 **/
    private Long createTime;

    @Data
    public static class NoticeVisibilityVO implements Serializable {
        /** 部门列表 **/
        private List<Integer> departmentIds;
        /** 用户id列表 **/
        private List<Integer> userIds;
        /**用户角色列表**/
        private List<RoleDetail> roles;
        // 会员营销筛选条件
        private List<Filter> filters;
    }

    @Data
    public static class ContentDetail implements Serializable {
        private String image;
        private String title;
    }

    @Data
    public static class RoleDetail implements Serializable{
        private String roleCode;
        private String roleName;
    }
}
