package com.facishare.marketing.api.arg.photoLibrary;

import com.facishare.marketing.api.arg.BasePageArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ListPhotoByGroupArg extends BasePageArg {
    @ApiModelProperty(value = "图片源 1:图片库  2：选择器", required = true)
    private Integer source;

    @ApiModelProperty(value = "图片分组id")
    private String groupId;

    @ApiModelProperty(value = "搜索关键词")
    private String keyword;

    @ApiModelProperty(value = "分组是否移动端展示,0全部,1在移动端展示,2不在移动端展示")
    private Integer mobileDisplay;

    // 标签过滤
    private MaterialTagFilterArg materialTagFilter;

    private String menuId;

}