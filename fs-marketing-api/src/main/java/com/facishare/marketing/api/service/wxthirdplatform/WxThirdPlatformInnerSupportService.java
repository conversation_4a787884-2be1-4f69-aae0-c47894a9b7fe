package com.facishare.marketing.api.service.wxthirdplatform;

import com.facishare.marketing.api.arg.DispatchRequestArg;
import com.facishare.marketing.api.result.DispatchRequestResult;
import com.facishare.marketing.api.result.QueryWxMiniListResult;
import com.facishare.marketing.api.result.WxCodeTemplateResult;
import com.facishare.marketing.api.result.WxTemplateVersionResult;
import com.facishare.marketing.common.result.Result;
import java.util.List;

/**
 * <AUTHOR>
 * Created on 2020-12-14.
 */

public interface WxThirdPlatformInnerSupportService {
	Result<String> getWxAppIdByFSPlatformIdAndEa(String fsPlatformId, String ea);
	
	Result<String> getEaByFSPlatformIdAndWxAppId(String fsPlatformId, String wxAppId);
	
	Result<DispatchRequestResult> dispatchRequest(String fsPlatformId, String wxAppId, DispatchRequestArg dispatchRequestArg);

    Result<List<QueryWxMiniListResult>> getWxMiniAppList(String fsPlatformId, String keywordType, String keyword);

    Result<String> batchCommitCodeAndSubmitAudit(List<String> appIds, String platformId);

	Result<String> batchReleaseCode(List<String> appIds, String platformId);

    Result<WxCodeTemplateResult> getWxMiniAppNewVersion(String platformId);

    Result<String> batchUndoCodeAudit(List<String> appIds, String platformId);

    Result<String> updateShowVersion(String showCodeTemplateId, String showCodeVersion, String showCodeDescription, String platformId);

    Result<List<WxTemplateVersionResult>> getVersionList(String platformId);
}
