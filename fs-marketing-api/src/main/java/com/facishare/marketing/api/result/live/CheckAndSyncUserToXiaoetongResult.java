package com.facishare.marketing.api.result.live;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CheckAndSyncUserToXiaoetongResult implements Serializable{
    @ApiModelProperty("用户是否报名直播")
    private boolean submit;

    @ApiModelProperty("用户登录接口url")
    private String loginUrl;

    private int code;  //code 0:返回有效地址  1：客户未报名
}
