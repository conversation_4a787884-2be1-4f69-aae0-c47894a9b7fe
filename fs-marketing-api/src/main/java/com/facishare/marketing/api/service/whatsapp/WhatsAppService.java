package com.facishare.marketing.api.service.whatsapp;

import com.facishare.marketing.api.arg.whatsapp.*;
import com.facishare.marketing.api.vo.whatsapp.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.fxiaoke.crmrestapi.common.data.ObjectData;

import java.util.List;


public interface WhatsAppService {

    // 获取whatsapp授权信息
    Result<AuthorizationVO> getAuthorization(String ea);

    // 保存whatsapp授权信息
    Result<Void> saveAuthorization(SaveAuthorizationArg saveAuthorizationArg);

    /**
     * 查询whatsapp模板
     */
    Result<TemplateVO> queryTemplate(QueryTemplateArg queryTemplateArg);

    /**
     * 获取商家手机号码
     */
    Result<List<BusinessPhoneVO>> getBusinessPhone(String ea);

    /**
     * 直接发送模板消息 不会有任何业务记录
     */
    Result<Void> directSendTemplateMessage(DirectSendMessageArg directSendMessageArg);

    /**
     * 获取发送任务列表
     */
    Result<PageResult<SpreadListVO>> spreadList(SpreadListArg spreadListArg);

    /**
     * 定时发送消息
     */
    Result<Void> sendMessageByJob();

    /**
     * 删除发送任务
     */
    Result<Void> deleteSendTask(DeleteSendTaskArg deleteSendTaskArg);

    /**
     * 取消发送任务
     */
    Result<Void> cancelSendTask(CancelSendTaskArg cancelSendTaskArg);

    Result<PageResult<SendDetailVO>> sendDetail(SendDetailArg sendDetailArg);

    Result<Integer> checkWhatsAppPluginOpenCondition(String ea);

    Result<PLoginDetailVO> pLogin(PLoginArg pLoginArg);

    Result<SyncDataResultVO> syncData(SyncDataArg syncDataArg);

    Result<List<ObjectData>> queryDataByIds(QueryDataByIdsArg queryDataByIdsArg);
}
