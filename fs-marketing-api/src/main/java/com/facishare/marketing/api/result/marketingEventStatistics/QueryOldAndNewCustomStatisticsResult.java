package com.facishare.marketing.api.result.marketingEventStatistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("查询新老客户分布结果")
public class QueryOldAndNewCustomStatisticsResult implements Serializable {

    @ApiModelProperty("老客户数量")
    private Integer oldCustomNum = 0;

    @ApiModelProperty("新客户数量")
    private Integer newCustomNum = 0;

    @ApiModelProperty("存入失败客户数量")
    private Integer failCustomNum = 0;

}
