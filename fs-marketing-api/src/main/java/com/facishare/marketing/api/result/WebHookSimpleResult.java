package com.facishare.marketing.api.result;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class WebHookSimpleResult implements Serializable {
    @ApiModelProperty("WebHook ID")
    private String id;
    @ApiModelProperty("WebHook名称")
    private String name;
    /** @see com.facishare.marketing.common.enums.MarketingSceneType */
    @ApiModelProperty("应用场景：common通用场景、conference会议营销、live直播营销、marketing_event活动营销")
    private String sceneType;
    /** @see com.facishare.marketing.common.enums.WebHookLifeStatus*/
    @ApiModelProperty("激活状态 ENABLE启用DISABLED停用")
    private String lifeStatus;
    @ApiModelProperty("创建时间")
    private Date createTime;
}
