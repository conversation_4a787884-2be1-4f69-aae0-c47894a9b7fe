package com.facishare.marketing.api.result.wxcoupon;

import com.facishare.marketing.api.vo.wxcoupon.CreateWxCouponVO;
import com.facishare.marketing.common.typehandlers.value.TagName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/8 14:22
 */
@Data
public class CouponResult implements Serializable {

    @ApiModelProperty("微信商家券id")
    private String weChatCouponId;

    @ApiModelProperty("优惠券名称")
    private String stockName; //优惠券名称

    @ApiModelProperty("备注")
    private String comment; //备注

    @ApiModelProperty("优惠券类型")
    private String stockType; //优惠券类型

    @ApiModelProperty("类型")
    private Integer type;

    @ApiModelProperty("适用商品名称")
    private String goodsName; //适用商品名称

    @ApiModelProperty("消费门槛")
    private Double transactionMinimum; //消费门槛

    @ApiModelProperty("优惠金额")
    private Double discountAmount; // 优惠金额

    @ApiModelProperty("微信商家券id")
    private Double discountPercent;//折扣比例

    @ApiModelProperty("单品换购价")
    private Double exchangePrice;//单品换购价

    @ApiModelProperty("使用说明")
    private String description; //使用说明

    @ApiModelProperty("是否隐藏链接 0:隐藏 1:不隐藏")
    private Integer hideLink; //是否隐藏链接 0:隐藏 1:不隐藏

    @ApiModelProperty("是否开启过期提醒  0:开启 1: 不开启")
    private Integer expiredTip; //是否开启过期提醒  0:开启 1: 不开启

    @ApiModelProperty("过期前多少天提醒")
    private Integer expiredDays; // 过期前多少天提醒

    @ApiModelProperty("商户名称")
    private String merchantName; //商户名称

    @ApiModelProperty("商户logo")
    private String merchantLogoUrl; //商户logo

    @ApiModelProperty("背景颜色")
    private String backgroundColor;//背景颜色

    @ApiModelProperty("券详情图片")
    private String couponImageUrl; //券详情图片

    @ApiModelProperty("券code 模式")
    private String couponCodeMode;//券code 模式

    @ApiModelProperty("优惠券状态")
    private Integer status; // 模板状态  0:正常 1:删除

    @ApiModelProperty("优惠券模板id")
    private String templateId;

    @ApiModelProperty("关联市场活动id")
    private String marketingEventId;

    @ApiModelProperty("渠道")
    private String channel;

    @ApiModelProperty("发放数量")
    private Integer maxCoupons;

    @ApiModelProperty("每人领取数量")
    private Integer maxCouponsPerUser;

    @ApiModelProperty("领取限制  0:仅会员 1:所有人")
    private Integer isMember;

    @ApiModelProperty("券可使用开始时间")
    private Long availableBeginTime;

    @ApiModelProperty("券可使用结束时间")
    private Long availableEndTime;

    @ApiModelProperty("生效后N天内有效")
    private Integer availableDayAfterReceive;

    @ApiModelProperty("可用星期数")
    private String weekDay;

    @ApiModelProperty("领取后N天开始生效")
    private Integer waitDaysAfterReceive;

    @ApiModelProperty("当天可用时间段")
    private String availableDayTime;

    @ApiModelProperty("券核销方式")
    private String useMethod;

    @ApiModelProperty("标签列表")
    private List<TagName> tags;

    @ApiModelProperty("小程序或公众号 appId")
    private String appId;

    @ApiModelProperty("请求单号")
    private String outRequestNo;

    @ApiModelProperty("批次id")
    private String stockId;

    @ApiModelProperty("物料id")
    private String objectId;

    @ApiModelProperty("已领取数量")
    private Integer receiveCount;

    @ApiModelProperty("剩余数量")
    private Integer remainCount;

    @ApiModelProperty("已使用数量")
    private Integer usedCont;

    @ApiModelProperty("创建人")
    private Integer operator;

    @ApiModelProperty("更新时间")
    private String updateTime;

    @ApiModelProperty("发送范围")
    private PartnerNoticeVisibilityVO partnerNoticeVisibilityVO;

    @Data
    public static class PartnerNoticeVisibilityVO implements Serializable {
        /** 企业名称列表 **/
        @ApiModelProperty("企业名称列表")
        private List<String> outerTenantIds;
        /** 企业群组id列表  **/
        @ApiModelProperty("企业群组id列表")
        private List<String> outerTenantGroupIds;
    }

    @ApiModelProperty("可售客户发送范围")
    private AccountVisibilityVO accountVisibilityVO;

    @Data
    public static class AccountVisibilityVO implements Serializable {
        @ApiModelProperty("类型")
        private String type;

        @ApiModelProperty("过滤条件")
        private String value;
    }

    private StoreReceiveVisibilityVO storeReceiveVisibilityVO; //门店客户领取范围

    @Data
    public static class StoreReceiveVisibilityVO implements Serializable {
        @ApiModelProperty("类型")
        private String type;

        @ApiModelProperty("过滤条件")
        private String value;
    }

    @ApiModelProperty("单经销商优惠券数量")
    private Integer dealerCount;

    @ApiModelProperty("经销商是否可选择参与 1:可选择参与 2:不可选择")
    private Integer isParticipate;

    @ApiModelProperty("使用场景 1:厂商制券,仅会员使用 2:上下游发券,仅伙伴营销使用 3:厂商制券,经销商发放,门店使用")
    private Integer scene;

    @ApiModelProperty("优惠券类型 0:微信商家券 1:分享券")
    private Integer createCouponType;

    @ApiModelProperty("优惠券方案名称")
    private String name;

    @ApiModelProperty("方案类型")
    private String planType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("开始时间")
    private Long startDate;

    @ApiModelProperty("结束时间")
    private Long endDate;

    @ApiModelProperty("产品条件类型")
    private String productConditionType;

    @ApiModelProperty("产品条件描述")
    private String productConditionContent;

    @ApiModelProperty("满额")
    private String lowerLimit;

    @ApiModelProperty("使用方式")
    private String useType;

    @ApiModelProperty("面额")
    private String amount;

    @ApiModelProperty("下发状态 0:未下发 1:已下发")
    private Integer sendDownStatus;

    @ApiModelProperty("优惠券编号")
    private String couponNo;

    @ApiModelProperty("优惠券所属企业")
    private String ea;

    @ApiModelProperty("市场活动名称")
    private String marketingEventName;

    @ApiModelProperty("所属企业名称")
    private String belongEnterpriseName;

    @ApiModelProperty("发送范围导入结果")
    private String sendScopeImportResult;

    @ApiModelProperty("领取门店范围导入结果")
    private String storeScopeImportResult;

    @ApiModelProperty("发送范围导入结果是否成功")
    private Boolean sendScopeImportSuccess;

    @ApiModelProperty("领取门店范围导入结果是否成功")
    private Boolean storeScopeImportSuccess;
}
