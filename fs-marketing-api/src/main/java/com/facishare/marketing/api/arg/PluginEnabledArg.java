package com.facishare.marketing.api.arg;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import com.facishare.marketing.common.typehandlers.value.ButtonStyle;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
public class PluginEnabledArg extends QYWXBaseArg {
    @ApiModelProperty(name = "id",notes = "文章id")
    private String ea;

    @ApiModelProperty("插件类型")
    private Integer type;

}
