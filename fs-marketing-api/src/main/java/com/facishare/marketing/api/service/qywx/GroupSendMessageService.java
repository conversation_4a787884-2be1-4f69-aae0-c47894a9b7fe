package com.facishare.marketing.api.service.qywx;

import com.facishare.marketing.api.result.qywx.*;
import com.facishare.marketing.api.vo.qywx.ListGroupSendMessageVO;
import com.facishare.marketing.api.vo.qywx.QuerySendGroupMessageDataVO;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;

/**
 * <AUTHOR>
 * @Description
 * @Date 19:15 2020/2/7
 * @ModifyBy
 */
public interface GroupSendMessageService {
    /**
     * 群发消息列表
     * @param vo
     * @return
     */
    Result<PageResult<ListGroupSendMessageResult>> listGroupSendMessage(String ea, Integer fsUserId, ListGroupSendMessageVO vo);

    /**
     * 群发消息预计范围数据统计
     * @param vo
     * @return
     */
    Result<SendMomentCustomerResult> queryGroupMessageSendData(String fsEa, Integer fsUserId, QuerySendGroupMessageDataVO vo);
    Result<SendMomentCustomerResult> asyncQueryGroupMessageSendData(String fsEa, Integer fsUserId, QuerySendGroupMessageDataVO vo);

    /**
     * 群发消息预计范围发送员工数据列表
     * @param vo
     * @return
     */
    Result<PageResult<MomentSendListResult>> queryGroupMessageSendList(String fsEa, QuerySendGroupMessageDataVO vo, Integer pageNum, Integer pageSize);

    /**
     * 群发消息预计范围发送客户列表
     * @param vo
     * @return
     */
    Result<PageResult<SendMomentListResult>> queryGroupMessageSendCustomerList(String fsEa,Integer fsUserId, String userId, QuerySendGroupMessageDataVO vo, Integer pageNum, Integer pageSize);

//    Result<PageResult<MomentSendListResult>> queryGroupMessageSendListV2(String fsEa, QuerySendGroupMessageDataVO vo, Integer pageNum, Integer pageSize);
//    Result<SendMomentCustomerResult> queryGroupMessageSendDataV2(String fsEa, Integer fsUserId, QuerySendGroupMessageDataVO vo);

}
