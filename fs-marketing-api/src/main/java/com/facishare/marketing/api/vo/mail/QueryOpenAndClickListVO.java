package com.facishare.marketing.api.vo.mail;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Created  By zhoux 2020/07/07
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QueryOpenAndClickListVO extends BaseMailVO {

    // 过去 days 天内的统计数据 (days=1表示今天)
    private Integer days;

    // 开始日期, 格式为yyyy-MM-dd
    private String startDate;

    // 结束日期, 格式为yyyy-MM-dd
    private String endDate;

    // 查询该地址在打开点击列表中的详情
    private String email;

    // 1表示打开，2表示点击，不传此参数查询打开和点击全部
    private String trackType;

    // 获取指定API_USER的统计数据，多个API_USER用；分开，如:apiUserList=a;b;c
    private String apiUserList;

    // 获取指定标签下的统计数据
    private String labelId;

    // 查询起始位置, 取值区间 [0-], 默认为 0
    private Integer start;

    // 查询个数, 取值区间 [0-100], 默认为 100
    private Integer limit;

}
