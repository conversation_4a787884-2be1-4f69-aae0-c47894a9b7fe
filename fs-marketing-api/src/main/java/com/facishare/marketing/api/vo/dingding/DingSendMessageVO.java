package com.facishare.marketing.api.vo.dingding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021/11/2 10:50 上午
 */
@Data
public class DingSendMessageVO implements Serializable {
    @ApiModelProperty("模板id")
    String templateId;
    @ApiModelProperty("模板数据")
    Map<String, Object> dataMap;
    @ApiModelProperty("接收用户id列表")
    List<String> userIdList;
    @ApiModelProperty("钉钉企业id")
    String corpId;
}
