package com.facishare.marketing.api.vo.conference;

import java.util.List;

import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by z<PERSON><PERSON> on 2019/7/17.
 */
@Data
public class QueryConferenceListVO implements Serializable{
    private String ea;                //公司帐号
    private Integer fsUserId;         //员工id
//    private Integer flowStatus;           //会议状态 (0全部，1未发布，2 会议未开始 3 会议进行中 4 会议已结束)
    private String keyword;           //搜索会议关键词
    private Integer pageSize;         //当前页码
    private Integer pageNum;          //每页显示数量
    private Boolean spreadSearch;
    /**
     * {@link com.facishare.marketing.common.enums.ActivityStatusEnum}
     */
    private List<Integer> activityStatus; //需要查找的会议状态

    private String marketingEventId; // 市场活动id

    private Boolean needMarketingActivityResult; // 是否需要查询关联营销活动数据

    private Boolean isShowSpread;

    private List<Integer> flowStatusList; //    会议进行状态（未开始，进行中，结束）

    private FilterData filterData; //对象选择器

    // 标签过滤
    private MaterialTagFilterArg materialTagFilter;

    private String menuId;

    private String eventType;
}
