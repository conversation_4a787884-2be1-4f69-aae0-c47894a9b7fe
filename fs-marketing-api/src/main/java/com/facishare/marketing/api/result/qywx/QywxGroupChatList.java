package com.facishare.marketing.api.result.qywx;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/19 10:44
 */
@Data
public class QywxGroupChatList implements Serializable {

    @ApiModelProperty("群id")
    private String chatId;

    @ApiModelProperty("群名称")
    private String chatName;

    @ApiModelProperty("群来源 1:初始绑定 2:自动新建")
    private Integer sourceType;

    @ApiModelProperty("群主名称")
    private String groupOwner;

    @ApiModelProperty("群人数")
    private Integer count;

    @ApiModelProperty("群创建时间")
    private Long createTime;
}
