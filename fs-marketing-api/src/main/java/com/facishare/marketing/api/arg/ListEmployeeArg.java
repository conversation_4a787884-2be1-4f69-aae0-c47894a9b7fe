package com.facishare.marketing.api.arg;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @Auther: dzb
 * @Date: 2018/10/24
 * @Description:
 */
@Data
public class ListEmployeeArg implements Serializable {
    @ApiModelProperty("部门id列表，用于过滤，不传或传递空列表则不过滤")
    private List<Integer> departmentIds;
    @ApiModelProperty(value = "接开通情况", notes = "0,1,2", name = "0:全部 1:已开通 2:未开通")
    private Integer isOpen;
    @ApiModelProperty("员工姓名")
    private String employeeName;
    /** 分页大小 **/
    private Integer pageSize;
    /** 当前页页码 **/
    private Integer pageNum;
}
