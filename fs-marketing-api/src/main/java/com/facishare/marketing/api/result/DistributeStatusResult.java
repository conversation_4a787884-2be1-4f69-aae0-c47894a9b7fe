package com.facishare.marketing.api.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class DistributeStatusResult implements Serializable {
    @ApiModelProperty(value = "开机社会化分销状态", notes = "0,1", name = "0:未开通 1:已开通")
    private Integer status;
    public DistributeStatusResult(Integer status){
        this.status = status;
    }
}
