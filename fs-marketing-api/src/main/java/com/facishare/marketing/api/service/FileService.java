package com.facishare.marketing.api.service;

import com.facishare.marketing.api.arg.GetDownLoadUrlArg;
import com.facishare.marketing.api.arg.GetPreviewUrlArg;
import com.facishare.marketing.api.arg.SendFileMailArg;
import com.facishare.marketing.api.arg.file.BatchGetUrlByPathArg;
import com.facishare.marketing.api.arg.file.UploadFileArg;
import com.facishare.marketing.api.arg.photoLibrary.CPathMetaDataByTcpathArg;
import com.facishare.marketing.api.result.CdnUrlByCpathResult;
import com.facishare.marketing.api.result.FilePreviewResult;
import com.facishare.marketing.api.result.GetDownLoadResult;
import com.facishare.marketing.api.result.file.BatchGetUrlByPathResult;
import com.facishare.marketing.api.result.UploadFileResult;
import com.facishare.marketing.api.result.file.CreateTNFileFromAFileArgResult;
import com.facishare.marketing.api.result.file.GenerateUploadFileOmitResult;
import com.facishare.marketing.api.result.photoLibrary.CPathMetaDataResult;
import com.facishare.marketing.api.vo.GetFileBySpliceUrlVO;
import com.facishare.marketing.common.result.Result;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface FileService {

    Result<Void> ensureKmFolder(String ea, Integer fsUserId);

    byte[] transferUrl(String path);

    Result<UploadFileResult> uploadFile(UploadFileArg arg);

    Result<BatchGetUrlByPathResult> batchGetUrlByPath(BatchGetUrlByPathArg arg);

    /**
     * 根据拼装url重新获取图片地址（终端使用）
     * @param vo
     * @return
     */
    byte[] getFileBySpliceUrl(GetFileBySpliceUrlVO vo);

    byte[] pathDownLoad(String path, String ea);
    
    Result<String> getFileUrlBySpliceUrl(GetFileBySpliceUrlVO vo);

    Result<UploadFileResult> uploadNFileByObject(UploadFileArg arg);

    Result<String> getNapathByApath(String ea, Integer fsUserId, String apath);

    byte[] redirectDownload(String path);

    void batchDownloadPicToZip(String dataListStr, String ea, Integer userId);

    Result<CreateTNFileFromAFileArgResult> createTNFileFromAFile(String ea, Integer fsUserId, String apath);

    Result<String> getCdnUrlBypath(String ea, Integer fsUserId, String path);

    Result<String> getCdnUrlByTcpath(String ea, String tcpath);

    Result<UploadFileResult> uploadToCFile(UploadFileArg uploadFileArg);

    Result<GenerateUploadFileOmitResult> generateUploadFileOmit(String ea, String resourceType, String filename, String extension, int fileSize);

    Result<CPathMetaDataResult> getCPathMetaDataByTcpath(CPathMetaDataByTcpathArg arg);

    Result<CdnUrlByCpathResult> getCurrentEaCpathByCpath(String ea, Integer userId, String path);

    Result<Void> sendFileMail(SendFileMailArg arg);

    Result<FilePreviewResult> getPreviewUrlV2(GetPreviewUrlArg arg);

    Result<GetDownLoadResult> getDownLoadUrlV2(GetDownLoadUrlArg arg);
}
