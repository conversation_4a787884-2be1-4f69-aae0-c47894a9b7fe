package com.facishare.marketing.api.result.open.material;

import com.facishare.marketing.api.result.QueryProductDetailResult;
import com.facishare.marketing.common.typehandlers.value.FieldInfoList;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Value;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "产品列表结果")
public class ProductResult implements Serializable {

    @ApiModelProperty(value = "产品ID")
    private String id;

    @ApiModelProperty(value = "状态 1-已启用 2-已停用 3-未启用")
    private int status;

    @ApiModelProperty(value = "产品名称")
    private String name;

    @ApiModelProperty(value = "产品价格")
    private String price;

    @ApiModelProperty(value = "产品优惠价格")
    private String discountPrice;

    @ApiModelProperty(value = "视频URL")
    private String video;

    @ApiModelProperty(value = "产品简介")
    private String summary;

    @ApiModelProperty(value = "封面图片")
    private List<String> headPics;

    @ApiModelProperty(value = "详情图片")
    private List<String> detailPics;

    @ApiModelProperty(value = "封面图片缩略图")
    private List<String> headPicsThumbs;

    @ApiModelProperty(value = "详情图片缩略图")
    private List<String> detailPicsThumbs;

    @ApiModelProperty(value = "产品试用:true 关闭 false 打开")
    private boolean tryOutEnable;

    @ApiModelProperty(value = "按钮名称")
    private String tryOutButtonValue;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "是否被置顶")
    private boolean top;

    @ApiModelProperty(value = "视频封面地址")
    private String videoCoverUrl;
}
