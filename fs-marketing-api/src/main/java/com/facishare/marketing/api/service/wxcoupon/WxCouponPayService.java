package com.facishare.marketing.api.service.wxcoupon;

import com.facishare.marketing.api.result.wxcoupon.*;
import com.facishare.marketing.api.vo.wxcoupon.*;
import com.facishare.marketing.common.enums.coupon.ParticipateEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;

import java.util.List;

public interface WxCouponPayService {

    /**
     * @description:创建微信商家券(WEB端)
     * @author: ming<PERSON><PERSON>
     * @date: 2021/9/22 15:16
     * @param vo: 入参VO
     * @return: com.facishare.marketing.common.result.Result<com.facishare.marketing.api.result.wxcoupon.CreateCouponResult>
     */
    Result<CreateCouponResult> createWxCouponStock(CreateWxCouponVO vo);

    /**
     * @description:创建上下游伙伴优惠券
     * @author: ming<PERSON><PERSON>
     * @date: 2021/12/6 15:54
     * @param vo: 入参VO
     * @return: com.facishare.marketing.common.result.Result<com.facishare.marketing.api.result.wxcoupon.CreateCouponResult>
     */
    Result<CreateCouponResult> createPartnerCoupon(CreateWxCouponVO vo);

    /**
     * @description:领券回调事件
     * @author: mingqiao
     * @date: 2021/9/22 15:17
     * @param ea: 企业ea
     * @param params: 回调参数
     * @param wechatpaySerial:回调头部 证书
     * @param wechatpaySignature: 回调头部 签名
     * @param wechatpayTimestamp: 回调头部 时间戳
     * @param wechatpayNonce: 回到头部 随机字符串
     * @return: com.facishare.marketing.common.result.Result<java.lang.Void>
     */
    Result<Void> stockCouponsReceiveCallback(String ea,String params, String wechatpaySerial, String wechatpaySignature, String wechatpayTimestamp, String wechatpayNonce);

    /**
     * @description:微信APIV3上传图片
     * @author: mingqiao
     * @date: 2021/9/22 15:19
     * @param ea: 企业ea
     * @param filePath: 图片资源路径
     * @return: com.facishare.marketing.common.result.Result<java.lang.String>
     */
    Result<String> uploadImageV3(String ea,String filePath);

    /**
     * @description:设置领券回调地址
     * @author: mingqiao
     * @date: 2021/9/22 15:19
     * @param notifyUrl: 通知URL
     * @param ea: 企业ea
     * @return: com.facishare.marketing.common.result.Result<com.facishare.marketing.api.result.wxcoupon.CallBackNotifyResult>
     */
    Result<CallBackNotifyResult> configureCallBackUrl(String notifyUrl, String ea);

    /**
     * @description:查询用户券列表(WEB端)
     * @author: mingqiao
     * @date: 2021/9/30 14:50
     * @param vo:
     * @return: com.facishare.marketing.common.result.Result<com.facishare.marketing.common.result.PageResult<com.facishare.marketing.api.result.wxcoupon.UserCouponResult>>
     */
    Result<PageResult<UserCouponListResult>> queryUserCouponList(String ea, QueryUserCouponListVo vo);

    /**
     * @description:查询券列表(WEB端)
     * @author: mingqiao
     * @date: 2021/10/8 17:52
     * @param ea: 企业
     * @param vo: 入参
     * @return: com.facishare.marketing.common.result.Result<com.facishare.marketing.common.result.PageResult<com.facishare.marketing.api.result.wxcoupon.CouponResult>>
     */
    Result<PageResult<CouponResult>> queryCouponList(String ea, QueryCouponListVo vo);


    /**
     * @description:编辑优惠券(WEB端)
     * @author: mingqiao
     * @date: 2021/10/14 17:50
     * @param vo: 入参
     * @param ea: 企业
     * @return: com.facishare.marketing.common.result.Result<java.lang.Void>
     */
    Result<Void> updateWeChatCoupon(UpdateWeChatCouponVo vo, String ea);


    /**
     * @description:查询优惠券详情(WEB端)
     * @author: mingqiao
     * @date: 2021/10/14 17:51
     * @param ea:
     * @param vo:
     * @return: com.facishare.marketing.common.result.Result<com.facishare.marketing.api.result.wxcoupon.CouponResult>
     */
    Result<CouponResult> queryCouponDetail(String ea, QueryCouponDetailVo vo);

    /**
     * @description:查询优惠券详情接口(APP,H5端)
     * @author: mingqiao
     * @date: 2021/10/15 15:49
     */
    Result<CouponResult> queryCouponDetailToCustomer(QueryCouponDetailVo vo);

    /**
     * @description:查询用户券列表(APP,H5端)
     * @author: mingqiao
     * @date: 2021/10/15 15:49
     */
    Result<PageResult<UserCouponListResult>> queryUserCouponListToCustomer(QueryUserCouponListVo vo);

    /**
     * @description:生成H5发券sign(H5端)
     * @param vo
     * @return
     */
    Result<SignResult> getH5Sign(GetH5SignVo vo);

    /**
     * @description:生成微信小程序sign(APP端)
     * @param vo
     * @return
     */
    Result<SignResult> getWxAppSign(GetWXAppSignVo vo);

    /**
     * @description:H5端获取会员配置
     * @param weChatCouponId
     * @return
     */
    Result<String> queryEaByStockId(String weChatCouponId);

    /**
     * @description: 统计优惠券批次相关数据(发放总量, 已领取,已使用)
     * @author: mingqiao
     * @date: 2021/12/21 10:09
     * @param objectId: 物料id
     * @param ea:
     * @return: com.facishare.marketing.common.result.Result<com.facishare.marketing.api.result.wxcoupon.CouponDetailStatisticResult>
     */
    Result<CouponDetailStatisticResult> queryCouponStockStatistic(String objectId,String ea);

    /**
     * @description: 查询会员领取列表
     * @author: mingqiao
     * @date: 2021/12/21 10:09
     * @param objectId: 物料id
     * @param ea:
     * @return: com.facishare.marketing.common.result.Result<com.facishare.marketing.api.result.wxcoupon.MemberReceiveResult>
     */
    Result<PageResult<MemberReceiveResult>> queryMemberReceiveList(String objectId, String ea,Integer pageNo,Integer pageSize);

    /**
     * @description: 查询伙伴领取列表
     * @author: mingqiao
     * @date: 2021/12/21 10:09
     * @param objectId: 物料id
     * @param ea:
     * @return: com.facishare.marketing.common.result.Result<com.facishare.marketing.api.result.wxcoupon.PartnerReceiveResult>
     */
    Result<PageResult<PartnerReceiveResult>> queryPartnerReceiveList(String objectId, String ea, Integer pageNo, Integer pageSize);


    /**
     * @description: APP,H5端查询券列表
     * @author: mingqiao
     * @date: 2021/12/21 20:04
     * @param vo:
     * @return: com.facishare.marketing.common.result.Result<com.facishare.marketing.common.result.PageResult<com.facishare.marketing.api.result.wxcoupon.CouponResult>>
     */
    Result<PageResult<CouponListResult>> getAppCouponList(GetAppCouponListVO vo);

    /**
     * @description: 失效或删除优惠券
     * @author: mingqiao
     * @date: 2021/12/22 15:54
     * @param objectId:
     * @param ea:
     * @param status:
     * @return: com.facishare.marketing.common.result.Result<java.lang.Void>
     */
    Result<Void> updateCouponStatus(String objectId,String ea,Integer status);

    /**
     * @description: 查询优惠券方案详情
     * @author: mingqiao
     * @date: 2022/2/22 10:27
     * @param vo:
     * @param ea:
     * @return: com.facishare.marketing.common.result.Result<com.facishare.marketing.api.result.wxcoupon.CouponPlanInfoResult>
     */
    Result<CouponPlanInfoResult> queryCouponPlanInfo(GetCouponPlanInfoVo vo, String ea);

    /**
     * @description: 下发分享优惠券
     * @author: mingqiao
     * @date: 2022/3/25 14:39
     * @param vo:
     * @return: com.facishare.marketing.common.result.Result<java.lang.Void>
     */
    Result<Void> sendCouponPartner(SendCouponPartnerVo vo);

    /**
     * @description: 领取分享优惠券
     * @author: mingqiao
     * @date: 2022/3/25 14:39
     * @param vo:
     * @return: com.facishare.marketing.common.result.Result<java.lang.Void>
     */
    Result<String> receivePartnerCoupon(ReceivePartnerCouponVo vo);

    /**
     * @description: 下游经销商参与活动
     * @author: mingqiao
     * @date: 2022/3/28 16:42
     * @param vo:
     * @return: com.facishare.marketing.common.result.Result<java.lang.Void>
     */
    Result<Void> participateCouponActivity(ParticipateActivityVo vo);


    /**
     * @description: 纷享券侧滑详情 < 经销商领取列表/>
     * @author: mingqiao
     * @date: 2022/3/31 19:29
     * @param ea: 企业ea
     * @param vo: 入参
     * @return: com.facishare.marketing.common.result.Result<com.facishare.marketing.api.result.wxcoupon.CouponDetailStatisticResult>
     */
    Result<PageResult<PartnerCouponDetailStatisticResult>> statisticDealerCoupon(String ea, PartnerCouponDetailStatisticVo vo);


    /**
     * @description:下游经销商优惠券活动列表
     * @author: mingqiao
     * @date: 2022/4/6 11:18
     * @param vo:
     * @return: com.facishare.marketing.common.result.Result<com.facishare.marketing.common.result.PageResult<com.facishare.marketing.api.result.wxcoupon.CouponActivityResult>>
     */
    Result<PageResult<CouponActivityResult>> queryCouponActivityList(CouponActivityVo vo);


    /**
     * @description:查询优惠券活动详情
     * @author: mingqiao
     * @date: 2022/4/6 15:44
     * @param vo:
     * @return: com.facishare.marketing.common.result.Result<com.facishare.marketing.api.result.wxcoupon.CouponActivityResult>
     */
    Result<CouponActivityResult> queryCouponActivityDetail(CouponDetailActivityVo vo);



    /**
     * @description: 纷享券侧滑详情 < 门店领取列表/>
     * @author: mingqiao
     * @date: 2022/3/31 19:29
     * @param ea: 企业ea
     * @param vo: 入参
     * @return: com.facishare.marketing.common.result.Result<com.facishare.marketing.api.result.wxcoupon.CouponDetailStatisticResult>
     */
    Result<PageResult<PartnerReceiveResult>> statisticStoreCoupon(String ea, PartnerCouponDetailStatisticVo vo);

    Result<String> queryPartnerAppId();

    /**
     * @description: 根据优惠券ID查询优惠券方案列表 <提供订货通使用/>
     * @param vo
     * @return
     */
    Result<List<CouponPlanInfoResult>> queryCouponPlanList(QueryCouponPlanVO vo);

    /**
     * @description: 查询纷享优惠券列表
     * @param vo
     * @return
     */
    Result<PageResult<CouponResult>> queryFxCouponList(QueryFxCouponVO vo);

    /**
     * @description: 查询客户是否有待领取优惠券 <提供给SFA前端调用>
     * @param vo
     * @return
     */
    Result<Boolean> checkCustomerPendingCoupon(CheckCustomerPendingCouponVO vo);

    /**
     * @description: 查询客户待领取优惠券列表 <提供给SFA前端调用>
     * @param vo
     * @return
     */
    Result<PageResult<QueryCustomerCouponResult>> queryCustomerPendingCouponList(QueryCustomerPendingCouponVO vo);

    /**
     * @description: 领取单张优惠券 <提供给SFA后台调用>
     * @param vo
     * @return
     */
    Result<String> receiveSingleCoupon(ReceiveSingleCouponVO vo);

    /**
     * @description: 侧滑经销商领取情况 <单经销商下门店的领取列表>
     * @param vo
     * @return
     */
    Result<PageResult<PartnerReceiveResult>> queryDealerReceiveList(QueryDealerListVo vo);

    Result<Void> exportDealerStatistic(String ea,Integer fsUserId,PartnerCouponDetailStatisticVo vo);

    Result<Void> exportStoreStatistic(String ea, Integer fsUserId, QueryDealerListVo vo);

    /**
     * @description 查询优惠券是否可领取
     * @param vo
     * @return Result<Boolean>
     */
    Result<Boolean> checkCouponCanBeReceived(CheckCouponVO vo);
}
