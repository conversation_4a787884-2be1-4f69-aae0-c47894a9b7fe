package com.facishare.marketing.api.result.kis;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.ToString;
import org.springframework.util.CollectionUtils;

/**
 * @author: zhengliy
 * @date: 2019/3/5
 * @Description: 全员推广
 */
@Data
@ToString(callSuper = true)
public class MarketingActivityNoticeSendVO implements Serializable {
    @ApiModelProperty("通知标题")
    private String title;
    @ApiModelProperty("推广物料")
    private Integer contentType;
    @ApiModelProperty("推广内容")
    private String content;
    @ApiModelProperty("宣传语")
    private String description;
    @ApiModelProperty("发送类型： 1：立即发送 2：定时发送 ")
    private Integer sendType;
    @ApiModelProperty("发送范围")
    private NoticeVisibilityArg noticeVisibilityArg;
    @ApiModelProperty("定时时间")
    private Long timingDate;
    @ApiModelProperty("推广开始时间")
    private Long startTime;
    @ApiModelProperty("推广结束时间")
    private Long endTime;
    @ApiModelProperty("物料相关推广数据")
    private GetMarketingActivityNoticeSendSumUpResult getMarketingActivityNoticeSendSumUpResult;

    @Data
    @ToString
    public static class NoticeVisibilityArg implements Serializable {
        /** 部门列表 **/
        @ApiModelProperty("部门列表")
        private List<Integer> departmentIds;
        /** 用户id列表 **/
        @ApiModelProperty("用户id列表 ")
        private List<Integer> userIds;

        public boolean isInvalidVisibility() {
            return CollectionUtils.isEmpty(departmentIds) && CollectionUtils.isEmpty(userIds);
        }
    }
}
