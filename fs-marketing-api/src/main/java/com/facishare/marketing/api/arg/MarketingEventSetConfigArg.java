package com.facishare.marketing.api.arg;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019-10-23
 */
@Data
public class MarketingEventSetConfigArg implements Serializable {
    private List<EventTypeAndColorData> eventTypeAndColorDataList;

    @Data
    public static class EventTypeAndColorData implements Serializable {
        @ApiModelProperty("市场活动类型")
        private String eventType;
        @ApiModelProperty("颜色")
        private String color;

        public EventTypeAndColorData() {
        }

        public EventTypeAndColorData(String eventType, String color) {
            this.setEventType(eventType);
            this.setColor(color);
        }
    }
}
