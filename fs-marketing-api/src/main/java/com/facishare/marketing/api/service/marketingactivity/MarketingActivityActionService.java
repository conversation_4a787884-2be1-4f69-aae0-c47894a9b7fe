package com.facishare.marketing.api.service.marketingactivity;

import com.facishare.fcp.exception.ValidationException;
import com.facishare.marketing.api.arg.GetMarketingActivityDetailData;
import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg;
import com.facishare.marketing.api.arg.marketingactivity.MarketingActivityPreviewArg;
import com.facishare.marketing.api.arg.marketingactivity.cancelSpreadActivity;
import com.facishare.marketing.api.result.marketingactivity.AddMarketingActivityResult;
import com.facishare.marketing.api.result.marketingactivity.GetMarketingActivityResult;
import com.facishare.marketing.api.result.marketingactivity.MarketingActivityPreviewData;
import com.facishare.marketing.common.result.Result;

/**
 * @author: dongzhb
 * @date: 2019/3/8
 * @Description:
 */
public interface MarketingActivityActionService {
    /**
     * 执行添加
     * @param ea
     * @param fsUserId
     * @param addMarketingActivityArg
     * */
    AddMarketingActivityResult doAddAction(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg);
    /**
     * 执行详情
     * @param ea
     * @param fsUserId
     * @param getMarketingActivityDetailData
     * */
    GetMarketingActivityResult doDetailAction(String ea, Integer fsUserId,  GetMarketingActivityDetailData getMarketingActivityDetailData);

    /**
     * 删除数据
     * @param ea
     * @param fsUserId
     * @param marketingActivityId
     */
    Result<Boolean> doDeleteAction(String ea, Integer fsUserId, String marketingActivityId);

    /**
     * 更新数据
     * @param ea
     * @param fsUserId
     * @param updateMarketingActivityArg
     */
    AddMarketingActivityResult doUpdateAction(String ea, Integer fsUserId, AddMarketingActivityArg updateMarketingActivityArg);

    AddMarketingActivityArg.MarketingActivityAuditData getMarketingActivityAuditData(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg);

    // 获取营销活动预览数据
    Result<MarketingActivityPreviewData> getPreviewData(String ea, Integer fsUserId, MarketingActivityPreviewArg arg);
}
