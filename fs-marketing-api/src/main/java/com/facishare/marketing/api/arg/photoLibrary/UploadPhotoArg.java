package com.facishare.marketing.api.arg.photoLibrary;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

@Data
public class UploadPhotoArg implements Serializable{
    @ApiModelProperty(value = "图片源 1:图片库  2：选择器", required = true)
    private Integer source;

    @ApiModelProperty(value = "图片文件名", required = true)
    private String photoName;

    @ApiModelProperty(value = "图片大小", required = true)
    private Long photoSize;

    @ApiModelProperty(value = "图片npath", required = true)
    private String photoPath;

    @ApiModelProperty(value = "图片后缀名", required = true)
    private String ext;

    public boolean checkParamValid(){
        if (StringUtils.isEmpty(photoName)){
            return false;
        }

        if (photoSize == null){
            return false;
        }

        if (StringUtils.isEmpty(photoPath)){
            return false;
        }

        if (StringUtils.isEmpty(ext)){
            return false;
        }

        return true;
    }
}
