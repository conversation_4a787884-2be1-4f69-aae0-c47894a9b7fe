package com.facishare.marketing.api.arg.sms;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/8/13
 * @Desc 短信营销-变量
 **/
@Data
public class SmsVarArg implements Serializable {
    @ApiModelProperty(value = "变量名")
    private String key;
    @ApiModelProperty(value = "变量值类型 0固定值 1动态参数")
    private String valueType;
    @ApiModelProperty(value = "变量值")
    private String value;
    @ApiModelProperty(value = "是否为url变量")
    private boolean urlVar;
}
