package com.facishare.marketing.api.arg.usermarketingaccount;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class GetMarketingAccountArg implements Serializable {

    @ApiModelProperty(value = "小程序类型 1：微信小程序  2：钉钉小程序")
    private Integer miniappType;

    private String token;

    // 小程序共用

    private String uid;

    private String appId;

    private String sessionKey;

    // 企业微信共用

    private String corpId;

    private String qyUserId;

    // 个人微信小程序专用
    private String openid;

    // 企业微信内部应用专用
    private String agentId;


    private String fsEa;

    private Integer fsEi;

    private Integer fsUserId;

    private String upstreamEa;
    // 钉钉
    private String dingUserId;

    @ApiModelProperty("物料id")
    private String objectId;

    @ApiModelProperty("物料类型")
    private Integer objectType;

    //浏览器访客id
    @ApiModelProperty(value = "浏览器访客id")
    private String browserId;

    //会员id
    @ApiModelProperty(value = "会员id")
    private String memberId;
}

