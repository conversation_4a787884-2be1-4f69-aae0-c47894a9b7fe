package com.facishare.marketing.api.service.live;

import com.facishare.marketing.api.arg.CheckMemberSubmitArg;
import com.facishare.marketing.api.arg.live.*;
import com.facishare.marketing.api.result.CheckMemberSubmitResult;
import com.facishare.marketing.api.result.EnrollDataCountResult;
import com.facishare.marketing.api.result.ExportEnrollsDataResult;
import com.facishare.marketing.api.result.QueryFormUserDataResult;
import com.facishare.marketing.api.result.live.*;
import com.facishare.marketing.api.vo.live.*;
import com.facishare.marketing.common.enums.live.LiveUserActionTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.training.outer.api.message.LiveMqTrainingMessage;

import java.util.List;
import java.util.Map;

/**
 * Created by z<PERSON>gh on 2020/3/19.
 */
public interface LiveService {
    /**
     * 创建直播
     * @param vo
     * @return
     */
    Result<CreateMarketingLiveResult> createLive(CreateLiveVO vo);

    /**
     * 更新直播
     * @param vo
     * @return
     */
    Result<CreateMarketingLiveResult> updateLive(CreateLiveVO vo);

    /**
     * 获取直播列表
     * @param arg
     * @return
     */
    Result<PageResult<ListResult>> list(ListVO arg);

    Result<PageResult<ListResult>> appList(ListVO arg);

    /**
     * 获取直播详情
     * @param id
     * @param role　LiveRoleEnum
     * @return
     */
    Result<GetLiveDetailResult> getDetail(String ea, String id, int role, Integer identityCheckType, Map<String, String> allMemberCookieInfos, String wxAppId, String openId);

    /**
     * 获取父级直播详情
     * @param id
     * @param role　LiveRoleEnum
     * @return
     */
    Result<GetLiveDetailResult> getParentDetail(String ea, String id, int role, Integer identityCheckType, Map<String, String> allMemberCookieInfos, String wxAppId, String openId);

    /**
     * 获取讲师直播地址
     * @param id
     * @param lecturePassword
     * @return
     */
    Result<LiveLectureUrlResult> getLectureUrl(String id, String lecturePassword,Integer type);

    /**
     * 查询直播简报
     * @param ea
     * * @param marketingEventId
     * @return
     */
    Result<LiveBriefStatisticsResult> getLiveBriefStatistics(String ea, String marketingEventId, List<Integer> objectTypes);

    /**
     * 活动线索明细
     * @param vo
     * @return
     */
    Result<PageResult<QueryFormUserDataResult>> getLiveLeadDetail(LiveLeadDetailVO vo);

    /**
     * 查询剩余直播流量
     * @param corpId
     * @return
     */
    Result<Double> queryLeftFlow(Integer corpId);

    /**
     * 获取直播观看地址
     * @param arg
     * @return
     */
    Result<GetViewUrlResult> getViewUrl( GetLiveViewUrlVO arg);


    Result<Boolean> checkUserInMarketingLive(String marketingLiveId,String uid);


    /**
     * 导出直播报名线索
     * @param vo
     * @return
     */
    Result<ExportEnrollsDataResult> exportLiveLead(LiveLeadDetailVO vo);

    /**
     * 处理直播会调数据
     * @param message
     */
    void handlerLiveMQ(LiveMqTrainingMessage message);

    /**
     * 查询直播参与者验证信息
     * @param liveKey
     * @return
     */
    Result<GetLiveViewCheckInfoResult> getViewCheckInfo(String liveKey);

    /**
     *查询直播讲师验证信息
     */
    Result<GetLectrureCheckInfoResult> getLectrureCheckInfo(String liveKey);

    /**
     * 获取直播状态
     * @param id
     * @return
     */
    Result<Integer> getLiveStatus(String id);

    Result<PageResult<QueryLiveEnrollListResult>> queryLiveEnrollList(QueryLiveEnrollListVO vo);
    Result<PageResult<QueryLiveEnrollListResult>> queryLiveEnrollListForSync(QueryLiveEnrollListVO vo);

    Result<ExportEnrollsDataResult> exportLiveEnrollList(QueryLiveEnrollListVO vo);

    /**
     * 查询最新直播数据
     * @param id
     * @param ea
     * @return
     */
    Result<Void> syncLiveStatistics(String id, String ea);

    /**
     * 添加/更新用户状态数据
     * @param liveId
     * @param phones
     * @param actionTypeEnum
     * @return
     */
    Result upsertUserStatus(Integer liveId, List<String> phones, LiveUserActionTypeEnum actionTypeEnum);

    /**
     * 根据直播互动状态查询用户数据
     * @param liveId
     * @param actionTypeEnum
     * @return
     */
    Result<List<String>> queryLiveUserByStatus(Integer liveId, LiveUserActionTypeEnum actionTypeEnum);

    /**
     * 定时同步直播状态
     * @return
     */
    Result<Void> syncLiveByTimer();

    /**
     * 定时设置直播回放
     */
    Result<Void> scheduleSetDefaultRecord();

    /**
     * 查询小鹅通直播列表
     * @param vo
     * @return
     */
    Result<PageResult<XiaoetongLiveListResult>>  xiaoetongLiveList(ListVO vo);

    /**
     * 查询polyv直播列表
     * @param vo
     * @return
     */
    Result<PageResult<PolyvLiveListResult>>  polyvLiveList(ListVO vo);

    /**
     * 获取目睹直播列表
     * @param vo
     * @return
     */
    Result<PageResult<GetMuduEventListResult>> getMuduEventList(ListMuduEventArg vo);

    /**
     * 获取目睹直播详情
     * @param vo
     * @return
     */
    Result<GetMuduEventListResult> getMuduLive(GetMuduLiveArg vo);

    /**
     * 查询微吼直播列表
     * @param vo
     * @return
     */
    Result<PageResult<QueryThirdLiveResult>> queryVHallLive(QueryThirdLiveArg vo);

    /**
     * 获取微吼直播详情
     * @param vo
     * @return
     */
    Result<GetThirdLiveResult> getVHallLive(GetThirdLiveArg vo);

    /**
     * 绑定小鹅通帐号
     * @param vo
     * @return
     */
    Result<Void> bindXiaoketongAccount(BindXiaoetongAccountVO vo);

    /**
     * 获取小鹅通账号
     * @param ea
     * @return
     */
    Result<XiaoetongAccountResult> getXiaoetongAccount(String ea);

    /**
     * 绑定小鹅通帐号
     * @param
     * @return
     */
    Result<Void> bindPolyvAccount(String ea,String appId,String appSecret,String userId);

    Result<PolyvAccountResult> bindAccount(String ea);

    /**
     * 绑定视频号帐号
     * @param vo
     * @return
     */
    Result<Void> bindChannelsAccount(BindChannelsAccountVo vo);

    /**
     * 获取视频号账号
     * @param ea
     * @return
     */
    Result<ChannelsAccountResult> getChannelsAccount(String ea,String id);

    /**
     * 绑定目睹账号
     * @param vo
     * @return
     */
    Result<Void> bindMuduAccount(BindMuduAccountVO vo);

    /**
     * 获取目睹账号
     * @param ea
     * @return
     */
    Result<GetMuduAccountResult> getMuduAccount(String ea);

    /**
     * 绑定第三方平台账号
     * @param vo
     * @return
     */
    Result<Void> bindThirdAccount(BindThirdAccountArg vo);

    /**
     * 获取第三方平台账号
     * @param ea
     * @return
     */
    Result<GetThirdAccountResult> getThirdAccount(GetThirdAccountArg arg);

    /**
     * 根据物料获取视频号账号
     * @param vo
     * @return
     */
    Result<ChannelsAccountResult> getAccountByMaterials(LiveMaterialsVO vo);

    Result<GetLiveDetailResult> getDetailLive(String marketingLiveId);

    /**
     * 根据小鹅通id查询市场活动id
     * @param xiaoetongId
     * @return
     */
    Result<GetMarketingLiveByXiaoetongIdResult> getMarketingLiveByXiaoetongId(String xiaoetongId);

    Result<Map<String,String>> getMarketingLiveByPolyvId(String polyvId);

    Result<Map<String,String>> checkRelate(CheckThirdRelateArg arg);

    /**
     * 用户是否用H5身份报名了直播
     * @param liveId
     * @param fingerPrint
     * @return
     */
    Result<CheckUserHaveSubmitResult> checkH5UserHaveSubmit(String liveId, String fingerPrint, Map<String, String> allEnterpriseMemberCookieMap, boolean checkMemberAndForm);

    /**
     * 用户是否用服务号身份报名了直播
     * @param liveId
     * @param wxAppId
     * @param wxOpenId
     * @return
     */
    Result<CheckUserHaveSubmitResult> checkWxServiceUserHaveSubmit(String liveId, String wxAppId, String wxOpenId);

    /**
     * 检查是用是否报名，并同步报名到用户到小鹅通
     * @param liveId
     * @param phone
     * @param xiaoetongViewUrl
     * @return
     */
    Result<CheckAndSyncUserToXiaoetongResult> checkAndSyncUserToXiaoetong(String liveId, String phone, String xiaoetongViewUrl);

    /**
     * 处理小鹅通消息回调
     * @param appId
     * @param encryptXmlBody
     */
    void xiaoetongMessageCallback(String appId, String encryptXmlBody);

    Result<EnrollDataCountResult> countUnSaveLiveEnrollData(String ea, String marketingEventId);

    /**
     * 小鹅通其他功能跳转到H5中间页后，帐号登录
     * @param appId
     * @param phone
     * @param xiaoetongViewUrl
     * @return
     */
    Result<XiaoetongCommonLoginResult> xiaoetongCommonLogin(String appId, String phone, String xiaoetongViewUrl, String phoneVerifyCode);

    /**
     * 发送小鹅通直播验证码
     * @param phone
     * @param appId
     * @return
     */
    Result sendXiaoetongLoginSms(String phone, String appId);

    /**
     * 检查是用是否报名，并同步报名到用户到小鹅通
     * @param liveId
     * @param phone
     * @param
     * @return
     */
    Result<CheckAndSyncUserToXiaoetongResult> checkPolyvSubmit(String liveId, String phone);

    Map<String, String> externalAuth(String channelId, String userid, Long ts, String token);

    Result<PolyvLiveListResult> getLiveInfo(String ea, String id);
    Result<String> shareXiaoetongAccessToken(Integer tenantId, Integer userId);

    Result<CheckMemberSubmitResult> checkMemberSubmit(CheckMemberSubmitArg arg);

    Result<List<ChannelsAccountResult>> getChannelsAccountByEa(String ea);

    Result<Void> setDefaultChannel(String ea, String id);

    Result<Void> deleteChannel(String ea, String id);
}
