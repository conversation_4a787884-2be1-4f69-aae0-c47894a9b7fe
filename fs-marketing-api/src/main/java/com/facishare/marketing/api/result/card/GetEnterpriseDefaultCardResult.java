package com.facishare.marketing.api.result.card;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @创建人 zhengliy
 * @创建时间 2019/1/15 19:39
 * @描述
 * @IgnoreI18nFile
 */
@Data
public class GetEnterpriseDefaultCardResult implements Serializable {
    @ApiModelProperty(name = "id", value = "预设企业卡片的id")
    String id;
    @ApiModelProperty(name = "companyName", value = "公司名")
    String companyName;
    @ApiModelProperty(name = "companyAddress", value = "公司地址")
    String companyAddress;
    @ApiModelProperty(name = "video", value = "视频信息")
    String video;
    @ApiModelProperty(name = "cardTradeInfo", value = "行业资料")
    CardTradeInfoResult cardTradeInfo;
//    String tradeCode;

    @ApiModelProperty(name = "photos", value = "图片url数组")
    List<EnterpriseDefaultPhotoResult> photos;

    @ApiModelProperty(name = "addressBookDefaultRange", value = "员工信息自动从通讯录获取的信息范围\n " + "avatar:\n" + "name:\n" + "phone:\n" + "vocation:\n" + "email:\n" + "1 为选择")
    String addressBookDefaultRange;
    @ApiModelProperty(name = "videoCover", value = "视频封面")
    String videoCover;
    @ApiModelProperty(value = "名片微页面id")
    private String cardHexagonId;
    @ApiModelProperty(value = "名片微页面id")
    private String cardHexagonName;
    @ApiModelProperty(value = "内容类型")
    private Integer contentType;
}
