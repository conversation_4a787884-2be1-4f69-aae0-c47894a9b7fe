package com.facishare.marketing.api.result.ticket;

import com.facishare.marketing.common.enums.ticket.TicketTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * Created  By zhoux 2019/10/29
 **/
@Data
public class DetermineJumpTypeResult implements Serializable {

    /**
     * {@link TicketTypeEnum}
     */
    @ApiModelProperty("跳转类型 0 自制卡券 1 微信卡券")
    private Integer jumpType;

}
