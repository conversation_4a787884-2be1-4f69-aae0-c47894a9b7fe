package com.facishare.marketing.api.service;

import com.facishare.marketing.api.arg.kis.GetClueContributionListArg;
import com.facishare.marketing.api.arg.kis.GetEmployeeMarketingActivityObjectDetailsArg;
import com.facishare.marketing.api.result.kis.GetClueContributionListResult;
import com.facishare.marketing.api.result.kis.GetEmployeeMarketingActivityObjectDetailsResult;
import com.facishare.marketing.api.result.kis.GetLastestEmployeeStatisticUnitResult;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;

public interface EmployeeSpreadStatisticService {

    Result<PageResult<GetLastestEmployeeStatisticUnitResult>> getLastestEmployeeStatistic(String ea, Integer userId, Integer ei, Integer pageSize, Integer pageNum, Long time);


    Result<PageResult<GetLastestEmployeeStatisticUnitResult>> getLastestEmployeeStatisticForPartner(String outTenantId, int outUserId, String upstreamEa, Integer pageSize, Integer pageNum, Long time);


    Result<GetEmployeeMarketingActivityObjectDetailsResult> getEmployeeMarketingActivityObjectDetails(GetEmployeeMarketingActivityObjectDetailsArg arg, String ea, Integer userId);


    Result<PageResult<GetClueContributionListResult>> getClueContributionList(GetClueContributionListArg arg);

}
