package com.facishare.marketing.api.data.material;

import com.facishare.marketing.api.data.material.AbstractMaterialData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/24.
 */
@Data
public class QRPosterBriefData extends AbstractMaterialData {


    /**
     * 海报背景图缩略图apath
     */
    private String bgThumbnailUrl;

    /**
     * 跳转类型名
     */
    private String forwardName;

    /**
     * 对应内容名(可点击跳转的文字，跳转类型为公众号渠道二维码时为空)
     */
    private String forwardContent;

    /**
     * 市场活动ID
     */
    private String marketingEventId;

    /**
     * 市场活动标题
     */
    private String marketingEventTitle;

    /**
     * 效果图apath
     */
    private String qrPosterApath;

    /**
     * 效果图缩略图apath
     */
    private String qrPosterThumbnailApath;
    
    @ApiModelProperty(value = "海报转发类型 10表示渠道二维码")
    private Integer qrPostForwardType;
}
