package com.facishare.marketing.api.service.hexagon;

import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.hexagon.*;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.FileToHexagonResult;
import com.facishare.marketing.api.result.HexagonQrCodeResult;
import com.facishare.marketing.api.result.OfficialWebsiteWxQrCodeResult;
import com.facishare.marketing.api.result.hexagon.*;
import com.facishare.marketing.api.vo.GetMarketingContentSiteVO;
import com.facishare.marketing.api.vo.SimpleHexagonVO;
import com.facishare.marketing.api.vo.QueryHexagonBackgroudColorVO;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.TagNameList;

import java.util.List;
import java.util.Map;

public interface HexagonService {

    Result<CreateSiteResult> editSite(String ea, Integer userId, CreateSiteArg arg);

    Result<CreateSiteResult> marketingCopySite(String ea,Integer userId,MarketingCopyArg arg, boolean checkName);

    Result<CreateSiteResult> hexagonCopySite(String ea,Integer userId,HexagonCopyArg arg);

    Result deleteSite(String ea, Integer fsUserId, String id);

    Result changeSiteStatus(String ea, Integer fsUserId, String id, Integer status);

    Result<PageResult<GetSiteByEaUnitResult>> getSiteByEa(String ea, Integer userId, Integer pageSize, Integer pageNum, Long time, String searchFitter, Integer statusFitter, Boolean excludeSystemSite);

    Result<GetSiteByEaUnitResult> getSiteById(String ea, Integer userId, String id);

    Result<SitePreviewResult> sitePreview(String ea, SitePreviewArg arg);

    Result<CreatePageResult> editPage(String ea, Integer userId, CreatePageArg arg);

    Result deletePage(String ea, Integer fsUserId, String id);

    Result<List<GetPagesBySiteIdUnitResult>> getPagesBySiteId(String ea, String siteId);

    Result<GetPageDetailResult>  getHomepageDetailBySiteId(HexagonhomepageDetailArg arg);

    Result<GetPageDetailResult> getPageDetail(Integer type, String id);

    Result<CreateTemplateSiteResult> editTemplateEaSite(String ea, Integer userId, CreateTemplateSiteEaArg arg);

    Result deleteTemplateSite(String ea, Integer fsUserId, String id);

    Result changeTemplateSiteStatus(Integer status,String id);

    Result<CreateTemplatePageResult> editTemplatePage(String ea, Integer userId, CreateTemplatePageArg arg);

    Result<PageResult<GetTemplateSiteResult>> getTemplateSite(String ea,Integer category,Integer pageSize,Integer pageNum,Long time,Integer type);

    Result<List<GetPagesByTemplateSiteIdResult>> getPagesByTemplateSiteId(String ea, String siteId);

    Result<HexagonFilePreviewResult> getFilePreviewUrl(String siteId, String npath, String fileName);

    Result<HexagonFilePreviewResult> getFilePreviewUrlByObject(GetFilePreviewUrlByObjectArg arg);

    Result<GetOfficialWebsiteInfoResult> getOfficialWebsiteInfo(String ea, String cardUid);

    Result updateOfficialWebsiteInfo(UpdateOfficialWebsiteInfoArg arg);

    /**
     * 创建&编辑站点分类
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    Result<EditObjectGroupResult> editHexagonGroup(String ea, Integer fsUserId, EditHexagonGroupArg arg);

    /**
     * 删除微页面站点分组
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    Result deleteHexagonGroup(String ea, Integer fsUserId, DeleteHexagonGroupArg arg);

    /**
     * 查询微页面分组
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    Result<ListHexagonTemplateGroupResult> listHexagonGroup(String ea, Integer fsUserId, ListHexagonGroupArg arg);

    /**
     * 查询分组下面的微页面列表
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    Result<PageResult<GetSiteByEaUnitResult>> listHexagonByGroup(String ea, Integer fsUserId, ListHexagonByGroupArg arg);

    /**
     * 设置微页面分组
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    Result setHexagonGroup(String ea, Integer fsUserId, SetHexagonGroupArg arg);

    /**
     * 设置微页面权限
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    Result setHexagonAuthorize(String ea, Integer fsUserId, SetHexagonAuthorizeArg arg);

    /**
     * 微页面站点专属状态设置
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    Result setHexagonSystemStatus(String ea, Integer fsUserId, SetHexagonSystemStatusArg arg);

    /**
     * 删除微页面模板页面
     * @param ea
     * @param fsUserId
     * @param id
     * @return
     */
    Result deleteTemplatePage(String ea, Integer fsUserId, String id);

    Result resetConferenceFormBySite(ResetConferenceFormBySiteArg arg);

    Result<PageResult<GetSiteByEaUnitResult>> getMarketingContentSite(String ea, Integer fsUserId, GetMarketingContentSiteVO vo);

    /**
     * 复制微页面
     */
    Result<String> copyPage(HexagonPageArg pageEntity, String ea, String siteId, Integer userId);

    /**
     * 获取指定物料的关联市场活动
     */
    Result bindMarketing(String ea, Integer userId, Integer objectType, String preObjectId, String newObjectId, String marketingId);


    /**
     * 根据工商查询返回企业信息
     * @param objectId
     * @param objectType
     * @param keyword
     * @return
     */
    Result<List<QueryEnterpriseCommerceInfoResult>> queryEnterpriseCommerceInfo(String objectId, Integer objectType, String keyword);

    /**
     * 根据企业账号查询内容中心信息
     * @param ea
     * @return
     */
    Result<GetContentCenterInfoResult> getContentCenterInfo(String ea, Integer fsUserId, boolean sync);

    /**
     * 根据物料类型查询内容中心信息
     * @param objectId
     * @param objectType
     * @return
     */
    Result<GetContentCenterInfoResult> getContentCenterInfoByObjectId(String objectId, Integer objectType);

    /**
     * 根据企业账号查询活动中心信息
     * @param ea
     * @param fsUserId
     * @param sync
     * @return
     */
    Result<GetActivityCenterInfoResult> getActivityCenterInfo(String ea, Integer fsUserId, boolean sync);

    /**
     * 根据企业账号查询活动中心信息
     * @param objectId
     * @param objectType
     * @return
     */
    Result<GetActivityCenterInfoResult> getActivityCenterInfoByObjectId(String objectId, Integer objectType);

    /**
     * 根据企业账号查询产品推广信息
     * @param ea
     * @return
     */
    Result<GetProductSpreadInfoResult> getProductSpreadInfo(String ea, Integer fsUserId, boolean sync);

    /**
     * 根据物料查询产品推广信息
     * @param objectId
     * @param objectType
     * @return
     */
    Result<GetProductSpreadInfoResult> getProductSpreadInfoByObjectId(String objectId, Integer objectType);

    /**
     * 设置微页面访问标签
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    Result addHexagonVisitTag(String ea, Integer fsUserId, AddHexagonVisitTagArg arg);

    /**
     * 查询微页面设置的访问标签
     * @param ea
     * @param fsUserId
     * @param hexagonSiteId
     * @return
     */
    Result<TagNameList> queryHexagonVisitTag(String ea, Integer fsUserId, String hexagonSiteId);

    /**
     * 获取小程序微站推广配置信息
     * @param ea                企业账户
     * @param fsUserId          员工纷享id
     * @param type              {@link com.facishare.marketing.common.enums.hexagon.MiniAppSiteSpreadInfoEnum}类型：1：产品推广 2：公司动态
     * @param sync
     * @return
     */
    Result<GetMiniAppSiteSpreadInfoResult> getMiniAppSiteSpreadInfo(String ea, Integer fsUserId, Integer type, boolean sync);

    Result<Void> deleteSiteBatch(String ea, Integer fsUserId, DeleteMaterialArg arg);

    Result<Void> topHexagonSite(String ea, Integer fsUserId, TopMaterialArg arg);

    Result<Void> cancelTopHexagonSite(String ea, Integer fsUserId, CancelMaterialTopArg arg);

    Result<Void> addHexagonGroupRole(String ea, Integer fsUserId, SaveObjectGroupVisibleArg arg);

    Result<Void> setHexagonGroupBatch(String ea, Integer fsUserId, SetObjectGroupArg arg);

    Result<List<String>> getGroupRole(String groupId);

    Result<HexagonQrCodeResult> createHexagonQrCode(CreateHexagonWxQrCodeArg arg);

    /*更新微页面外部展示名称*/
    Result updateSiteOutDisplayName(String ea, Integer fsUserId, UpdateSiteOutDisplayNameArg arg);

    Result<FileToHexagonResult> fileToHexagon(FileToHexagonArg arg);

    void updateFileToHexagonStatus(String siteId, int status, String failReason,List<FileToHexagonDataArg> argList);

    Result<String> genMktParam(String mktParam);

    Result<PageResult<SimpleHexagonVO>> simpleHexagonList(SimpleHexagonListArg arg);

    Result<List<String>> queryHexagonBackgroundColorSetting(QueryHexagonBackgroudColorVO vo);

    Result<List<String>> updateHexagonBackgroundColorSetting(String ea, UpdateHexagonBackgroudColorArg arg);
}
