package com.facishare.marketing.api.service.crowd;

import com.facishare.marketing.api.arg.crowd.OperateTimedTaskArg;
import com.facishare.marketing.api.arg.contentmarketing.ListContentMarketingEventArg;
import com.facishare.marketing.api.arg.crowd.MarketingCrowdPlanArg;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.api.result.crowd.MarketingCrowdPlanDetailResult;
import com.facishare.marketing.api.result.crowd.MarketingCrowdPlanStatisticsResult;
import com.facishare.marketing.common.result.Result;

public interface TargetCrowdOperationService {

    void handleTimedTask(String taskId);
    Result saveMarketingCrowdPlan(String ea, Integer fsUserId, MarketingCrowdPlanArg arg);
    Result<PageResult<MarketingEventsBriefResult>> listCrowdMarketingEvent(String ea, Integer fsUserId, ListContentMarketingEventArg arg);
    Result<MarketingCrowdPlanDetailResult> getDetail(String ea, Integer fsUserId, String marketingEventId);
    Result<MarketingCrowdPlanStatisticsResult> getStatistics(String ea, Integer fsUserId, String marketingEventId);

    Result<Void> operateTimedTask(String ea, Integer fsUserId, OperateTimedTaskArg arg);
}
