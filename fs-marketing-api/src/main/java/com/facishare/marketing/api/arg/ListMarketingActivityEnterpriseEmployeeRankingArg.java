package com.facishare.marketing.api.arg;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.ToString;

/**
 * @author: dongzhb
 * @date: 2019/3/12
 * @Description:
 */
@Data
@ToString
public class ListMarketingActivityEnterpriseEmployeeRankingArg implements Serializable {
    private static final long serialVersionUID = 8777232954796410452L;
    @ApiModelProperty(value = "最近n天(必填)", allowableValues = "range[1, 30]")
    private Integer recentDay;
    @ApiModelProperty(value = "4->推广次数   5->CRM线索数 2->访问人次", allowableValues = "2,4,5")
    private Integer searchType;
    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;
    @ApiModelProperty(value = "当前页页码")
    private Integer pageNum;

}
