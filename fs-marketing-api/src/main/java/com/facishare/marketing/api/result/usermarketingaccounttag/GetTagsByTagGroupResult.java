package com.facishare.marketing.api.result.usermarketingaccounttag;

import com.facishare.marketing.api.data.usermarketingaccounttag.TagData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class GetTagsByTagGroupResult implements Serializable {
    @ApiModelProperty("营销用户id")
    private String userMarketingAccountId;
    @ApiModelProperty("营销用户拥有的标签")
    private List<TagData> tags;
}
