package com.facishare.marketing.api.arg;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class BasePageArg implements Serializable {

    @ApiModelProperty(value = "当前页码", required = true)
    private Integer pageNum;
    @ApiModelProperty(value = "每页数量", required = true)
    private Integer pageSize;
    @ApiModelProperty(value = "第一页请求时间戳")
    private Long time;

    public boolean isPageArgValid() {
        return pageNum != null && pageSize !=null;
    }
}
