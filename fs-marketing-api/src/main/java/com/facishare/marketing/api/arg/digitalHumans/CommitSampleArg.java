package com.facishare.marketing.api.arg.digitalHumans;

import com.facishare.marketing.api.arg.qywx.BasePageArg;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
@Data
public class CommitSampleArg extends BasePageArg {

//    vhModelType string true 播报：STUDIO 交互： INTERACTIVE
//    trainingVideoUrl string true 训练视频（长度建议40s，前30秒为闭合嘴唇 30～40秒⼤声讲话，长度限制为35-45s）
//    audioText string true 声音样本的文本，用户录制最后10秒音频时读的文本内容
//    authVideoUrl string true 授权视频地址

    private String ea;

    private Integer userId;

    private String name;

    private String vhModelType = "INTERACTIVE";

    private String trainingVideoUrl;

    private String audioText;

    private String authVideoUrl;

    public boolean checkParam() {
        return StringUtils.isNotBlank(vhModelType) && StringUtils.isNotBlank(trainingVideoUrl) && StringUtils.isNotBlank(audioText) && StringUtils.isNotBlank(authVideoUrl);
    }

}
