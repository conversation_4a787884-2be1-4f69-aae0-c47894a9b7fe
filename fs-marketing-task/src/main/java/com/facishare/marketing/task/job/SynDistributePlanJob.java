/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.service.distribution.DistributionPlanService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2021/05/19
 **/
@JobHander(value = "SynDistributePlanJob")
@Service
@Slf4j
public class SynDistributePlanJob extends IJobHandler {

    @Autowired
    private DistributionPlanService distributionPlanService;

    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        try {
            distributionPlanService.synDistributePlan();
        } catch (Exception e) {
            log.error("SynDistributePlanJob定时任务调用异常调用异常，error:{}", e);
            throw e;
        }
        return new ReturnT(200, "SynDistributePlanJob定时任务调用成功");
    }
}
