/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.result.MarketingUserGroupSimpleResult;
import com.facishare.marketing.api.service.MarketingUserGroupService;
import java.util.List;

import com.facishare.marketing.common.enums.MarketingGroupUserActionTriggerTypeEnum;
import com.facishare.marketing.task.interceptor.UserInfoKeeper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/2/12.
 */
@JobHander(value = "CalculateUserMarketingAccountRuleJob")
@Service
@Slf4j
public class CalculateUserMarketingAccountRuleJob extends IJobHandler {
    @Value("${host}")
    private String host;
    @Autowired
    private MarketingUserGroupService marketingUserGroupService;

    @Override
    public ReturnT execute(TriggerParam params) throws Exception {
        try {
            log.info("start CalculateUserMarketingAccountRuleJob");
            handleCalculate();
            return new ReturnT(200, "定时任务调用成功");
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
    }

 //   @Scheduled(cron = "${calculateUserMarketingAccountRule.cron.expression}")
    private void handleCalculate() {
        int limit = 100;
        int offset = 0;
        for (int i = 0; i < 1000; i++) {
            List<MarketingUserGroupSimpleResult> marketingUserGroupResultList = marketingUserGroupService.listAllMarketingUserGroupResult(offset, limit).getData();
            offset += limit;
            for (MarketingUserGroupSimpleResult marketingUserGroupSimpleResult : marketingUserGroupResultList) {
                try {
                    String ea = marketingUserGroupSimpleResult.getEa();
                    UserInfoKeeper.setEa(ea);
                    marketingUserGroupService.calculateUserMarketingAccountData(ea, -10000, marketingUserGroupSimpleResult.getId());
                } catch (Exception ex) {
                    log.error("handleCalculate , Exception : ", ex);
                }
            }
            if (marketingUserGroupResultList.size() < limit) {
                break;
            }
        }
    }
}
