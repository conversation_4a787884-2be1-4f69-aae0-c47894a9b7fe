package com.facishare.marketing.web.controller.qywx;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg;
import com.facishare.marketing.api.arg.CancelMaterialTopArg;
import com.facishare.marketing.api.arg.DeleteMaterialArg;
import com.facishare.marketing.api.arg.TopMaterialArg;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.qywx.QywxGroupChatList;
import com.facishare.marketing.api.result.qywx.QywxGroupCodeDetailResult;
import com.facishare.marketing.api.result.qywx.QywxGroupCodeResult;
import com.facishare.marketing.api.service.qywx.QywxGroupCodeService;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.api.vo.qywx.QywxGroupCodeListVO;
import com.facishare.marketing.api.vo.qywx.QywxGroupCodeVO;
import com.facishare.marketing.api.vo.qywx.UpdateQywxGroupCodeVO;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.arg.IdArg;
import com.facishare.marketing.web.arg.qywx.CreateQywxGroupCodeArg;
import com.facishare.marketing.web.arg.qywx.QueryQywxGroupCodeArg;
import com.facishare.marketing.web.arg.qywx.UpdateQywxGroupCodeArg;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/13 10:27
 * @description 企业微信群活码
 */
@RestController
@RequestMapping("qywxGroupCode")
@Slf4j
@Api(value = "企业微信群活码")
public class QywxGroupCodeController {

    @Autowired
    private QywxGroupCodeService groupCodeService;

    @ApiOperation(value = "新建企微群活码")
    @RequestMapping(value = "/saveQywxGroupCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> saveQywxGroupCode(@RequestBody CreateQywxGroupCodeArg arg){
        if (CollectionUtils.isEmpty(arg.getChatIdList())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QywxGroupCodeVO vo = BeanUtil.copy(arg,QywxGroupCodeVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        return groupCodeService.saveQywxGroupCode(vo);
    }

    @ApiOperation(value = "查询企微群活码列表")
    @RequestMapping(value = "/queryQywxGroupCodePage", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<PageResult<QywxGroupCodeResult>> queryQywxGroupCodePage(@RequestBody QueryQywxGroupCodeArg arg){
        if (arg.getPageNum() == null || arg.getPageSize() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QywxGroupCodeListVO vo = BeanUtil.copy(arg,QywxGroupCodeListVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        return groupCodeService.queryPage(vo);
    }

    @ApiOperation(value = "查询企微群活码详情")
    @RequestMapping(value = "/queryQywxGroupCodeDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<QywxGroupCodeDetailResult> queryQywxGroupCodeDetail(@RequestBody IdArg arg){
        if (StringUtils.isBlank(arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return groupCodeService.queryCodeDetail(arg.getId());
    }

    @ApiOperation(value = "失效企微群活码")
    @RequestMapping(value = "/updateContactGroupCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> updateContactGroupCode(@RequestBody IdArg arg){
        if (StringUtils.isBlank(arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return groupCodeService.updateContactGroupCode(arg.getId());
    }


    @ApiOperation(value = "批量删除企微群活码")
    @RequestMapping(value = "/deleteQywxGroupCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> deleteQywxGroupCode(@RequestBody DeleteMaterialArg arg){
        if (CollectionUtils.isEmpty(arg.getIdList())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return groupCodeService.deleteQywxGroupCode(UserInfoKeeper.getEa(), arg.getIdList());
    }

    @ApiOperation(value = "编辑企微群活码")
    @RequestMapping(value = "/updateQywxGroupCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> updateQywxGroupCode(@RequestBody UpdateQywxGroupCodeArg arg){
        if (StringUtils.isBlank(arg.getId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        UpdateQywxGroupCodeVO vo = BeanUtil.copy(arg,UpdateQywxGroupCodeVO.class);
        return groupCodeService.updateQywxGroupCode(vo);
    }


    @ApiOperation(value = "创建&编辑企微群活码分组")
    @RequestMapping(value = "/editQywxGroupCodeGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<EditObjectGroupResult> editQywxGroupCodeGroup(@RequestBody EditObjectGroupArg arg) {
        if (!arg.checkParamValid()){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return groupCodeService.editQywxGroupCodeGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "删除企微群活码分组")
    @RequestMapping(value = "/deleteQywxGroupCodeGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> deleteQywxGroupCodeGroup(@RequestBody DeleteObjectGroupArg arg) {
        if (!arg.checkParamValid()){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return groupCodeService.deleteQywxGroupCodeGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "企微群活码设置分组")
    @RequestMapping(value = "/setQywxGroupCodeGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> setQywxGroupCodeGroup(@RequestBody SetObjectGroupArg arg){
        if (org.apache.commons.lang3.StringUtils.isEmpty(arg.getGroupId()) || CollectionUtils.isEmpty(arg.getObjectIdList())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return groupCodeService.setQywxGroupCodeGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "置顶企微群活码")
    @RequestMapping(value = "/topQywxGroupCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> topQywxGroupCode(@RequestBody TopMaterialArg arg){
        if (StringUtils.isBlank(arg.getObjectId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return groupCodeService.topQywxGroupCode(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "取消置顶企微群活码")
    @RequestMapping(value = "/cancelTopQywxGroupCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> cancelTopQywxGroupCode(@RequestBody CancelMaterialTopArg arg){
        if (StringUtils.isBlank(arg.getObjectId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return groupCodeService.cancelTopQywxGroupCode(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "企微群活码分组增加适用角色")
    @RequestMapping(value = "/addQywxGroupCodeGroupRole", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> addQywxGroupCodeGroupRole(@RequestBody SaveObjectGroupVisibleArg arg){
        if (StringUtils.isBlank(arg.getGroupId()) || CollectionUtils.isEmpty(arg.getRoleIdList())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return groupCodeService.addQywxGroupCodeGroupRole(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "获取企微群活码分组列表")
    @RequestMapping(value = "/listQywxGroupCodeGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<ObjectGroupListResult> listQywxGroupCodeGroup(@RequestBody ListGroupArg arg){
        return groupCodeService.listQywxGroupCodeGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "获取企微群活码分组适用角色")
    @RequestMapping(value = "/getQywxCodeGroupRole", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<List<String>> getGroupRole(@RequestBody JSONObject arg){
//        @ApiParam("分组ID") @RequestParam("groupId")
        String groupId = arg.getString("groupId");
        return groupCodeService.getQywxCodeGroupRole(groupId);
    }

    @ApiOperation(value = "查询群活码群列表信息")
    @RequestMapping(value = "/queryChatListInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<List<QywxGroupChatList>> queryChatListInfo(@RequestBody IdArg arg){
        return groupCodeService.queryChatListInfo(UserInfoKeeper.getEa(),arg.getId());
    }
}
