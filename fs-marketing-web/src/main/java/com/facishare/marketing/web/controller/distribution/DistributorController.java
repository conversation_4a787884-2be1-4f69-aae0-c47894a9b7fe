package com.facishare.marketing.web.controller.distribution;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.arg.distribution.QueryDistributorResultArg;
import com.facishare.marketing.api.arg.distribution.UpdateOperatorArg;
import com.facishare.marketing.api.result.distribution.DistributorResult;
import com.facishare.marketing.api.result.distribution.QueryDistributorGradeResult;
import com.facishare.marketing.api.service.distribution.DistributorService;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.api.vo.ConfirmDistributorVO;
import com.facishare.marketing.api.vo.QueryDistributorGradeVO;
import com.facishare.marketing.common.enums.ExcelConfigEnum;
import com.facishare.marketing.common.enums.distribution.DistributorStatusEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.arg.distribution.ConfirmDistributorArg;
import com.facishare.marketing.web.arg.distribution.QueryDistributorGradeByOrderArg;
import com.facishare.marketing.web.enums.CheckIndentityAuthScopeEnum;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;

@Api(description = "分销人员相关接口")
@RestController
@RequestMapping("distributor")
@Slf4j
public class DistributorController {
    @Autowired
    private DistributorService distributorService;

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @ApiOperation(value = "查询分销人员数据", httpMethod = "POST")
    @RequestMapping(value = "queryDistributorResult", method = RequestMethod.POST)
    public Result<PageResult<DistributorResult>> queryDistributorResult(@RequestBody com.facishare.marketing.web.arg.distribution.QueryDistributorResultArg arg) {
        QueryDistributorResultArg vo = BeanUtil.copy(arg, QueryDistributorResultArg.class);
        vo.setStartDate(arg.getStartDate() == null ? null : arg.getStartDate());
        vo.setEndDate(arg.getEndDate() == null ? null : arg.getEndDate());
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setIsAppAdmin(UserInfoKeeper.getIsAppAdmin());
        vo.setIsOperator(UserInfoKeeper.getIsOperator());
        return distributorService.queryDistributorResult(vo);
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @ApiOperation(value = "更换运营人员", httpMethod = "POST")
    @RequestMapping(value = "updateOperator", method = RequestMethod.POST)
    public Result updateOperator(@RequestBody com.facishare.marketing.web.arg.distribution.UpdateOperatorArg arg) {
        if (!UserInfoKeeper.getIsAppAdmin()) {
            return new Result(SHErrorCode.SUCCESS, I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORCONTROLLER_66));
        }
        UpdateOperatorArg vo = BeanUtil.copy(arg, UpdateOperatorArg.class);
        vo.setUpdateOperators(BeanUtil.copy(arg.getUpdateOperators(), UpdateOperatorArg.UpdateOperator.class));
        return distributorService.updateOperator(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @RequestMapping(value = "/exportQueryDistributorResult", method = RequestMethod.POST)
    @ApiOperation(value = "导出分销人员数据", notes = "导出分销人员数据")
    public void exportQueryDistributorResult(com.facishare.marketing.web.arg.distribution.QueryDistributorResultArg arg, HttpServletResponse httpServletResponse) throws Exception {
        QueryDistributorResultArg vo = BeanUtil.copy(arg, QueryDistributorResultArg.class);
        vo.setPageNum(0);
        vo.setPageSize(100000);
        vo.setStartDate(arg.getStartDate() == null ? null : arg.getStartDate());
        vo.setEndDate(arg.getEndDate() == null ? null : arg.getEndDate());
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setIsAppAdmin(UserInfoKeeper.getIsAppAdmin());
        vo.setIsOperator(UserInfoKeeper.getIsOperator());
        distributorService.exportQueryDistributorResult(vo);
        //Long startTime = arg.getStartDate();
//        Long startTime = new Date().getTime();
//        Long endTime = arg.getEndDate();
//        StringBuilder title = new StringBuilder("分销人员");
//        String filename = generateQueryDistributorResultFilename(startTime, endTime, title).toString();
//        Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
//        excelConfigMap.put(ExcelConfigEnum.FILE_NAME, filename);
//        excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "分销人员");
//        excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
//        XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
//        XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
//        xssfSheet.setDefaultColumnWidth(20);
//        List<String> titleList = generateExcelTitleList();
//        List<List<Object>> distributorResultList = generateExcelDatasList(distributorResult.getData().getResult());
//        ExcelUtil.fillContent(xssfSheet, titleList, distributorResultList);
//        filename = URLEncoder.encode(filename, "UTF-8");
//        httpServletResponse.setContentType(excelConfigMap.get(ExcelConfigEnum.CONTENT_TYPE).toString());
//        httpServletResponse.setHeader("Content-disposition", "attachment; filename=" + filename);
//        OutputStream bos = httpServletResponse.getOutputStream();
//        xssfWorkbook.write(bos);
//        httpServletResponse.flushBuffer();
//        bos.flush();
//        bos.close();
    }

    private List<List<Object>> generateExcelDatasList(List<DistributorResult> distributorResult) {
        List<List<Object>> datasList = new ArrayList<>();
        distributorResult.forEach(value -> {
            List<Object> objList = new ArrayList<>();
            objList.add(value.getDistributorName());
            objList.add(value.getName());
            objList.add(value.getPhone());
            objList.add(value.getGrade());
            objList.add(value.getGradeName());
            objList.add(value.getCountClue());
            objList.add(String.format("%.2f", value.getReward()));
            objList.add(value.getRecruitCount());
            objList.add(value.getRecruitName());
            objList.add(value.getOperator());
            objList.add(getDistributorStatusName(value.getStatus()));
            objList.add(value.getRefuseDesc());
            objList.add(value.getCreateTime());
            datasList.add(objList);
        });
        return datasList;
    }
    private String getDistributorStatusName(int status){
        String resultStr = "";
        if (status == DistributorStatusEnum.UNCONFIRMED.getType()) {
            resultStr = I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_259);
        } else if (status == DistributorStatusEnum.TO_BE_PROCESSED.getType()) {
            resultStr = I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_261);
        } else {
            resultStr = I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_263);
        }
        return resultStr;
    }

    private List<String> generateExcelTitleList() {
        List<String> titleList = new ArrayList<>();
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_70));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_708));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_594));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_273));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_274));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_275));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_276));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_277));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_278));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_279));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1078));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_281));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_282));
        return titleList;
    }

    public StringBuilder generateQueryDistributorResultFilename(Long startTime, Long endTime, StringBuilder title) {
        String startTimeFormat = DateUtil.dateMillis2String(startTime, I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_229));
        //String endTimeFormat = DateUtil.dateMillis2String(endTime, "yyyy年MM月dd日");
        return new StringBuilder(startTimeFormat).append(title).append(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_233) + ".xlsx");
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @ApiOperation(value = "审核分销人员", httpMethod = "POST")
    @RequestMapping(value = "confirmDistributor", method = RequestMethod.POST)
    public Result confirmDistributor(@RequestBody ConfirmDistributorArg arg) {
        if (arg == null || arg.isWrongParam()) {
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        ConfirmDistributorVO vo = BeanUtil.copy(arg, ConfirmDistributorVO.class);
        vo.setFsEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());

        return distributorService.confirmDistributor(vo);
    }

    @ApiOperation(value = "通过销售订单查询分销人员等级", httpMethod = "POST")
    @RequestMapping(value = "queryDistributorGradeByOrder", method = RequestMethod.POST, produces = "application/json")
    public Result<QueryDistributorGradeResult> queryDistributorGradeByOrder(@RequestBody QueryDistributorGradeByOrderArg arg) {
        if (arg == null) {
            return new Result(SHErrorCode.PARAMS_ERROR);
        }

        QueryDistributorGradeVO vo = BeanUtil.copy(arg, QueryDistributorGradeVO.class);
        return distributorService.queryDistributorGrade(vo);
    }
}