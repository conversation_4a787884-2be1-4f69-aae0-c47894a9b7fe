package com.facishare.marketing.web.arg;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.ToString;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2018/8/21.
 */
@Data
@ToString(callSuper = true)
public class ListCrmObjectRecordTypesArg implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "对象apiName", required = true)
    public String objectApiName;
}
