package com.facishare.marketing.web.vo;

import com.facishare.marketing.api.vo.WeChatTemplateMessageData;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.ToString;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/2/25.
 */
@Data
@ToString
public class ExternalConfigVO implements Serializable {
    @ApiModelProperty("微信营销")
    private WeChatServiceMarketingActivityVO weChatServiceMarketingActivityVO;
    @ApiModelProperty("全员营销")
    private MarketingActivityNoticeSendVO marketingActivityNoticeSendVO;
    @ApiModelProperty("短信营销")
    private MarketingActivityGroupSenderVO marketingActivityGroupSenderVO;
    @ApiModelProperty("模版消息")
    private WeChatTemplateMessageData weChatTemplateMessageVO;
}