package com.facishare.marketing.web.arg.officialWebsite;

import com.facishare.marketing.common.enums.officialWebsite.OfficialWebsiteStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Arrays;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2019/11/28
 **/
@Data
public class UpdateWebsiteDataArg implements Serializable {

    @ApiModelProperty("数据id")
    private String id;

    @ApiModelProperty("官网名称")
    private String websiteName;

    @ApiModelProperty("官网url")
    private String websiteUrl;

    @ApiModelProperty("状态 0 正常 1停用 2删除")
    private Integer status;

    public boolean isWrongParam() {
        return StringUtils.isBlank(id) || Arrays.stream(OfficialWebsiteStatusEnum.values()).noneMatch(data -> data.getValue().equals(status));
    }

}
