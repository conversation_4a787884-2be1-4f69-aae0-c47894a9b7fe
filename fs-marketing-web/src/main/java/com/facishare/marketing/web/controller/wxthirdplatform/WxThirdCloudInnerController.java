package com.facishare.marketing.web.controller.wxthirdplatform;

import com.facishare.marketing.api.arg.CloudSmsReportArg;
import com.facishare.marketing.api.arg.GetQywxSuiteTokenArg;
import com.facishare.marketing.api.arg.QueryNoticeListArg;
import com.facishare.marketing.api.arg.SendSystemNoticeArg;
import com.facishare.marketing.api.result.WxThirdComponentResult;
import com.facishare.marketing.api.result.WxThirdPlatformDomainResult;
import com.facishare.marketing.api.result.WxThirdPlatformVersionResult;
import com.facishare.marketing.api.result.system.SystemNoticeCloudListResult;
import com.facishare.marketing.api.result.system.SystemNoticeResult;
import com.facishare.marketing.api.service.wxcallback.QywxSelfBuildAppCallbackService;
import com.facishare.marketing.api.service.wxthirdplatform.WxThirdCloudInnerService;
import com.facishare.marketing.common.result.Result;
import com.google.common.base.Preconditions;
import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 专属云与纷享云共享信息接口
 */
@Slf4j
@RestController
@RequestMapping("/wxThirdCloudInner")
public class WxThirdCloudInnerController {
    @Autowired
    private WxThirdCloudInnerService wxThirdCloudInnerService;
    @Autowired
    private QywxSelfBuildAppCallbackService qywxSelfBuildAppCallbackService;

    // ============= 平台级 =============

    @GetMapping("getComponentAccessTokenAndAppIdByPlatformId")
    public Result<WxThirdComponentResult> getComponentAccessTokenAndAppIdByPlatformId(@RequestParam("fsPlatformId") String fsPlatformId, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        return wxThirdCloudInnerService.getComponentAccessTokenAndAppIdByPlatformId(fsPlatformId);
    }

    @GetMapping("getComponentAccessTokenByPlatformId")
    public Result<String> getComponentAccessTokenByPlatformId(@RequestParam("fsPlatformId") String fsPlatformId, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        return wxThirdCloudInnerService.getComponentAccessTokenByPlatformId(fsPlatformId);
    }

    @GetMapping("getComponentAppIdByPlatformId")
    public Result<String> getComponentAppIdByPlatformId(@RequestParam("fsPlatformId") String fsPlatformId, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        return wxThirdCloudInnerService.getComponentAppIdByPlatformId(fsPlatformId);
    }

    @GetMapping("getComponentAccessTokenByAppId")
    public Result<String> getComponentAccessTokenByAppId(@RequestParam("appId") String appId, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        return wxThirdCloudInnerService.getComponentAccessTokenByAppId(appId);
    }

    @GetMapping("getDomainByPlatformId")
    public Result<WxThirdPlatformDomainResult> getDomainByPlatformId(@RequestParam("fsPlatformId") String fsPlatformId, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        return wxThirdCloudInnerService.getDomainByPlatformId(fsPlatformId);
    }

    @GetMapping("getShowVersionByPlatformId")
    public Result<WxThirdPlatformVersionResult> getShowVersionByPlatformId(@RequestParam("fsPlatformId") String fsPlatformId, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        return wxThirdCloudInnerService.getShowVersionByPlatformId(fsPlatformId);
    }
    @ResponseBody
    @RequestMapping(value = "/handleQywxAppAgentCallback", method = {RequestMethod.POST, RequestMethod.GET})
    public String handleQywxAppAgentCallback( @RequestParam("suitId") String suitId,
                                              @RequestParam("autoCode") String autoCode,
                                              @RequestParam("corpId") String corpId,
                                              @RequestParam("agentId") String agentId,
                                              @RequestParam("msg_signature") String msgSignature,
                                              @RequestParam("timestamp") String timestamp,
                                              @RequestParam("nonce") String nonce,
                                              @RequestParam(required = false) String encryptXmlBody,
                                              @RequestParam("ea") String ea) {
        return qywxSelfBuildAppCallbackService.handleQywxAppAgentCallback(encryptXmlBody, timestamp, nonce, msgSignature,
                 autoCode, corpId, agentId, suitId, ea).getData();
    }

    //接受云信息保存corpid和ea关系
    @ResponseBody
    @RequestMapping(value = "/saveQYWXCorpIdAndEa", method = {RequestMethod.POST, RequestMethod.GET})
    public String saveQYWXCorpIdAndEa( @RequestParam("id") String id,
                                              @RequestParam("corpId") String corpId,
                                              @RequestParam("ea") String ea,
                                              @RequestParam("host") String host){
        return qywxSelfBuildAppCallbackService.saveQYWXCorpIdAndEa(ea, host).getData();
    }


    // ============= 小程序级 =============

    @GetMapping("getEaByAppId")
    public Result<String> getEaByAppId(@RequestParam("appId") String appId, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        return wxThirdCloudInnerService.getEaByAppId(appId);
    }

    @PostMapping("bindAppIdAndEa")
    public Result<Boolean> bindAppIdAndEa(@RequestParam String appId, @RequestParam String ea, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        return wxThirdCloudInnerService.bindAppIdAndEa(appId, ea);
    }

    @PostMapping("unbindAppIdAndEa")
    public Result<Boolean> unbindAppIdAndEa(@RequestParam String appId, @RequestParam String ea, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        return wxThirdCloudInnerService.unbindAppIdAndEa(appId, ea);
    }

    private void onlyPermitInnerVisit(HttpServletRequest request){
        Preconditions.checkArgument(request.getRequestURI().contains("/inner/"));
    }

    @GetMapping("getAccessByMankeeppro")
    public Result<String> getAccessByMankeeppro(@RequestParam("mankeepproAppId") String mankeepproAppId, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        return wxThirdCloudInnerService.getAccessByMankeeppro(mankeepproAppId);
    }

    @RequestMapping(value = "getSystemNoticeFromFoneshareCloud", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<SystemNoticeCloudListResult> getSystemNoticeFromFoneshareCloud(@RequestBody QueryNoticeListArg arg){
        return wxThirdCloudInnerService.queryNoticeList(arg);
    }

    @RequestMapping(value = "getSystemNoticeDetailFromFoneshareCloud", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<SystemNoticeResult> getSystemNoticeDetailFromFoneshareCloud(@RequestBody SendSystemNoticeArg arg) {
        return wxThirdCloudInnerService.getSystemNoticeDetailFromFoneshareCloud(arg);
    }

    @RequestMapping(value = "handleCloudSmsReportData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result handleCloudSmsReportData(@RequestBody CloudSmsReportArg arg){
        if (arg == null || CollectionUtils.isEmpty(arg.getReportResultList())){
            return Result.newSuccess();
        }
        return wxThirdCloudInnerService.handleCloudSmsReportData(arg);
    }

    /**
     * 专属云查询企业微信suiteToken（从纷享云环境查）
     * @param arg
     * @return
     */
    @RequestMapping(value = "getQywxSuiteToken", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<String> getQywxSuiteToken(@RequestBody GetQywxSuiteTokenArg arg) {
        return qywxSelfBuildAppCallbackService.getQywxSuiteToken(arg.getSuiteId());
    }
}
