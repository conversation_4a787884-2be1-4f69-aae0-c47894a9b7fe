package com.facishare.marketing.web.arg.conference;

import com.facishare.marketing.api.arg.BasePageArg;
import com.facishare.marketing.common.enums.ActivitySignOrEnrollEnum;
import com.facishare.marketing.common.enums.SaveCrmStatusEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataInviteStatusEnum;
import com.facishare.marketing.common.enums.conference.ConferenceEnrollReviewStatusEnum;
import com.facishare.marketing.common.typehandlers.value.RuleGroupList;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Created by zhengh on 2019/7/18.
 * 查询参会人员
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class QueryConferenceParticipantsArg extends BasePageArg {

    @ApiModelProperty("会议id")
    private String conferenceId;

    /**
     * {@link ConferenceEnrollReviewStatusEnum}
     */
    @ApiModelProperty("审核状态 0 待审核 1审核成功 2 审核失败")
    private List<Integer> reviewStatus;

    /**
     * {@link ActivitySignOrEnrollEnum}
     */
    @ApiModelProperty("签到状态 2 已签到 3 未签到")
    private List<Integer> signStatus;

    @ApiModelProperty("搜索关键字")
    private String keyword;

    @ApiModelProperty("是否过滤无手机号用户")
    private Boolean filterPhoneUser = false;

    /**
     * {@link CampaignMergeDataInviteStatusEnum}
     */
    @ApiModelProperty("邀约状态 0 已邀约 1 待邀约")
    private List<Integer> inviteStatus;

    /**
     * {@link SaveCrmStatusEnum}
     */
    @ApiModelProperty("保存crm状态 -1 全部、0已存入、1存入失败、2重复待关联、 3已关联")
    private List<Integer> saveCrmStatus;

    @ApiModelProperty("来源渠道value")
    private List<String> channelValue;

    @ApiModelProperty("分组过滤id")
    private List<String> groupUserId;

    @ApiModelProperty("支付状态 0 全部 1 未支付 2 已支付")
    private List<Integer> payStatus;

    private RuleGroupList ruleGroupJson;
}
