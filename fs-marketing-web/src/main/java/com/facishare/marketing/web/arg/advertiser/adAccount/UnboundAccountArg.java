package com.facishare.marketing.web.arg.advertiser.adAccount;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021/11/24 3:17 下午
 */
@Data
public class UnboundAccountArg implements Serializable {
    @ApiModelProperty("广告账户表的主键id")
    private String adAccountId;
    @ApiModelProperty("账户类型")
    private String source;
}
