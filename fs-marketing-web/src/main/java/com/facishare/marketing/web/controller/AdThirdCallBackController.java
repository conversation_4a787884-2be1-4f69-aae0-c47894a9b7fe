package com.facishare.marketing.web.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.AdThirdSyncDataArg;
import com.facishare.marketing.api.service.baidu.BaiduCampaignService;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.web.mq.AdvertiseCallbackMessageSender;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.Enumeration;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/adThridCallBack")
public class AdThirdCallBackController {
    @Autowired
    private BaiduCampaignService baiduCampaignService;

    @Autowired
    private AdvertiseCallbackMessageSender advertiseCallbackMessageSender;

    @ApiOperation(value = "接收第三方平台推送的线索数据", tags = "680")
    @PostMapping(value = "/recvData")
    public Result recvData(@RequestBody AdThirdSyncDataArg arg) {
        if (arg == null || StringUtils.isEmpty(arg.getEa())) {
            log.info("AdThirdCallBackController.recvData param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (StringUtils.isEmpty(arg.getAdSource())) {
            arg.setAdSource(AdSourceEnum.OTHER.getSource());
        }
//        return baiduCampaignService.syncThirdLeadDataToMq(arg);
        log.info("syncThirdLeadDataToMq data:[{}]", JSON.toJSONString(arg));
        advertiseCallbackMessageSender.send(arg.getEa(), arg);
        return Result.newSuccess();
    }

    @ApiOperation(value = "腾讯线索推送", tags = "681")
    @PostMapping(value = "/tencent/recvData")
    public Result tencentRecvData(@RequestBody ObjectData objectData, @RequestHeader("x-signature") String signature, @RequestHeader("x-request-id") String requestId) {
        return baiduCampaignService.syncTencentLeadDataToMq(objectData, signature, requestId);
    }

    @ApiOperation(value = "专属云内部广告线索接口", tags = "680")
    @PostMapping(value = "inner/recvData")
    public Result innerRecvData(@RequestBody AdThirdSyncDataArg arg) {
        if (arg == null || StringUtils.isEmpty(arg.getEa())) {
            log.info("AdThirdCallBackController.innerRecvData param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (StringUtils.isEmpty(arg.getAdSource())) {
            arg.setAdSource(AdSourceEnum.OTHER.getSource());
        }
        return baiduCampaignService.syncThirdLeadData(arg);
    }
}
