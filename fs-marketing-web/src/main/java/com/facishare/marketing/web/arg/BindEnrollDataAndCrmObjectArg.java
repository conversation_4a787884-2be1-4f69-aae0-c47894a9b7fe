package com.facishare.marketing.web.arg;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * Created  By zhoux 2020/09/17
 **/
@Data
public class BindEnrollDataAndCrmObjectArg implements Serializable {

    @ApiModelProperty("报名id")
    private String enrollId;

    @ApiModelProperty("对象id")
    private String objectId;

    @ApiModelProperty("对象apiName")
    private String objectApiName;


    public boolean isWrongParam() {
        return StringUtils.isBlank(enrollId) || StringUtils.isBlank(objectId) || StringUtils.isBlank(objectApiName);
    }

}
