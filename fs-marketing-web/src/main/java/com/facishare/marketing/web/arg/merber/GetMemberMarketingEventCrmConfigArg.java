package com.facishare.marketing.web.arg.merber;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2020/12/24
 **/
@Data
public class GetMemberMarketingEventCrmConfigArg implements Serializable {

    @ApiModelProperty("市场活动id")
    private String marketingEventId;

    public boolean isWrongParam() {
        return StringUtils.isBlank(marketingEventId);
    }

}
