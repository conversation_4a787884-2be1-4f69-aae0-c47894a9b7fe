package com.facishare.marketing.web.arg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 * Created by z<PERSON>gh on 2020/12/9.
 */
@Data
public class CreateOrUpdateShareContent implements Serializable{
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("企业账号")
    private String ea;

    @ApiModelProperty("员工id")
    private Integer fsUserId;

    @ApiModelProperty("物料类型")
    private Integer objectType;

    @ApiModelProperty("物料id")
    private String objectId;

    @ApiModelProperty("转发标题")
    private String title;

    @ApiModelProperty("转发描述")
    private String description;

    @ApiModelProperty("转发封面地址")
    private String image;

    public boolean isWrongParam(){
        if (StringUtils.isEmpty(objectId) || objectType == null || StringUtils.isEmpty(title) || StringUtils.isEmpty(description) || StringUtils.isEmpty(image)){
            return true;
        }
        return false;
    }
}
