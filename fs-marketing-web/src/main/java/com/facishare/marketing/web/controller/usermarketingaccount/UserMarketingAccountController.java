package com.facishare.marketing.web.controller.usermarketingaccount;

import com.facishare.marketing.api.arg.AssociateExternalMarketingAccountByUnionIdAndOpenIdArg;
import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.usermarketingaccount.*;
import com.facishare.marketing.api.data.usermarketingaccount.GetInfoByIdentifyVO;
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.usermarketingaccount.*;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.common.enums.ChannelEnum;
import com.facishare.marketing.common.result.FunctionResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.util.AssertUtil;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.arg.DeleteTagsToCrmDataArg;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import retrofit2.http.HeaderMap;

import javax.servlet.http.HttpServletResponse;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 03/04/2019
 */
@RestController
@RequestMapping("userMarketingAccount")
@Slf4j
@Api(description = "营销用户", tags = "UserMarketingAccountController")
public class UserMarketingAccountController {
    @Autowired
    private UserMarketingAccountService userMarketingAccountService;

    @CheckIdentityTrigger
    @RequestMapping(value = "getUserMarketingAccountDetails", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "营销用户详情", notes = "营销用户详情", tags = "1.9.2")// todo get
    public Result<UserMarketingAccountDetailsResult> getUserMarketingAccountDetails(@RequestBody UserMarketingAccountDetailsArg arg) {
        return userMarketingAccountService.getUserMarketingAccountDetails(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getUserMarketingId());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "listUserMarketingAccount", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "筛选营销用户", notes = "筛选营销用户", tags = "1.9.2")
    public Result<List<UserMarketingAccountData>> listUserMarketingAccount(@RequestBody ListByFilterArg arg) {
        return userMarketingAccountService.listUserMarketingAccount(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "getStatisticalDatas", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取统计数据", notes = "获取统计数据", tags = {"1.9.2","570"})
    public Result<GetStatisticalDatasResult> getStatisticalDatas() {
        return userMarketingAccountService.getStatisticalDatas(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "getTagsByUserMarketingAccount", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查看单个用户标签", notes = "查看单个用户标签", tags = "1.9.2")
    public Result<TagNameList> getTagsByUserMarketingAccount(@RequestBody GetTagsByUserMarketingArg arg) {// todo get
        return userMarketingAccountService.getTagsByUserMarketingId(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getUserMarketingId());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "batchAddTags", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "为指定营销用户添加标签", notes = "为指定营销用户添加指定标签", tags = {"1.9.2", "3.1.2"})
    public Result<Boolean> batchAddTags(@RequestBody AddTagsToUserMarketingAccountsTArg arg) {
        return userMarketingAccountService.asyncBatchAddTagsToUserMarketings(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getTargetObjectApiNames(), arg.getUserMarketingIds(), arg.getTagNameList());
   }

    @CheckIdentityTrigger
    @RequestMapping(value = "batchDeleteTagsFromUserMarketings", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "批量移除影响用户的标签", notes = "批量将用户用指定标签移除", tags = {"1.9.2", "3.1.2"})
    public Result<Boolean> batchDeleteUserMarketingAccountsFromTags(@RequestBody BatchDeleteTagsFromUserMarketingArg arg) {
        return userMarketingAccountService.asyncBatchDeleteTagsFromUserMarketings(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(),arg.getTargetObjectApiName(), arg.getUserMarketingIds(), arg.getTagNameList());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "getUserMarketingAccountByMemberId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "会员id获取营销用户id", notes = "会员id获取营销用户id", tags = "1.9.2")
    public Result<String> getUserMarketingAccountByMemberId(@RequestBody GetUserMarketingAccountByMemberIdArg arg) {
        return userMarketingAccountService.getUserMarketingAccountByMemberId(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getMemberId());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "getUserMarketingAccountByWxUserId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "微信用户获取营销用户id", notes = "微信用户获取营销用户id", tags = "1.9.5")
    public Result<UserMarketingAccountIdResult> getUserMarketingAccountByWxUserId(@RequestBody GetUserMarketingAccountByWxUserArg arg) {
        return userMarketingAccountService.getUserMarketingAccountByWxUserId(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getCrmWxUserId());
    }

    @RequestMapping(value = "getUserMarketingAccountByObject", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "对象数据获取营销用户id", notes = "对象数据获取营销用户id", tags ="7.8")
    public Result<UserMarketingAccountIdResult> getUserMarketingAccountByObject(@RequestBody GetUserMarketingAccountByObjectArg arg){
        if (arg == null || StringUtils.isBlank(arg.getCrmObjectApiName()) || StringUtils.isBlank(arg.getCrmObjectId())) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.getUserMarketingAccountByObject(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "给CRM对象添加标签", httpMethod = "POST")
    @RequestMapping(value = "addTagToCrmData", method = RequestMethod.POST, produces = "application/json")
    public Result<Void> addTagToCrmData(@RequestBody AddTagToCrmDataArg arg) {
        if (arg == null || !arg.valid()) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.addTagToCrmData(arg);
    }

    @ApiOperation(value = "查询对象上的标签", httpMethod = "POST")
    @RequestMapping(value = "queryTagByCrmObjectId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<TagNameList> queryTagByCrmObjectId(@RequestBody QueryTagByCrmObjectIdArg arg){
        if (arg == null || StringUtils.isBlank(arg.getCrmObjectApiName()) || StringUtils.isBlank(arg.getCrmObjectId())){
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        return userMarketingAccountService.queryTagByCrmObjectId(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "给CRM对象移除标签", httpMethod = "POST")
    @RequestMapping(value = "deleteTagsToCrmData", method = RequestMethod.POST, produces = "application/json")
    public Result<Void> deleteTagsToCrmData(@RequestBody DeleteTagsToCrmDataArg arg) {
        if (arg == null || !arg.valid()) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.batchDeleteTagNamesToCrmData(arg.getTenantId(), -10000, arg);
    }

    @CheckIdentityTrigger
    @ApiOperation(value = "批量给CRM对象添加标签", httpMethod = "POST", tags = "3.1.2")
    @RequestMapping(value = "batchAddTagNamesToCrmData", method = RequestMethod.POST, produces = "application/json")
    public Result<Void> batchAddTagNamesToCrmData(@RequestBody BatchAddOrDeleteTagNamesToCrmDataArg arg) {
        if (arg == null || !arg.valid()) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.batchAddTagNamesToCrmData(UserInfoKeeper.getEi(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "批量给CRM对象添加标签-对象列表-异步通知", httpMethod = "POST", tags = "10.2")
    @RequestMapping(value = "bulkHangTagWithCrmNotice", method = RequestMethod.POST, produces = "application/json")
    public Result bulkHangTagWithCrmNotice(@RequestBody BulkHangTagWithCrmNoticeArg arg) {
        if (CollectionUtils.isEmpty(arg.getDataIds()) || CollectionUtils.isEmpty(arg.getTagIds()) || StringUtils.isBlank(arg.getObjectApiName())) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.bulkHangTagWithCrmNotice(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @ApiOperation(value = "批量给CRM对象添加标签", httpMethod = "POST", tags = "3.1.2")
    @RequestMapping(value = "appendAddTagNamesToCrmData", method = RequestMethod.POST, produces = "application/json")
    public Result<Void> appendAddTagNamesToCrmData(@RequestBody AppendAddTagNamesToCrmDataArg arg) {
        if (arg == null || !arg.valid()) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.appendAddTagNamesToCrmData(UserInfoKeeper.getEi(), UserInfoKeeper.getFsUserId(), arg);
    }


    @CheckIdentityTrigger
    @ApiOperation(value = "批量给CRM对象删除标签", httpMethod = "POST", tags = "3.1.2")
    @RequestMapping(value = "batchDeleteTagNamesToCrmData", method = RequestMethod.POST, produces = "application/json")
    public Result<Void> batchDeleteTagNamesToCrmData(@RequestBody BatchAddOrDeleteTagNamesToCrmDataArg arg) {
        if (arg == null || !arg.valid()) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.batchDeleteTagNamesToCrmData(UserInfoKeeper.getEi(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "initAllUserMarketingAccountAndTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "初始化就企业营销用户和标签相关数据", notes = "初始化就企业营销用户和标签相关数据", tags = "1.9.2")
    public Result<Map<String, Integer>> initAllUserMarketingAccountAndTag() {
        return userMarketingAccountService.initUserMarketingAccountAndTag();
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "initUserMarketingAccountAndTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "初始化就企业营销用户和标签相关数据", notes = "初始化就企业营销用户和标签相关数据", tags = "1.9.2")
    public Result<Integer> initUserMarketingAccountAndTag(@RequestBody JSONObject arg) {
        String eas = arg.getString("eas");
        AssertUtil.checkNotNull(eas, "eas");
        String[] eaList = eas.split(",");
        return userMarketingAccountService.initUserMarketingAccountAndTag(eaList);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "initAllMarketingProcessObj", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "初始化营销流程", notes = "初始化营销流程", tags = "2.0")
    public Result<Map<String, Integer>> initAllMarketingProcessObj() {
        return userMarketingAccountService.initAllMarketingProcessObj();
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "initAllMarketingProcessLatencyResultObj", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "初始化营销流程", notes = "初始化营销流程", tags = "2.0")
    public Result<Map<String, Integer>> initAllMarketingProcessLatencyResultObj() {
        return userMarketingAccountService.initAllMarketingProcessLatencyResultObj();
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "initMarketingProcessLatencyResultObj", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "初始化营销流程等待结果对象", notes = "初始化营销流程等待结果对象", tags = "2.0")
    public Result<Integer> initMarketingProcessLatencyResultObj(@RequestBody JSONObject arg) {
        String eas = arg.getString("eas");
        AssertUtil.checkNotNull(eas, "eas");
        String[] eaList = eas.split(",");
        return userMarketingAccountService.initMarketingProcessLatencyResultObj(eaList);
    }


    @CheckIdentityTrigger
    @RequestMapping(value = "pageUserMarketingLookUpStatisticByObject", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "根据物料获取营销用户访问详情统计", notes = "根据物料获取营销用户访问详情统计", tags = "2.0")
    public Result<PageResult<UserMarketingLookUpStatisticByObjectResult>> pageUserMarketingLookUpStatisticByObject(@RequestBody UserMarketingLookUpStatisticByObjectArg arg) {
        if (!arg.checkParam()) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.pageUserMarketingLookUpStatisticByObject(UserInfoKeeper.getEa(),arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "exportUserMarketingLookUpStatisticByObject", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "导出营销用户物料访问详情统计", notes = "导出营销用户物料访问详情统计", tags = "2.0")
    public void exportUserMarketingLookUpStatisticByObject(UserMarketingLookUpStatisticByObjectArg arg, HttpServletResponse httpServletResponse) throws Exception{

        userMarketingAccountService.exportUserMarketingLookUpStatisticByObject(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);

    }

    @CheckIdentityTrigger
    @RequestMapping(value = "pageUserMarketingLookUpDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "营销用户访问详情", notes = "营销用户访问详情", tags = "2.0")
    public Result<PageResult<pageUserMarketingLookUpDetail>> pageUserMarketingLookUpDetail(@RequestBody UserMarketingLookUpStatisticByObjectArg arg) {
        if (!arg.checkParam()||StringUtils.isBlank(arg.getUserMarketingId())) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.pageUserMarketingLookUpDetail(UserInfoKeeper.getEa(),arg);
    }
    @ApiOperation(value = "根据标签的label查询PAAS标签的id(不存在则创建)", httpMethod = "POST")
    @RequestMapping(value = "getOrCreateTagIdByTagName", method = RequestMethod.POST, produces = "application/json")
    public Result<Map<String, String>> getOrCreateTagIdByTagName(@RequestBody GetOrCreateTagIdsByTagNamesArg arg) {
        if (arg == null || !arg.valid()) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.getOrCreateTagIdsByTagNames(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getDescribeApiName(), arg.getTagNames());
    }

    @ApiOperation(value = "根据PAAS标签id查询标签label", httpMethod = "POST")
    @RequestMapping(value = "listTagByIds", method = RequestMethod.POST, produces = "application/json")
    public Result<Map<String, TagName>> listTagByIds(@RequestBody ListTagByIdsArg arg){
        if (arg == null || !arg.valid()) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        return userMarketingAccountService.listTagByIds(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getTagIds());
    }

    @RequestMapping(value = "queryMarketingUserAccountByObject", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "企业库详情获取营销用户列表", notes = "企业库详情获取营销用户列表", tags ="9.3.1")
    public Result<com.facishare.marketing.common.result.PageResult<UserMarketingAccountDetailsResult>> queryMarketingUserAccountByObject(@RequestBody GetPageUserMarketingAccountByObjectArg arg){
        if (arg == null || StringUtils.isBlank(arg.getCrmObjectApiName()) || StringUtils.isBlank(arg.getCrmObjectId())) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.queryMarketingUserAccountByObject(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @RequestMapping(value = "associateExternalMarketingAccountByUnionIdAndOpenId", method = RequestMethod.POST)
    @ApiOperation(value = "通过外部的openid或unionid关联营销用户", notes = "通过外部的openid或unionid关联营销用户", tags = {"9.4"})
    public Result<AssociateExternalMarketingAccountByUnionIdAndOpenIdResult> associateExternalMarketingAccountByUnionIdAndOpenId(@RequestHeader Map<String, String> header, @RequestBody AssociateExternalMarketingAccountByUnionIdAndOpenIdArg arg) {
        String userId = header.get("x-fs-userinfo");
        String ea = header.get("x-fs-enterprise-account");
        if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(ea)){
            log.info("mergeUserMarketingAccountByUnionId ea or userId 参数错误 header:{}, data:{}", header, arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (StringUtils.isEmpty(arg.getUnionId()) && StringUtils.isEmpty(arg.getOpenId())){
            log.info("mergeUserMarketingAccountByUnionId unionId or openId 参数错误 header:{}, data:{}", header, arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        List<String> apinames = ChannelEnum.getAllChannelApiName();
        if (!apinames.contains(arg.getApiName())){
            log.info("mergeUserMarketingAccountByUnionId apiName错误 header:{}  arg:{}", header, arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return userMarketingAccountService.associateExternalMarketingAccountByUnionIdAndOpenId(ea, Integer.parseInt(userId), arg.getApiName(), arg.getObjectId(), arg.getUnionId(), arg.getOpenId());
    }
    @CheckIdentityTrigger
    @RequestMapping(value = "listMiniAppUserMarketingAccount", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "筛选小程序营销用户", notes = "筛选小程序营销用户", tags = "1.9.2")
    public Result<PageResult<UserMarketingAccountData>> listMiniAppUserMarketingAccount(@RequestBody ListByFilterArg arg) {
        return userMarketingAccountService.listMiniAppUserMarketingAccount(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "getInfoByIdentify", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "根据身份标识获取营销用户", notes = "", tags = "1.9.2")
    public FunctionResult<GetInfoByIdentifyVO> getInfoByIdentify(@RequestBody GetInfoByIdentifyArg arg, @RequestHeader Map<String, String> header) {

        Integer tenantId = Integer.parseInt(header.get("x-fs-ei"));
        Integer userId = Integer.parseInt(header.get("x-fs-userinfo"));
        arg.setTenantId(tenantId);
        arg.setFsUserId(userId);
        return userMarketingAccountService.getInfoByIdentify(arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "getMarketingAccountByTargetId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "营销用户id", notes = "营销用户id", tags = "1.9.2")
    public Result<String> getMarketingAccountByTargetId(@RequestBody MarketingAccountByTargetArg arg) {
        if (StringUtils.isBlank(arg.getTargetId()) || arg.getTargetType() == null) {
            log.warn("UserMarketingAccountController.getTagsByUserMarketingAccount param error args{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if(StringUtils.isBlank(arg.getEa())){
            arg.setEa(UserInfoKeeper.getEa());
        }
        if (StringUtils.isBlank(arg.getEa()) ) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.getMarketingAccountByTargetId(arg);
    }
}
