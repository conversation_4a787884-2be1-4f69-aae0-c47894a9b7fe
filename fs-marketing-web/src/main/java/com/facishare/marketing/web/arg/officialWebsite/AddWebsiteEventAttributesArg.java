package com.facishare.marketing.web.arg.officialWebsite;

import com.facishare.marketing.api.arg.officialWebsite.BaseWebSiteAttributesArg;
import com.facishare.marketing.common.enums.officialWebsite.OfficialWebsiteEventAttributesTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2020/08/19
 **/
@Data
public class AddWebsiteEventAttributesArg implements Serializable {

    @ApiModelProperty("属性列表")
    List<BaseWebSiteAttributesArg> attributesData;

    @ApiModelProperty("当前保存数据类型 0 事件 1属性")
    private Integer dataType;

    @ApiModelProperty("对象主内容")
    private BaseWebSiteAttributesArg content;

    @ApiModelProperty("属性保存时的事件id")
    private String parentId;

    private String websiteId;


    public boolean isWrongParam() {
        return dataType == null || Arrays.stream(OfficialWebsiteEventAttributesTypeEnum.values()).noneMatch(data -> data.getType().equals(dataType)) || (dataType.equals(OfficialWebsiteEventAttributesTypeEnum.ATTRIBUTES.getType()) && StringUtils.isBlank(parentId)) || content == null;
    }

}
