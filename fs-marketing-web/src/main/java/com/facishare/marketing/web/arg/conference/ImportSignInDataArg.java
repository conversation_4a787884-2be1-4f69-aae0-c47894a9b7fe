package com.facishare.marketing.web.arg.conference;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2021/04/20
 **/
@Data
public class ImportSignInDataArg implements Serializable {

    @ApiModelProperty("文件path")
    private String filePath;

    @ApiModelProperty("市场活动id")
    private String marketingEventId;

    public boolean isWrongParam() {
        return StringUtils.isBlank(filePath) || StringUtils.isBlank(marketingEventId);
    }

}
