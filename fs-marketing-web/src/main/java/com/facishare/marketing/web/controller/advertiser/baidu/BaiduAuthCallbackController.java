package com.facishare.marketing.web.controller.advertiser.baidu;


import com.facishare.marketing.api.arg.advertiser.BaiduAuthCallBackArg;
import com.facishare.marketing.api.service.advertiser.headlines.AuthCallbackService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping("baidu")
@Api(description = "百度广告鉴权回调", tags = "BaiduAuthCallbackController")
public class BaiduAuthCallbackController {
    @Autowired
    private AuthCallbackService authCallbackService;

    @RequestMapping(value = "/authCallback", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "授权回调地址,获取access_token", notes = "授权回调地址")
    public Result<String> authCallback(BaiduAuthCallBackArg baiduAuthCallBackArg) {
        return authCallbackService.baiduAuthCallBack(baiduAuthCallBackArg);
    }

    @RequestMapping(value = "/inner/authCallback", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "授权专属云回调地址,获取access_token", notes = "授权专属云回调地址")
    public Result<String> authInnerCallback(BaiduAuthCallBackArg baiduAuthCallBackArg) {
        return authCallbackService.baiduAuthInnerCallBack(baiduAuthCallBackArg);
    }

    @RequestMapping(value = "/getBaiduAuthUrl", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取百度广告授权链接", notes = "获取百度广告授权链接")
    public Result<String> getBaiduAuthUrl() {
        return authCallbackService.getBaiduAuthUrl(UserInfoKeeper.getEa());
    }
}
