package com.facishare.marketing.web.controller;

import com.facishare.marketing.api.arg.ListEmployeeArg;
import com.facishare.marketing.api.result.ListEmployeeResult;
import com.facishare.marketing.api.result.fsBind.*;
import com.facishare.marketing.api.service.FsBindService;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.arg.GetCardInfoByBindFsUserIdArg;
import com.facishare.marketing.web.arg.UpdateMarketingNoticeSettingArg;
import com.facishare.marketing.web.arg.UpdateQywxContactSettingArg;
import com.facishare.marketing.web.arg.updateQywxSidebarSendSettingArg;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Auther: dzb
 * @Date: 2018/10/24
 * @Description:
 */
@RestController
@RequestMapping("fsBind")
@Slf4j
@Api(description = "渠道员工开通情况", tags = "FsBindController")
public class FsBindController {
    @Autowired
    private FsBindService fsBindService;

    @CheckIdentityTrigger
    @RequestMapping(value = "/listFsBind", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "员工开通情况", notes = "员工开通情况")
    public Result<PageResult<ListEmployeeResult>> listFsBind(@RequestBody com.facishare.marketing.web.arg.ListEmployeeArg arg) {
        ListEmployeeArg vo = BeanUtil.copy(arg, ListEmployeeArg.class);
        return fsBindService.listEmployee(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    @RequestMapping(value = "/getAddressBookSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取通讯录设置")
    public Result<GetAddressBookSettingResult> getAddressBookSetting() {
        return fsBindService.getAddressBookSetting(UserInfoKeeper.getEa());
    }


    @RequestMapping(value = "/getCardInfoByBindFsUserId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "根据绑定员工信息获取名片信息")
    public Result<GetCardInfoByBindFsUserIdResult> getCardInfoByBindFsUserId(@RequestBody GetCardInfoByBindFsUserIdArg arg) {
        if(arg.isWrongParam()) {
            log.warn("FsBindController.getCardInfoByBindFsUserId param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return fsBindService.getCardInfoByBindFsUserId(UserInfoKeeper.getEa(), arg.getFsUserId(), true);
    }

    @ApiOperation(value = "获取全员营销通知方式设置")
    @RequestMapping(value = "/getMarketingNoticeSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetMarketingNoticeSettingResult> getMarketingNoticeSetting() {
        return fsBindService.getMarketingNoticeSetting(UserInfoKeeper.getEa());
    }

    @ApiOperation(value = "更新全员营销通知方式设置")
    @RequestMapping(value = "/updateMarketingNoticeSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> updateMarketingNoticeSetting(@RequestBody UpdateMarketingNoticeSettingArg arg) {
        if(StringUtils.isBlank(arg.getId()) || CollectionUtils.isEmpty(arg.getNoticeType())) {
            log.warn("FsBindController.updateMarketingNoticeSetting param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return fsBindService.updateMarketingNoticeSetting(UserInfoKeeper.getEa(),arg.getId(),arg.getNoticeType());
    }


    @ApiOperation(value = "获取全侧边栏发送设置")
    @RequestMapping(value = "/getQywxSidebarMaterialSendSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetQywxSidebarSendSettingResult> getQywxSidebarMaterialSendSetting() {
        return fsBindService.getQywxSidebarMaterialSendSetting(UserInfoKeeper.getEa());
    }

    @ApiOperation(value = "更新侧边栏发送设置")
    @RequestMapping(value = "/updateQywxSidebarMaterialSendSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> updateQywxSidebarMaterialSendSetting(@RequestBody updateQywxSidebarSendSettingArg arg) {
        if(arg.getSendType()==null) {
            log.warn("FsBindController.updateQywxSidebarMaterialSendSetting param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return fsBindService.updateQywxSidebarMaterialSendSetting(UserInfoKeeper.getEa(),arg.getSendType());
    }

    @ApiOperation(value = "获取企微朋友圈,群发通讯录设置")
    @RequestMapping(value = "/getQywxContactSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetQywxContactSettingResult> getQywxContactSetting() {
        return fsBindService.getQywxContactSetting(UserInfoKeeper.getEa());
    }

    @ApiOperation(value = "更新企微朋友圈,群发通讯录设置")
    @RequestMapping(value = "/updateQywxContactSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> updateQywxContactSetting(@RequestBody UpdateQywxContactSettingArg arg) {
        if(arg.getContactType()==null) {
            log.warn("FsBindController.updateQywxContactSetting param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return fsBindService.updateQywxContactSetting(UserInfoKeeper.getEa(),arg.getContactType());
    }
}
