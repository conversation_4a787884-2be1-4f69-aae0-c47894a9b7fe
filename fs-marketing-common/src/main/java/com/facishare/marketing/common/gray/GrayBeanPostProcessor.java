package com.facishare.marketing.common.gray;

import com.alibaba.dubbo.config.ConsumerConfig;
import com.alibaba.dubbo.config.ProviderConfig;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;

import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class GrayBeanPostProcessor implements BeanPostProcessor {

    private final AtomicBoolean consumerInitialized = new AtomicBoolean(false);
    private final AtomicBoolean providerInitialized = new AtomicBoolean(false);

    @Autowired(required=false)
    private ConsumerConfig consumerConfig;

    @Autowired(required=false)
    private ProviderConfig providerConfig;

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        initConsumer();
        initProvider();
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    private void initConsumer() {
        try {
            if (consumerConfig != null && !consumerInitialized.get()) {
                //设置负载均衡策略
                consumerConfig.setCluster(GrayCluster.NAME);
                consumerConfig.setLoadbalance("grayLoadBalance");
                consumerInitialized.set(true);
            }
        } catch (Exception e) {
            log.error("initConsumer fail, can not set environment, please check", e);
        }
    }

    private void initProvider() {
        try {
            if (providerConfig != null && !providerInitialized.get()) {
                if (null == providerConfig.getParameters()) {
                    providerConfig.setParameters(Maps.newHashMap());
                }
                providerConfig.getParameters().putIfAbsent(Constant.DUBBO_PROVIDER_BIZ_KEY, Constant.DUBBO_PROVIDER_BIZ_VALUE);
                providerConfig.getParameters().putIfAbsent(Constant.DUBBO_PROVIDER_PROFILE_KEY, System.getProperty("process.profile"));
                //112本地单元测试
                String local = System.getProperty("local");
                if (StringUtils.isNotBlank(local)) {
                    providerConfig.getParameters().putIfAbsent("local", local);
                }
                providerInitialized.set(true);
            }
        } catch (Exception e) {
            log.error("initProvider fail, can not set environment, please check", e);
        }
    }

}
