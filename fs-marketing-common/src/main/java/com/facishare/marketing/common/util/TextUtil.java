package com.facishare.marketing.common.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

/**
 * @ClassName TextUtil
 * @Description
 * <AUTHOR>
 * @Date 2019/3/13 3:56 PM
 */
public class TextUtil {

    public static String subStr(String string, int maxLength) {
        if (StringUtils.isBlank(string)) {
            return null;
        }

        string = string.replaceAll("[\\ud800\\udc00-\\udbff\\udfff\\ud800-\\udfff]", "");

        if (string.length() <= maxLength) {
            return string;
        }

        return string.substring(0, maxLength);
    }
    
    public static String pureSubStr(String string, int maxLength) {
        if (StringUtils.isBlank(string)) {
            return null;
        }
        
        if (string.length() <= maxLength) {
            return string;
        }
        
        return string.substring(0, maxLength);
    }

    public static boolean isJson(String content) {
        try {
            JSONObject jsonStr = JSONObject.parseObject(content);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static String replaceText(String content, String replaceText, String targetText) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        content = content.replace(replaceText, targetText);
        return content;
    }

    /**
     * 字符串替换，替换source中的{abc}为paramMap中具体的值
     * @param source
     * @param paramMap
     * @return
     */
    public static String replaceVarByMap(String source, Map<String, Object> paramMap){
        String resultStr = source;
        String leftBrace = "\\{";
        String rightBrace = "\\}";
        if (paramMap == null || paramMap.size() == 0){
            return resultStr;
        }
        StringBuilder paramStr;
        for (String key : paramMap.keySet()) {
            if (key == null || paramMap.get(key) == null) {
                continue;
            }
            paramStr = new StringBuilder();
            paramStr.append(leftBrace).append(key).append(rightBrace);
            resultStr = resultStr.replaceAll(paramStr.toString(), ObjectUtils.toString(paramMap.get(key)));
        }
        return resultStr;
    }

    public static String getGrowthRate(Integer count, Integer lastCount) {
        if(count==null || lastCount==null || (count-lastCount==0)){
            return "+0%";
        }
        if(lastCount == 0){
            return "+100%";
        }
        //return (count-lastCount>0 ? "+" : "-") + (int) Math.abs(Double.parseDouble(String.valueOf(count-lastCount)) / lastCount * 100) + "%";
        return (count-lastCount>0 ? "+" : "-") + (int) Math.abs((BigDecimal.valueOf(Double.parseDouble(String.valueOf(count-lastCount)) / lastCount).setScale(2, RoundingMode.UP).doubleValue() * 100))+"%"
        ;
    }

    public static void main(String[] args) {
       String ss = UrlUtils.urlDecode("%E7%99%BE%E8%83%9C");
       System.out.println("ss=="+ss);
    }

    public static boolean checkIdIsLegal(String id) {
        if (StringUtils.isBlank(id)) {
            return true;
        }
        String pattern = "^[a-zA-Z0-9]+$";
        return id.matches(pattern);
    }
}
