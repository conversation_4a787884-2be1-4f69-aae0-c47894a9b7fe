package com.facishare.marketing.common.typehandlers;

import com.facishare.marketing.common.typehandlers.value.DistributorFormDataEnroll;
import com.facishare.marketing.common.util.GsonUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class DistributorFormDataEnrollTypeHandler extends BaseTypeHandler<DistributorFormDataEnroll> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, DistributorFormDataEnroll parameter, JdbcType jdbcType) throws SQLException {
        PGobject pGobject = new PGobject();
        pGobject.setType("jsonb");
        pGobject.setValue(GsonUtil.getGson().toJson(parameter));
        ps.setObject(i, pGobject);
    }

    @Override
    public DistributorFormDataEnroll getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return GsonUtil.getGson().fromJson(rs.getString(columnName), DistributorFormDataEnroll.class);
    }

    @Override
    public DistributorFormDataEnroll getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return GsonUtil.getGson().fromJson(rs.getString(columnIndex), DistributorFormDataEnroll.class);
    }

    @Override
    public DistributorFormDataEnroll getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return GsonUtil.getGson().fromJson(cs.getString(columnIndex), DistributorFormDataEnroll.class);
    }
}
