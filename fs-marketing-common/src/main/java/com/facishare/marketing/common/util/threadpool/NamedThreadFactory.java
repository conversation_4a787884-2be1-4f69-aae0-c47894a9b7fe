package com.facishare.marketing.common.util.threadpool;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

public class NamedThreadFactory implements ThreadFactory {
    private final AtomicInteger threadNumber = new AtomicInteger(0);
    private final String name;
    private final ThreadGroup group;

    public NamedThreadFactory(String name) {
        this.name = name;
        SecurityManager s = System.getSecurityManager();
        this.group = s != null ? s.getThreadGroup() : Thread.currentThread().getThreadGroup();
    }

    public static void main(String[] args) {
        (new NamedThreadFactory("TestThread")).newThread(() -> {
            System.out.println("Thread name: " + Thread.currentThread().getName());
            System.out.println("Thread is daemon false: " + Thread.currentThread().isDaemon());
        }).start();
        (new NamedThreadFactory("TestThreadDaemon")).newDaemonThread(() -> {
            System.out.println("Thread name: " + Thread.currentThread().getName());
            System.out.println("Thread is daemon true: " + Thread.currentThread().isDaemon());
        }).start();
    }

    @Override
    public Thread newThread(Runnable runnable) {
        Thread t = new Thread(this.group, runnable, this.name + "_" + this.threadNumber.incrementAndGet(), 0L);
        if (t.isDaemon()) {
            t.setDaemon(false);
        }

        if (t.getPriority() != 5) {
            t.setPriority(5);
        }

        return t;
    }

    public Thread newDaemonThread(Runnable runnable) {
        Thread t = new Thread(this.group, runnable, this.name + "_" + this.threadNumber.incrementAndGet(), 0L);
        if (!t.isDaemon()) {
            t.setDaemon(true);
        }

        return t;
    }
}
