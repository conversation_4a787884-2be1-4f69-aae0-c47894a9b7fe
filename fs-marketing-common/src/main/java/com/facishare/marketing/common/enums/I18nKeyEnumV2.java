package com.facishare.marketing.common.enums;

public enum I18nKeyEnumV2 {
    MARK_ENUMS_ACTIONTYPEENUM_19("mark.enums.actiontypeenum_19", "复制邮箱"),
    MARK_ENUMS_ACTIONTYPEENUM_23("mark.enums.actiontypeenum_23", "复制微信"),
    MARK_ENUMS_ACTIONTYPEENUM_27("mark.enums.actiontypeenum_27", "点击手机号"),
    MARK_ENUMS_ACTIONTYPEENUM_31("mark.enums.actiontypeenum_31", "复制公司名称"),
    MARK_ENUMS_ACTIONTYPEENUM_35("mark.enums.actiontypeenum_35", "复制公司地址"),
    MARK_ENUMS_ACTIONTYPEENUM_39("mark.enums.actiontypeenum_39", "转发名片"),
    MARK_ENUMS_ACTIONTYPEENUM_43("mark.enums.actiontypeenum_43", "保存名片"),
    MARK_ENUMS_ACTIONTYPEENUM_47("mark.enums.actiontypeenum_47", "浏览名片"),
    MARK_ENUMS_ACTIONTYPEENUM_55("mark.enums.actiontypeenum_55", "浏览产品"),
    MARK_ENUMS_ACTIONTYPEENUM_59("mark.enums.actiontypeenum_59", "查看产品列表"),
    MARK_ENUMS_ACTIONTYPEENUM_63("mark.enums.actiontypeenum_63", "转发产品"),
    MARK_ENUMS_ACTIONTYPEENUM_68("mark.enums.actiontypeenum_68", "浏览文章"),
    MARK_ENUMS_ACTIONTYPEENUM_72("mark.enums.actiontypeenum_72", "浏览图片"),
    MARK_ENUMS_ACTIONTYPEENUM_82("mark.enums.actiontypeenum_82", "转发文章"),
    MARK_ENUMS_ACTIONTYPEENUM_86("mark.enums.actiontypeenum_86", "浏览文件"),
    MARK_ENUMS_ACTIONTYPEENUM_90("mark.enums.actiontypeenum_90", "预览文件"),
    MARK_ENUMS_ACTIONTYPEENUM_94("mark.enums.actiontypeenum_94", "下载文件"),
    MARK_ENUMS_ACTIONTYPEENUM_98("mark.enums.actiontypeenum_98", "转发文件"),
    MARK_ENUMS_ACTIONTYPEENUM_102("mark.enums.actiontypeenum_102", "浏览动态"),
    MARK_ENUMS_ACTIONTYPEENUM_106("mark.enums.actiontypeenum_106", "评论动态"),
    MARK_ENUMS_ACTIONTYPEENUM_110("mark.enums.actiontypeenum_110", "转发图片"),
    MARK_ENUMS_ACTIONTYPEENUM_114("mark.enums.actiontypeenum_114", "私信互动"),
    MARK_ENUMS_ACTIONTYPEENUM_126("mark.enums.actiontypeenum_126", "同意交换名片"),
    MARK_ENUMS_ACTIONTYPEENUM_130("mark.enums.actiontypeenum_130", "查看转发的产品"),
    MARK_ENUMS_ACTIONTYPEENUM_138("mark.enums.actiontypeenum_138", "查看转发的图片"),
    MARK_ENUMS_ACTIONTYPEENUM_142("mark.enums.actiontypeenum_142", "查看转发的文件"),
    MARK_ENUMS_ACTIONTYPEENUM_146("mark.enums.actiontypeenum_146", "完善客脉信息"),
    MARK_ENUMS_ACTIONTYPEENUM_150("mark.enums.actiontypeenum_150", "邀请交换名片"),
    MARK_ENUMS_ACTIONTYPEENUM_154("mark.enums.actiontypeenum_154", "转换为客脉"),
    MARK_ENUMS_ACTIONTYPEENUM_158("mark.enums.actiontypeenum_158", "转换为客户"),
    MARK_ENUMS_ACTIONTYPEENUM_163("mark.enums.actiontypeenum_163", "存入CRM"),
    MARK_ENUMS_ACTIONTYPEENUM_167("mark.enums.actiontypeenum_167", "查看活动"),
    MARK_ENUMS_ACTIONTYPEENUM_171("mark.enums.actiontypeenum_171", "转发活动"),
    MARK_ENUMS_ACTIONTYPEENUM_175("mark.enums.actiontypeenum_175", "报名活动"),
    MARK_ENUMS_ACTIONTYPEENUM_179("mark.enums.actiontypeenum_179", "查看转发的活动"),
    MARK_ENUMS_ACTIONTYPEENUM_184("mark.enums.actiontypeenum_184", "存入CRM线索"),
    MARK_ENUMS_ACTIONTYPEENUM_255("mark.enums.actiontypeenum_255", "查看产品试用"),
    MARK_ENUMS_ACTIONTYPEENUM_260("mark.enums.actiontypeenum_260", "提交产品试用"),
    MARK_ENUMS_ACTIONTYPEENUM_265("mark.enums.actiontypeenum_265", "提交文章表单"),
    MARK_ENUMS_ACTIONTYPEENUM_270("mark.enums.actiontypeenum_270", "转发海报"),
    MARK_ENUMS_ACTIONTYPEENUM_275("mark.enums.actiontypeenum_275", "浏览海报"),
    MARK_ENUMS_ACTIONTYPEENUM_280("mark.enums.actiontypeenum_280", "转发邀请函"),
    MARK_ENUMS_ACTIONTYPEENUM_285("mark.enums.actiontypeenum_285", "浏览邀请函"),
    MARK_ENUMS_ACTIONTYPEENUM_290("mark.enums.actiontypeenum_290", "提交表单"),
    MARK_ENUMS_ACTIONTYPEENUM_295("mark.enums.actiontypeenum_295", "浏览表单"),
    MARK_ENUMS_ACTIONTYPEENUM_300("mark.enums.actiontypeenum_300", "转发表单"),
    MARK_ENUMS_ACTIONTYPEENUM_305("mark.enums.actiontypeenum_305", "浏览微页面"),
    MARK_ENUMS_ACTIONTYPEENUM_310("mark.enums.actiontypeenum_310", "转发微页面"),
    MARK_ENUMS_ACTIONTYPEENUM_315("mark.enums.actiontypeenum_315", "会议签到"),
    MARK_ENUMS_ACTIONTYPEENUM_320("mark.enums.actiontypeenum_320", "领取名片红包"),
    MARK_ENUMS_ACTIONTYPEENUM_321("mark.enums.actiontypeenum_321", "领取文章红包"),
    MARK_ENUMS_ACTIONTYPEENUM_322("mark.enums.actiontypeenum_322", "领取微信红包"),
    MARK_ENUMS_ACTIONTYPEENUM_328("mark.enums.actiontypeenum_328", "分销人员提交线索"),
    MARK_ENUMS_ACTIONTYPEENUM_330("mark.enums.actiontypeenum_330", "添加分销人员"),
    MARK_ENUMS_ACTIONTYPEENUM_332("mark.enums.actiontypeenum_332", "增加企业社群"),
    MARK_ENUMS_ACTIONTYPEENUM_334("mark.enums.actiontypeenum_334", "短信开通企业"),
    MARK_ENUMS_ACTIONTYPEENUM_336("mark.enums.actiontypeenum_336", "短息购买条数"),
    MARK_ENUMS_ACTIONTYPEENUM_338("mark.enums.actiontypeenum_338", "短息消耗条数"),
    MARK_ENUMS_ACTIONTYPEENUM_340("mark.enums.actiontypeenum_340", "移动营销通访问"),
    MARK_ENUMS_ACTIONTYPEENUM_348("mark.enums.actiontypeenum_348", "浏览网页"),
    MARK_ENUMS_ACTIONTYPEENUM_350("mark.enums.actiontypeenum_350", "触发官网事件/属性"),
    MARK_ENUMS_ACTIONTYPEENUM_352("mark.enums.actiontypeenum_352", "通过手机号浏览短信下的链接"),
    MARK_ENUMS_ACTIONTYPEENUM_354("mark.enums.actiontypeenum_354", "收到服务号模板消息"),
    MARK_ENUMS_ACTIONTYPEENUM_355("mark.enums.actiontypeenum_355", "收到服务号图文消息"),
    MARK_ENUMS_ACTIONTYPEENUM_356("mark.enums.actiontypeenum_356", "查看外部链接"),
    MARK_ENUMS_ACTIONTYPEENUM_357("mark.enums.actiontypeenum_357", "转发外部链接"),
    MARK_ENUMS_ACTIONTYPEENUM_362("mark.enums.actiontypeenum_362", "查看视频号主页"),
    MARK_ENUMS_ACTIONTYPEENUM_366("mark.enums.actiontypeenum_366", "查看视频号视频"),
    MARK_ENUMS_ACTIONTYPEENUM_370("mark.enums.actiontypeenum_370", "查看视频号直播"),
    MARK_ENUMS_ACTIONTYPEENUM_372("mark.enums.actiontypeenum_372", "查看视频"),
    MARK_ENUMS_ACTIONTYPEENUM_373("mark.enums.actiontypeenum_373", "转发视频"),
    MARK_ENUMS_ACTIONTYPEENUM_378("mark.enums.actiontypeenum_378", "访问直播校验页"),
    MARK_ENUMS_ACTIONTYPEENUM_383("mark.enums.actiontypeenum_383", "在线客服"),
    MARK_ENUMS_ACTIONTYPEENUM_388("mark.enums.actiontypeenum_388", "观看直播"),
    MARK_ENUMS_ACTIONTYPEENUM_389("mark.enums.actiontypeenum_389", "报名/预约直播"),
    MARK_ENUMS_ACTIONTYPEENUM_390("mark.enums.actiontypeenum_390", "直播互动"),
    MARK_ENUMS_ACTIONTYPEENUM_391("mark.enums.actiontypeenum_391", "观看回放"),
    MARK_ENUMS_ACTIONTYPEENUM_392("mark.enums.actiontypeenum_392", "关注公众号"),
    MARK_ENUMS_ACTIONTYPEENUM_393("mark.enums.actiontypeenum_393", "取消关注公众号"),
    MARK_ENUMS_ACTIONTYPEENUM_394("mark.enums.actiontypeenum_394", "点击公众号菜单"),
    MARK_ENUMS_ACTIONTYPEENUM_395("mark.enums.actiontypeenum_395", "点击链接"),
    MARK_ENUMS_ACTIONTYPEENUM_396("mark.enums.actiontypeenum_396", "发送服务号消息"),
    MARK_ENUMS_ACTIONTYPEENUM_397("mark.enums.actiontypeenum_397", "扫描二维码"),
    MARK_ENUMS_ACTIONTYPEENUM_398("mark.enums.actiontypeenum_398", "邮件发送"),
    MARK_ENUMS_ACTIONTYPEENUM_399("mark.enums.actiontypeenum_399", "邮件打开"),
    MARK_ENUMS_ACTIONTYPEENUM_400("mark.enums.actiontypeenum_400", "邮件点击"),
    MARK_ENUMS_ACTIONTYPEENUM_401("mark.enums.actiontypeenum_401", "邮件取消订阅"),
    MARK_ENUMS_ACTIONTYPEENUM_402("mark.enums.actiontypeenum_402", "邮件垃圾举报"),
    MARK_ENUMS_ACTIONTYPEENUM_403("mark.enums.actiontypeenum_403", "无效邮件"),
    MARK_ENUMS_ACTIONTYPEENUM_404("mark.enums.actiontypeenum_404", "邮件软退回"),
    MARK_ENUMS_ACTIONTYPEENUM_405("mark.enums.actiontypeenum_405", "回复邮件"),
    MARK_ENUMS_ACTIONTYPEENUM_406("mark.enums.actiontypeenum_406", "收到短信"),
    MARK_ENUMS_ACTIONTYPEENUM_407("mark.enums.actiontypeenum_407", "访问广告落地页"),
    MARK_ENUMS_ACTIONTYPEENUM_408("mark.enums.actiontypeenum_408", "点击关键词"),
    MARK_ENUMS_ACTIONTYPEENUM_409("mark.enums.actiontypeenum_409", "搜索关键词"),
    MARK_ENUMS_ACTIONTYPEENUM_410("mark.enums.actiontypeenum_410", "添加企微员工"),
    MARK_ENUMS_ACTIONTYPEENUM_411("mark.enums.actiontypeenum_411", "删除企微员工"),
    MARK_ENUMS_ACTIONTYPEENUM_412("mark.enums.actiontypeenum_412", "加入群聊"),
    MARK_ENUMS_ACTIONTYPEENUM_413("mark.enums.actiontypeenum_413", "退出群聊"),
    MARK_ENUMS_ACTIONTYPEENUM_414("mark.enums.actiontypeenum_414", "领取优惠券"),
    MARK_ENUMS_ACTIONTYPEENUM_415("mark.enums.actiontypeenum_415", "核销优惠券"),
    MARK_ENUMS_ACTIONTYPEENUM_416("mark.enums.actiontypeenum_416", "参加优惠券活动"),
    MARK_ENUMS_ACTIONTYPEENUM_418("mark.enums.actiontypeenum_418", "拨打电话"),
    MARK_ENUMS_ACTIONTYPEENUM_420("mark.enums.actiontypeenum_420", "获取员工企业微信"),
    MARK_ENUMS_ACTIONTYPEENUM_422("mark.enums.actiontypeenum_422", "点击联系员工按钮"),
    MARK_ENUMS_ACTIONTYPEENUM_424("mark.enums.actiontypeenum_424", "播放视频"),
    MARK_ENUMS_ACTIONTYPEENUM_426("mark.enums.actiontypeenum_426", "点击保存手机"),
    MARK_ENUMS_ACTIONTYPEENUM_429("mark.enums.actiontypeenum_429", "收到企微群发消息"),
    MARK_ENUMS_ACTIONTYPEENUM_432("mark.enums.actiontypeenum_432", "朋友圈展现员工发布内容"),
    MARK_ENUMS_ACTIONTYPEENUM_433("mark.enums.actiontypeenum_433", "跳出"),

    MARK_ENUMS_ACTIONTYPEENUM_440("mark.enums.actiontypeenum_440", "发送文件到邮箱"),


    MARK_ENUMS_ACTIONTYPEENUM_434("mark.enums.actiontypeenum_434", "注册成为会员"),
    MARK_ENUMS_ACTIONTYPEENUM_435("mark.enums.actiontypeenum_435", "登录会员账号"),
    MARK_ENUMS_ACTIONTYPEENUM_436("mark.enums.actiontypeenum_436", "授权微信手机号"),
    MARK_ENUMS_ACTIONTYPEENUM_437("mark.enums.actiontypeenum_437", "授权微信头像昵称"),
    MARK_ENUMS_ACTIONTYPEENUM_438("mark.enums.actiontypeenum_438", "触发CTA组件，展示引导留咨页"),
    MARK_VO_MARKETINGTRIGGERVO_144("mark.vo.marketingtriggervo_144", "使用范围参数错误"),
    MARK_VO_MARKETINGTRIGGERVO_147("mark.vo.marketingtriggervo_147", "使用场景参数错误"),
    MARK_VO_MARKETINGTRIGGERVO_150("mark.vo.marketingtriggervo_150", "触发类型错误"),
    MARK_VO_MARKETINGTRIGGERVO_155("mark.vo.marketingtriggervo_155", "目标用户类型错误"),
    MARK_VO_MARKETINGTRIGGERVO_158("mark.vo.marketingtriggervo_158", "触发日期偏移量错误"),
    MARK_VO_MARKETINGTRIGGERVO_161("mark.vo.marketingtriggervo_161", "触发分钟数错误"),
    MARK_VO_MARKETINGTRIGGERVO_180("mark.vo.marketingtriggervo_180", "触发动作类型错误"),
    MARK_VO_MARKETINGTRIGGERVO_190("mark.vo.marketingtriggervo_190", "限制类型错误"),
    MARK_VO_MARKETINGTRIGGERVO_193("mark.vo.marketingtriggervo_193", "限制次数错误"),
    MARK_VO_MARKETINGTRIGGERVO_196("mark.vo.marketingtriggervo_196", "限制时常错误"),
    MARK_VO_MARKETINGTRIGGERVO_200("mark.vo.marketingtriggervo_200", "触发器动作不能为空"),
    MARK_VO_TRIGGERTASKVO_88("mark.vo.triggertaskvo_88", "任务名不能为空"),
    MARK_VO_TRIGGERTASKVO_91("mark.vo.triggertaskvo_91", "任务序列号不能为空"),
    MARK_VO_TRIGGERTASKVO_94("mark.vo.triggertaskvo_94", "任务类型错误"),
    MARK_VO_TRIGGERTASKVO_97("mark.vo.triggertaskvo_97", "任务执行类型错误"),
    MARK_VO_TRIGGERTASKVO_100("mark.vo.triggertaskvo_100", "任务延迟时间设置错误"),
    MARK_VO_TRIGGERTASKVO_104("mark.vo.triggertaskvo_104", "微信消息内容不能为空"),
    MARK_VO_TRIGGERTASKVO_107("mark.vo.triggertaskvo_107", "公众号不能为空"),
    MARK_VO_TRIGGERTASKVO_111("mark.vo.triggertaskvo_111", "模板消息内容设置错误"),
    MARK_VO_TRIGGERTASKVO_118("mark.vo.triggertaskvo_118", "短信模板错误"),
    MARK_VO_TRIGGERTASKVO_122("mark.vo.triggertaskvo_122", "邮件内容错误"),
    MARK_VO_TRIGGERTASKVO_126("mark.vo.triggertaskvo_126", "标签不能为空"),
    MARK_VO_TRIGGERTASKVO_130("mark.vo.triggertaskvo_130", "看板信息不能为空"),
    PARTNER_NOT_EXIST("mark.partner.not.exist", "互联用户不存在"),
    MOBILE_BOUND_UPSTREAM_EA("mark.mobile.bound.upstream.ea", "当前登录手机号已经在上游企业中绑定,暂时无法使用,绑定企业:"),
    MOBILE_BOUND_OTHER_PUBLIC_ENTERPRISE("mark.mobile.bound.other.public.enterprise", "当前登录手机号已经在其他企业中绑定,暂时无法使用,绑定企业:"),

    ;
    private String i18nKey;
    private String defaultValue;

    I18nKeyEnumV2(String i18nKey, String defaultValue) {
        this.i18nKey = i18nKey;
        this.defaultValue = defaultValue;
    }

    public String getI18nKey() {
        return i18nKey;
    }

    public String getDefaultValue() {
        return defaultValue;
    }
}
