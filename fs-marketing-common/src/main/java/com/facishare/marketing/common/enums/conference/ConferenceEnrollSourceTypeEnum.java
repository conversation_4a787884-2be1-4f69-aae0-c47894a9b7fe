package com.facishare.marketing.common.enums.conference;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created  By zhoux 2019/07/18
 **/
@AllArgsConstructor
@Getter
public enum ConferenceEnrollSourceTypeEnum {

    // 小程序
    MANKEEP(1, "小程序名片"),

    // 全员营销
    FULL_MARKETING(2, "全员推广"),

    // 公众号营销
    WX_OFFICIAL_ACCOUNTS_MARKETING(3, "公众号推广"),

    // 短信营销
    SMS_MARKETING(4, "短信推广"),

    // 二维码（邀请函）
    QR_CODE_INVITATION(5, "邀请函"),

    // 二维码（会议主页）
    QR_CODE_INDEX_PAGE(6, "会议主页"),

    // 二维码（签到）
    QR_CODE_SIGN_IN(7, "签到二维码 "),

    // 手动导入
    MANUAL_IMPORT(8, "手动导入"),

    // 二维码表单
    QR_CODE_FORM(9, "二维码表单"),

    // 企业微信小程序
    QYWX_MINIAPP(10, "企业微信小程序"),

    //从CRM同步数据
    SYNC_FROM_CRM(11, "从CRM同步"),

    // 活动成员同步
    SYNC_FROM_CRM_CAMPAIGN_OBJ(12, "活动成员同步"),

    // 会员一键报名
    MEMBER_ENROLL(13, "会员一键报名"),

    // 其它
    OTHER(99, "其它"),;

    public Integer type;
    public String desc;

    public static String getTypeDesc(int type) {
        String result = "未知";
        for (ConferenceEnrollSourceTypeEnum value : ConferenceEnrollSourceTypeEnum.values()) {
            if(value.getType() == type){
                return value.getDesc();
            }
        }
        return result;
    }


}
