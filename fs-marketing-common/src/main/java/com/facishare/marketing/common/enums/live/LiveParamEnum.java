package com.facishare.marketing.common.enums.live;

import com.facishare.marketing.common.enums.conference.ConferenceParamEnum;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.ReflectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
@Slf4j
public enum LiveParamEnum {

    VIEW_URL("直播链接", "viewUrl","url"),

    START_TIME("直播开始时间", "startTime","date_time"),

    LECTURE("讲师", "lectureName","text"),

    END_TIME("直播结束时间", "endTime","date_time"),

    TITLE("直播标题", "title","text"),;

    private String desc;

    private String attributes;

    private String filedType;


    public static List<LiveParamEnum> getNeedReplaceByContent(String content) {
        List<LiveParamEnum> liveParamEnumArrayList = Lists.newArrayList();
        if (StringUtils.isBlank(content)) {
            return liveParamEnumArrayList;
        }
        Arrays.stream(LiveParamEnum.values()).forEach(data -> {
            if (content.contains("{" + data.getDesc() + "}")) {
                liveParamEnumArrayList.add(data);
            }
        });
        return liveParamEnumArrayList;
    }

    public static String replaceContent(String content, List<LiveParamEnum> needReplaceParam, Object object) {
        for (LiveParamEnum liveParamEnum : needReplaceParam) {
            try {
                Object value = ReflectionUtil.getFieldValue(liveParamEnum.getAttributes(), object);
                if (value != null) {
                    value = timeFormat(liveParamEnum, value);
                    content = content.replaceAll("\\{" + liveParamEnum.getDesc() + "}", value.toString());
                } else {
                    content = content.replaceAll("\\{" + liveParamEnum.getDesc() + "}", "");
                }
            } catch (Exception e) {
                log.warn("exception:",  e);
            }
        }
        return content;
    }

    public static Object timeFormat(LiveParamEnum liveParamEnum, Object value) {
        if (liveParamEnum == null || value == null) {
            return "";
        }
        if (liveParamEnum == LiveParamEnum.START_TIME || liveParamEnum == LiveParamEnum.END_TIME) {
            return DateUtil.format((Date) value);
        }
        return value;
    }

    public static Map<String, String> conversionBaseParamByObject(Object object, boolean needAddCurlyBraces) {
        Map<String, String> result = Maps.newHashMap();
        for (LiveParamEnum liveParamEnum : LiveParamEnum.values()) {
            try {
                Object value = ReflectionUtil.getFieldValue(liveParamEnum.getAttributes(), object);
                if (value != null) {
                    value = timeFormat(liveParamEnum, value);
                    if (needAddCurlyBraces) {
                        result.put("{" + liveParamEnum.getDesc() + "}", value.toString());
                    } else {
                        result.put(liveParamEnum.getDesc(), value.toString());
                    }
                }
            } catch (Exception ignored) {
            }
        }
        return result;
    }

}
