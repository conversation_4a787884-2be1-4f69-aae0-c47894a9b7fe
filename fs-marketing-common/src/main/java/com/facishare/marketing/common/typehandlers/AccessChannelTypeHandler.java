package com.facishare.marketing.common.typehandlers;

import com.facishare.marketing.common.typehandlers.value.AccessChannel;
import com.facishare.marketing.common.util.GsonUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class AccessChannelTypeHandler extends BaseTypeHandler<AccessChannel> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, AccessChannel parameter, JdbcType jdbcType) throws SQLException {
        PGobject pGobject = new PGobject();
        pGobject.setType("jsonb");
        pGobject.setValue(GsonUtil.getGson().toJson(parameter));
        preparedStatement.setObject(i, pGobject);
    }

    @Override
    public AccessChannel getNullableResult(ResultSet resultSet, String columnName) throws SQLException {
        return GsonUtil.getGson().fromJson(resultSet.getString(columnName), AccessChannel.class);
    }

    @Override
    public AccessChannel getNullableResult(ResultSet resultSet, int i) throws SQLException {
        return GsonUtil.getGson().fromJson(resultSet.getString(i), AccessChannel.class);
    }

    @Override
    public AccessChannel getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        return GsonUtil.getGson().fromJson(callableStatement.getString(i), AccessChannel.class);
    }
}
