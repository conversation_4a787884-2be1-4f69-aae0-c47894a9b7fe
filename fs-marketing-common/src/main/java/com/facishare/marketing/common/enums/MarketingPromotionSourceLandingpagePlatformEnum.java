package com.facishare.marketing.common.enums;

import lombok.Getter;

@Getter
public enum MarketingPromotionSourceLandingpagePlatformEnum {

    JI_MU_YU("基木鱼", "jimuyu"),
    CHENG_ZI_JIAN_ZHAN("橙子建站", "ch<PERSON><PERSON><PERSON><PERSON><PERSON>"),
    TENCENT("腾讯", "tencent"),
    YXT("营销通", "yxt"),
    OFFICIAL_WEBSITE("官网", "official_website"),
    OTHER("其他", "other"),

    ;

    MarketingPromotionSourceLandingpagePlatformEnum(String platformName, String platformValue) {
        this.platformName = platformName;
        this.platformValue = platformValue;
    }

    private String platformValue;

    private String platformName;
}
