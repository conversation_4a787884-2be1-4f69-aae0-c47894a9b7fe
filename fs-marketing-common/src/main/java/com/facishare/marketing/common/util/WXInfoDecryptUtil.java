/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.common.util;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.common.encryption.Pkcs7EncoderUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class WXInfoDecryptUtil {

    public static JSONObject decryptUserInfo(String encryptedData, String iv, String sessionKey, String uid) throws Exception {
        if (StringUtils.isNotBlank(encryptedData) && StringUtils.isNotBlank(iv) && StringUtils.isNotBlank(sessionKey)) {
            return decryptData(encryptedData, iv, sessionKey, uid);
        } else {
            throw new IllegalArgumentException("decrypt failed:params error."
                + " uid:" + uid
                + " iv:" + iv
                + " sessionKey:" + sessionKey
                + " encryptedData:" + encryptedData);
        }
    }

    public static String decryptUserPhone(String encryptedData, String iv, String sessionKey, String uid) throws Exception {
        if (StringUtils.isNotBlank(encryptedData) && StringUtils.isNotBlank(iv) && StringUtils.isNotBlank(sessionKey)) {
            JSONObject jsonObject = decryptData(encryptedData, iv, sessionKey, uid);
            String purePhoneNumber = jsonObject.getString("purePhoneNumber");
            if (StringUtils.isBlank(purePhoneNumber)) {
                throw new IllegalArgumentException("decrypt failed:purePhoneNumber is not found."
                    + " uid:" + uid
                    + " jsonObject:" + jsonObject.toJSONString()
                    + " iv:" + iv
                    + " sessionKey:" + sessionKey
                    + " encryptedData:" + encryptedData);
            }
            return purePhoneNumber;
        } else {
            throw new IllegalArgumentException("decrypt failed:params error."
                + " uid:" + uid
                + " iv:" + iv
                + " sessionKey:" + sessionKey
                + " encryptedData:" + encryptedData);
        }
    }

    public static String decryptGroupId(String encryptedData, String iv, String sessionKey, String uid) throws Exception {
        if (StringUtils.isNotBlank(encryptedData) && StringUtils.isNotBlank(iv) && StringUtils.isNotBlank(sessionKey)) {
            JSONObject jsonObject = decryptData(encryptedData, iv, sessionKey, uid);
            String openGid = jsonObject.getString("openGId");
            if (StringUtils.isBlank(openGid)) {
                throw new IllegalArgumentException("decrypt failed:openGid is not found."
                    + " uid:" + uid
                    + " jsonObject:" + jsonObject.toJSONString()
                    + " iv:" + iv
                    + " sessionKey:" + sessionKey
                    + " encryptedData:" + encryptedData);
            }
            return openGid;
        } else {
            throw new IllegalArgumentException("decrypt failed:params error."
                + " uid:" + uid
                + " iv:" + iv
                + " sessionKey:" + sessionKey
                + " encryptedData:" + encryptedData);
        }
    }

    private static JSONObject decryptData(String encryptedData, String iv, String sessionKey, String uid) throws Exception {
        JSONObject jsonObject = Pkcs7EncoderUtil.decryptWX(sessionKey, encryptedData, iv);
        if (jsonObject == null) {
            throw new IllegalStateException("decrypt failed:jsonObject is null."
                + " uid:"+ uid
                + " iv:" + iv
                + " sessionKey:" + sessionKey
                + " encryptedData:" + encryptedData);
        }
        return jsonObject;
    }

    public static void main(String[] args) {
        try {
            String encryptedData = "k7bBmnmrHbVtj/ksgpNz0pXJjcoA3sZMkm2Z4KF7ZltbC0g2jzc9VTEclDKmftuu5jl6EgOxY0w2XinCCN3AdsTgyn0NS2JR4dh8Y5TSgZn+Ww7mjIIkVudZT0mJPvUJizEU+SLjjsnbwXMlmPuIAT3J0evWsNpavPlp3e6Dgfka1Qo2d/ZbI8rbcl7ibIyEzA0XoJP1Y00gZ/08IJbi9g==";
            String iv = "D3WZT3A0RuvhGl8Nz3G4Hw==";
            String sessionKey = "4ivMWyWYtHcs58wDiwCJew==";
            JSONObject jsonObject = decryptData(encryptedData, iv, sessionKey, null);
            System.out.println("result:" + jsonObject.toJSONString());
        } catch (Exception e) {
            log.warn("exception:",  e);
        }
    }
}
