package com.facishare.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/1/8 16:02
 * @Version 1.0
 */
@Getter
public enum  ResponseMsgTypeEnum implements Serializable {
    /** 文本消息 */
    TEXT("text", 1),
    /** 图片消息 */
    IMAGE("image", 2),
    /** 图文外链 */
    MATERIAL("material", 3),
    /** 图文消息 */
    MP_NEWS("mpnews", 5)
    ;

    private String type;
    private Integer wxCustomerServiceMsgType;

    ResponseMsgTypeEnum(String type, Integer wxCustomerServiceMsgType) {
        this.type = type;
        this.wxCustomerServiceMsgType = wxCustomerServiceMsgType;
    }

    public static Integer toWxCustomerServiceMsgType(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (ResponseMsgTypeEnum element : ResponseMsgTypeEnum.values()) {
            if (element.getType().equals(type)) {
                return element.getWxCustomerServiceMsgType();
            }
        }
        return null;
    }
}
