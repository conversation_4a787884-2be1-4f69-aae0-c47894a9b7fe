package com.facishare.marketing.common.contstant;

import com.google.common.collect.ImmutableList;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.List;
@Getter
@ToString
@AllArgsConstructor
public enum MiniappSubscribeMessageEnum {
    PAY_ORDER_CREATED("1625", "待付款提醒", ImmutableList.of(2, 3, 4), "待付款提醒（系统）", "payOrderToPayTemplateId"),
    PAY_ORDER_PAID("1754", "支付成功通知", ImmutableList.of(8, 2, 1), "支付成功通知（系统）", "payOrderPaidTemplateId"),
    ENROLL_RESULT_NOTICE("401", "报名结果通知", ImmutableList.of(3, 4, 5, 6), "报名结果通知（系统）", "enrollResultNoticeTemplateId"),
    FORM_SUBMIT_NOTICE("1585", "表单提交通知", ImmutableList.of(2, 3), "表单提交通知（系统）", "formSubmitNoticeTemplate"),
    REVIEW_RESULT_NOTICE("402", "审核结果通知", ImmutableList.of(2, 19, 17), "审核结果通知（系统）", "reviewResultNoticeTemplateId"),
    PRIVATE_MESSAGE_NOTICE("4774", "收到留言通知", ImmutableList.of(3, 1, 2), "收到留言通知（系统）", "privateMessageNoticeTemplateId");

    private String tid;
    private String title;
    private List<Integer> kidList;
    private String sceneDesc;
    private String resultIdName;

    MiniappSubscribeMessageEnum(String tid, String title, List<Integer> kidList, String sceneDesc) {
        this.tid = tid;
        this.title = title;
        this.kidList = kidList;
        this.sceneDesc = sceneDesc;
    }
}
