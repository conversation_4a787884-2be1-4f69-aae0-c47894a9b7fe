package com.facishare.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created  By zhoux 2019/10/15
 **/
@Getter
@AllArgsConstructor
public enum ImportErrorEnum {

    SUCCESS(0, "成功"),

    REQUIRED_FIELD_IS_EMPTY(1, "必填项未填"),

    RADIO_OPTION_DOES_NOT_EXIST(2 ,"单选项不存在"),

    MULTIPLE_SELECTION_OPTION_DOES_NOT_EXIST(3, "多选选项不存在"),

    TIME_FORMAT_ERROR(4, "时间格式有误"),

    NUMBER_FORMAT_ERROR(5, "数值类型错误"),

    PHONE_FORMAT_ERROR(6, "手机号格式有误"),

    EMAIL_FORMAT_ERROR(7, "邮箱格式有误"),

    CONFERENCE_INVITE_NAME_ERROR(8, "邀约人昵称有误"),

    DATA_EXIST_ERROR(9, "数据重复或已存在"),

    PHONE_DATA_EXIST_ERROR(10, "手机号重复"),

    IMAGE_TYPE_DATA_NOT_SUPPORT(11, "图片字段暂不支持导入"),

    PROVINCE_DATA_ERROR(12, "省份数据有误"),

    CITY_DATA_ERROR(12, "城市数据有误"),

    DISTRICT_DATA_ERROR(13, "区数据有误"),

    COUNTRY_DATA_ERROR(14, "国家数据有误"),

    SYSTEM_ERROR(999, "系统错误");

    private Integer type;

    private String desc;

}
