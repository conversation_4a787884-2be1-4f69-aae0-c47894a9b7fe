package com.facishare.marketing.common.typehandlers.value;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Setter
@ToString
public class NestedId implements Serializable {
    private String id;
    private List<NestedId> sub;

    public NestedId() {
    }

    public NestedId(String id) {
        this.id = id;
    }

    public void pushSubId(String subId){
        if (this.sub == null){
            this.sub = new NestedIdList();
        }
        NestedId nestedId = new NestedId(subId);
        if(!this.sub.contains(nestedId)){
            this.sub.add(nestedId);
        }
    }

    public void pushSubIds(List<String> subIds){
        if (subIds != null){
            for (String subId : subIds) {
                pushSubId(subId);
            }
        }
    }


    public void pushSubNestedIds(List<NestedId> subIds){
        if (subIds != null){
            for (NestedId subNestedId : subIds) {
                pushSubId(subNestedId.getId());
            }
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        NestedId nestedId = (NestedId) o;
        return Objects.equals(id, nestedId.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    public void mergeNestedIdToLast(NestedId nestedId){
        nestedId = nestedId.deepCopy();
        if(this.id.equals(nestedId.getId()) && nestedId.sub != null){
            if(this.sub == null){
                this.sub = nestedId.sub;
                return;
            }
            Map<String, NestedId> subNestedIdMap = nestedId.sub.stream().collect(Collectors.toMap(NestedId::getId, v -> v, (v1, v2) -> v1));
            for (NestedId subNestedId : this.sub) {
                if(subNestedIdMap.containsKey(subNestedId.getId())){
                    subNestedId.mergeNestedIdToLast(subNestedIdMap.get(subNestedId.getId()));
                }
            }
            nestedId.sub.removeAll(this.sub);
            this.sub.addAll(nestedId.sub);
        }
    }

    public NestedId deepCopy(){
        NestedId nestedId = new NestedId();
        nestedId.setId(this.getId());
        if(this.sub != null){
            List<NestedId> nestedIds = this.sub.stream().map(NestedId::deepCopy).collect(Collectors.toList());
            nestedId.setSub(nestedIds);
        }
        return nestedId;
    }
}
