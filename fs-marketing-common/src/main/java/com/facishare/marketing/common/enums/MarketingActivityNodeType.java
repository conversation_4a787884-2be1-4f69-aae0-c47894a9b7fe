package com.facishare.marketing.common.enums;

import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@ToString
public enum MarketingActivityNodeType {
    /** 等待用户更新CRM属性, 不支持 */
    @Deprecated
    CRM_PROPERTY_UPDATE(1, MarketingFlowTaskType.USER_TODO_TASK),
    /** 点击链接: 不支持*/
    @Deprecated
    CLICK_LINK(2, MarketingFlowTaskType.USER_TODO_TASK),
    /** 提交表单 */
    SUBMIT_FORM(3, MarketingFlowTaskType.USER_TODO_TASK),
    /** 活动签到 */
    @Deprecated
    CHECK_IN_ACTIVITY(4, MarketingFlowTaskType.USER_TODO_TASK),
    /** 发送服务号消息 */
    SEND_WX_MESSAGE(5, MarketingFlowTaskType.SYSTEM_TASK),
    /** 发送短信消息 */
    SEND_SMS_MESSAGE(6, MarketingFlowTaskType.SYSTEM_TASK),
    /** 添加标签 */
    ADD_TAG(7, MarketingFlowTaskType.SYSTEM_TASK),
    /** 移除标签 */
    REMOVE_TAG(8, MarketingFlowTaskType.SYSTEM_TASK),
    /** 等待 */
    WAIT(9, MarketingFlowTaskType.SYSTEM_TASK),
    /** 更新CRM属性 */
    UPDATE_CRM_PROPERTY(10, MarketingFlowTaskType.SYSTEM_TASK),
    /** 创建定时等待节点的结果对象 */
    CREATE_LATENCY_RESULT_OBJECT_DATA(11, MarketingFlowTaskType.SYSTEM_TASK),
    /** 回复微信消息 */
    RESPONSE_WX_MESSAGE(12, MarketingFlowTaskType.USER_TODO_TASK),
    /** 会议签到 */
    CHECK_IN_CONFERENCE(13, MarketingFlowTaskType.USER_TODO_TASK),
    /** 会议报名 */
    ENROLL_CONFERENCE(14, MarketingFlowTaskType.USER_TODO_TASK),
    /** CRM属性判断节点 */
    CRM_PROPERTY_JUDGE(15, MarketingFlowTaskType.SYSTEM_TASK),
    /** 发送邮件 */
    SEND_EMAIL(16, MarketingFlowTaskType.SYSTEM_TASK),

    /** 启动节点 */
    START(1000001, MarketingFlowTaskType.SYSTEM_TASK),
    /** 结束节点 */
    END(1000002, MarketingFlowTaskType.SYSTEM_TASK),
    /** 网关节点 */
    EXCLUSIVE_GATE_WAY(1000003, MarketingFlowTaskType.SYSTEM_TASK)
    ;
    private final int type;
    private final MarketingFlowTaskType marketingFlowTaskType;

    MarketingActivityNodeType(int type, MarketingFlowTaskType marketingFlowTaskType) {
        this.type = type;
        this.marketingFlowTaskType = marketingFlowTaskType;
    }

    public static boolean isValid(Integer type) {
        if (type == null) {
            return false;
        }
        for (MarketingActivityNodeType value : values()) {
            if (value.getType() == type) {
                return true;
            }
        }
        return false;
    }

    public static MarketingActivityNodeType fromType(Integer type) {
        Preconditions.checkArgument(type != null);
        for (MarketingActivityNodeType value : values()) {
            if (type == value.getType()) {
                return value;
            }
        }
        throw new IllegalArgumentException();
    }
}
