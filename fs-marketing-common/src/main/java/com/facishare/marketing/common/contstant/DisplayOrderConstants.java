package com.facishare.marketing.common.contstant;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;

public interface DisplayOrderConstants {
    String TAG_MODEL_DISPLAY_KEY = "ALL_TAG_MODEL";
    String TAG_IN_MODEL_PREFIX_KEY = "TAG_IN_MODEL_";
    String TAG_MODEL_MATERIAL_DISPLAY_KEY = "MATERIAL_TAG_MODEL";
    String CARD_LIST_IN_BOARD = "CARD_LIST_IN_BOARD_";
    String CARD_IN_CARD_LIST = "CARD_IN_CARD_LIST_";
    String TASK_IN_CARD = "TASK_IN_CARD_";
    String HEXAGON_TEMPLATE_GROUP_DISPLAY_KEY = "HEXAGON_TEMPLATE_GROUP_";   //微页面模板分类
    String HEXAGON_GROUP_DISPLAY_KEY = "HEXAGON_GROUP_";                     //微页面分组
    String FILE_GROUP_DISPLAY_KEY = "FILE_GROUP_";                           //文件分组
    String PHOTO_GROUP_DISPLAY_KEY = "PHOTO_GROUP_";                         //图片分组

    String COUPON_TEMPLATE_GROUP_DISPLAY_KEY = "COUPON_TEMPLATE_GROUP_";     //优惠券分组
    String ARTICLE_GROUP_DISPLAY_KEY = "ARTICLE_GROUP_";                     //文章分组
    String PRODUCT_GROUP_DISPLAY_KEY = "PRODUCT_GROUP_";                     //产品分组
    String CUSTOMIZE_FORM_GROUP_DISPLAY_KEY = "CUSTOMIZE_FORM_GROUP_";       //线索表单分组
    String QR_POSTER_GROUP_DISPLAY_KEY = "QR_POSTER_GROUP_";                 //海报分组
    String VIDEO_GROUP_DISPLAY_KEY = "VIDEO_GROUP_";                         //视频分组
    String QY_GROUP_CODE_GROUP_DISPLAY_KEY = "QY_GROUP_CODE_GROUP_";         //企微群活码分组
    String FAN_CODE_GROUP_DISPLAY_KEY = "FAN_CODE_GROUP_";                   //员工活码分组
    String MARKETING_USER_GROUP_DISPLAY_KEY = "MARKETING_USER_GROUP_";       //目标人群
    String QY_WELCOME_MSG_GROUP_DISPLAY_KEY = "QY_WELCOME_MSG_GROUP_";       //企微欢迎语
    String QY_SOP_GROUP_DISPLAY_KEY = "QY_SOP_GROUP_";                       //企微SOP
    String QY_GROUP_SOP_GROUP_DISPLAY_KEY = "QY_GROUP_SOP_GROUP_";                       //企微群SOP


    static String getTagDisplayKey(String tagModelId){
        return doGetDisplayKeyByPrefixAndId(TAG_IN_MODEL_PREFIX_KEY, tagModelId);
    }

    static String getCardListDisplayKey(String boardId){
        return doGetDisplayKeyByPrefixAndId(CARD_LIST_IN_BOARD, boardId);
    }

    static String getCardDisplayKey(String cardListId){
        return doGetDisplayKeyByPrefixAndId(CARD_IN_CARD_LIST, cardListId);
    }

    static String getTaskDisplayKey(String cardId){
        return doGetDisplayKeyByPrefixAndId(TASK_IN_CARD, cardId);
    }

    static String doGetDisplayKeyByPrefixAndId(String prefix, String id){
        Preconditions.checkArgument(!Strings.isNullOrEmpty(prefix) && !Strings.isNullOrEmpty(id));
        return prefix + id;
    }

    static boolean checkDisplayKey(String displayKey){
        return displayKey != null && (displayKey.equals(TAG_MODEL_DISPLAY_KEY)
                || displayKey.startsWith(TAG_IN_MODEL_PREFIX_KEY)
                || displayKey.startsWith(TAG_MODEL_MATERIAL_DISPLAY_KEY)
                || displayKey.startsWith(CARD_LIST_IN_BOARD)
                || displayKey.startsWith(CARD_IN_CARD_LIST)
                || displayKey.startsWith(TASK_IN_CARD)
                || displayKey.startsWith(HEXAGON_GROUP_DISPLAY_KEY)
                || displayKey.startsWith(HEXAGON_TEMPLATE_GROUP_DISPLAY_KEY)
                || displayKey.startsWith(FILE_GROUP_DISPLAY_KEY)
                || displayKey.startsWith(PHOTO_GROUP_DISPLAY_KEY)
                || displayKey.startsWith(COUPON_TEMPLATE_GROUP_DISPLAY_KEY)
                || displayKey.startsWith(ARTICLE_GROUP_DISPLAY_KEY)
                || displayKey.startsWith(PRODUCT_GROUP_DISPLAY_KEY)
                || displayKey.startsWith(CUSTOMIZE_FORM_GROUP_DISPLAY_KEY)
                || displayKey.startsWith(QR_POSTER_GROUP_DISPLAY_KEY)
                || displayKey.startsWith(VIDEO_GROUP_DISPLAY_KEY)
                || displayKey.startsWith(QY_GROUP_CODE_GROUP_DISPLAY_KEY)
                || displayKey.startsWith(FAN_CODE_GROUP_DISPLAY_KEY)
                || displayKey.startsWith(MARKETING_USER_GROUP_DISPLAY_KEY)
                || displayKey.startsWith(QY_WELCOME_MSG_GROUP_DISPLAY_KEY)
                || displayKey.startsWith(QY_SOP_GROUP_DISPLAY_KEY)
                || displayKey.startsWith(QY_GROUP_SOP_GROUP_DISPLAY_KEY)
        );
    }
}
