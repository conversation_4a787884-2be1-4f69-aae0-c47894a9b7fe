package com.facishare.marketing.common.typehandlers.value;

import java.io.Serializable;

import com.facishare.marketing.common.enums.hexagon.MemberCheckTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created  By zhoux 2019/04/04
 **/
@Data
public class FormMoreSetting implements Serializable{

    /**
     * 同步crm
     */
    private boolean synchronousCRM;

    /**
     * 推广人 -> 线索负责人
     */
    private boolean chargePerson;

    /**
     * 提交一次
     */
    private boolean fillInOnce;

    /**
     * 校验是否为会员
     */
    private Boolean checkMember = false;

    /**
     * 报名限制
     */
    private boolean enrollLimit;

    /**
     * 报名限制数量
     */
    private Long enrollLimitNum;

    /**
     * 报名限制提示
     */
    private String enrollLimitText;
    
    @ApiModelProperty("是否同步到会员(是否自动生成会员)，只有在synchronousCRM为true且配置了会员映射的情况下生效，并且要勾选自动生成会员的按钮")
    private boolean syncToMember = false;

    /**
     * 提交跳转类型
     */
    private String submitJumpType;

    /**
     * 会员识别类型
     * 0:不自动识别身份，需要再次填写提交表单
     * 1:自动识别会员身份，跳过表单填写，无法收集额外信息
     * 2: 自动识别并填充会员基础信息（姓名、手机、邮箱、公司、职务）到表单，用户仅填写额外信息即可
     */
    private Integer memberCheckType;

    public Integer getMemberCheckType() {
        if (memberCheckType == null) {
            if (getCheckMember() == null) {
                memberCheckType =  MemberCheckTypeEnum.NO.getType();
            } else {
                //兼容以前的企业,并进行赋值新字段
                memberCheckType = getCheckMember() ? MemberCheckTypeEnum.CHECK.getType() : MemberCheckTypeEnum.NO.getType();
            }
        }
        return memberCheckType;
    }

    /**
     * 存入对象类型 0:线索对象 1:其他任意对象
     */
    private Integer saveCrmObjectType = 0;
}
