package com.facishare.marketing.common.contstant.sms;

/**
 * Created by zhengh on 2018/12/20.
 */
public class SmsContants {
    public static final int SIGNATURE_COUNT_PER_APPID = 200;   //每个appid下签名数量

    public static final int TEMPLATE_COUNT_PER_APPID = 1000;   //每个appid下模板数量

    public static final int SIGNATURE_COUNT_PER_EA = 1;        //每个公司只能申请一个签名

    public static final int SIGNATURE_MAX_MODIFY_COUNT = 3;    //签名每月最多修改次数

    public static final int ONE_SMS_CONTENT_LENGTH = 67;       //每条短信字符数量

    public static final int MORE_THAN_CONTENT_COUNT = 70;       //超过70个字，按67个算

    public static final int TEMPLATE_COUNT_PER_EA = 10;        //每个公司只能申请10个模板

    public static final int SIGNATURE_MIN_LENGTH = 2;          //签名最少字数

    public static final int SIGNATURE_MAX_LENGTH = 8;          //签名最大字数

    public static final int MAXIMUM_SMS_CONTENT_WORD_NUMBER = 450;  //最大短信字数450

    public static final int MW_SMS_CONTENT_WORD_LIMIT = 1000;  //梦网最大短信字数1000字

    public static final int MW_SMS_BATCH_SEND_LIMIT = 1000;  //梦网固定内容单次发送最大人数

    public static final int MW_SMS_MULTI_SEND_LIMIT = 100;  //梦网动态内容单次发送最大人数

}
