package com.facishare.marketing.common.enums;

import lombok.Getter;

@Getter
public enum BrowserUserThirdPlateFormTypeEnum {
    KF(1, "53kf"),
    AIFANFAN(2,"百度")
    ;

    private Integer type;
    private String des;

    BrowserUserThirdPlateFormTypeEnum(Integer type, String des) {
        this.type = type;
        this.des = des;
    }

    public static boolean isValid(Integer type) {
        for (BrowserUserThirdPlateFormTypeEnum value : BrowserUserThirdPlateFormTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return true;
            }
        }
        return false;
    }
}
