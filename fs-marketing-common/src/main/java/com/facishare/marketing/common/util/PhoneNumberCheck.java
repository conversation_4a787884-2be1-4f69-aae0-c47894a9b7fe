/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.common.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

/**
 * Created by zhengh on 2018/5/8. 手机号码合法性验证
 */
public class PhoneNumberCheck {
    public static boolean isPhoneLegal(String str) throws PatternSyntaxException {
        return isChinaPhoneLegal(str) || isHKPhoneLegal(str);
    }

    /**
     * 大陆手机号码11位数，匹配格式：前三位固定格式+后8位任意数 此方法中前三位格式有： 13+任意数 15+除4的任意数 18+除1和4的任意数 17+除9的任意数 147
     */
    private static boolean isChinaPhoneLegal(String str) throws PatternSyntaxException {
        String regExp = "^[1][1-9][0-9]\\d{8}$";
        Pattern p = Pattern.compile(regExp);
        Matcher m = p.matcher(str);
        return m.matches();
    }

    /**
     * 香港手机号码8位数，5|6|8|9开头+7位任意数
     */
    private static boolean isHKPhoneLegal(String str) throws PatternSyntaxException {
        String regExp = "^(5|6|8|9)\\d{7}$";
        Pattern p = Pattern.compile(regExp);
        Matcher m = p.matcher(str);
        return m.matches();
    }

    public static boolean validateGlobalPhoneNumber(String phoneNumber) {
        // 定义正则表达式模式
        String pattern = "^\\+?[1-9]\\d{0,2}-?\\d{7,15}$";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(phoneNumber);
        return m.matches();
    }

    public static boolean isChinaMobileNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return false;
        }

        String chinaMobilePattern = "^("
                + "134[0-8]\\d{7}"
                + "|13[5-9]\\d{8}"
                + "|147\\d{8}"
                + "|15[0-27-9]\\d{8}"
                + "|165\\d{8}"
                + "|17[02356]\\d{8}"
                + "|18[2-47-8]\\d{8}"
                + "|19[58]\\d{8}"
                + "|1440\\d{7}"
                + "|170[34567]\\d{7}"
                + ")$";

        return Pattern.matches(chinaMobilePattern, phoneNumber);
    }

    public static void main(String[] args) {
        String testNumber = "18219815498";
        System.out.println(isChinaMobileNumber(testNumber)); // 输出：true
    }

}
