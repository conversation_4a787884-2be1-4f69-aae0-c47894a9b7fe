package com.facishare.marketing.common.contstant;

import java.util.Set;

public interface RoleConstant {
    /** 系统管理员角色ID */
    String SYSTEM_ADMIN_ROLE_ID = "super-administrator";
    /** 市场活动策划 */
    String MARKETING_PLANNING = "marketing-planning";
    /** SDR线索清洗 */
    String SDR_LEADS_CLEANING = "SDR-leads-cleaning";
    /** 用户运营 */
    String USER_OPERATION = "user-operation";
    /** 会员运营 */
    String MEMBER_OPERATION = "member-operation";
    /** 公众号运营 */
    String WECHAT_OPERATION = "wechat-operation";
    /** 企业微信运营 */
    String WECHAT_WORK_OPERATION = "wechat-work-operation";
    /** 企业分销员 */
    String CORPORATE_DISTRIBUTOR = "corporate-distributor";
    /** 内容制作人员 */
    String CONTENT_PRODUCER = "content-producer";
    /** 小程序运营 */
    String MINIPROGRAM_OPERATION = "miniprogram-operation";
    /** 官网运营 */

    String OFFICIAL_WEBSITE_OPERATION = "official-website-operation";
    /** 广告运营 */
    String ADVERTISING_OPERATION = "advertising-operation";
    /** 数据分析管理员 */
    String DATA_ANALYSIS_ADMINISTRATOR = "data-analysis-administrator";
    /** 会议活动策划 */
    String CONFERENCE_PLANNING = "conference-planning";
    /** 线上活动策划 */
    String CONTENT_MARKET_PLANNING = "content-market-planning";
    /** 直播活动策划 */
    String LIVING_MARKET_PLANNING = "living-market-planning";
    /** 全员营销策划 */
    String EMPLOYEE_SPREAD_MARKET_PLANNING = "employee-spread-market-planning";
    /** 短信营销 */
    String SMS_MARKET_PLANNING = "sms-market-planning";
    /** 邮件营销 */
    String EMAIL_MARKET_PLANNING = "email-market-planning";

    /** 伙伴营销 */
    String PARTNER_MARKET_PLANNING = "partner-market-planning";
    /** whatsapp营销 */
    String WHATSAPP_MARKET = "whatsapp-marketing";
    /** 会员营销 */
    String MEMBER_MARKETING = "member-marketing";
    // SDR人员
    String MARKETING_SDR = "marketing-sdr";
}