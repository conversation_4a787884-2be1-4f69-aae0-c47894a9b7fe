package com.facishare.marketing.common.typehandlers;

import com.facishare.marketing.common.typehandlers.value.CrmPropertyJudgeRuleList;
import com.facishare.marketing.common.util.GsonUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * @author: doggy1853
 * @date: 2019/2/13
 * @Description:
 */
public class CrmPropertyJudgeRuleListTypeHandler extends BaseTypeHandler<CrmPropertyJudgeRuleList> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, CrmPropertyJudgeRuleList crmPropertyJudgeRules, JdbcType jdbcType) throws SQLException {
        PGobject pgObject = new PGobject();
        pgObject.setType("jsonb");
        pgObject.setValue(GsonUtil.toJson(crmPropertyJudgeRules));
        preparedStatement.setObject(i, pgObject);
    }

    @Override
    public CrmPropertyJudgeRuleList getNullableResult(ResultSet resultSet, String s) throws SQLException {
        if (null != resultSet.getString(s)) {
            return GsonUtil.fromJson(resultSet.getString(s), CrmPropertyJudgeRuleList.class);
        } return null;
    }

    @Override
    public CrmPropertyJudgeRuleList getNullableResult(ResultSet resultSet, int i) throws SQLException {
        if (null != resultSet.getString(i)) {
            return GsonUtil.fromJson(resultSet.getString(i), CrmPropertyJudgeRuleList.class);
        }
        return null;
    }

    @Override
    public CrmPropertyJudgeRuleList getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        if(null!=callableStatement.getString(i)){
            return GsonUtil.fromJson(callableStatement.getString(i), CrmPropertyJudgeRuleList.class);
        }
        return null;
    }
}
