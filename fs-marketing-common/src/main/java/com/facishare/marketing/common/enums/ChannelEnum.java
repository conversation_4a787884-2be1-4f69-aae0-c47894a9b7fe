package com.facishare.marketing.common.enums;

import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/3/6.
 */
@Getter
@AllArgsConstructor
public enum ChannelEnum {
    // 微信小程序渠道
    MINIAPP(1, "MINIAPP"),
    // 公众号身份
    WX_SERVICE(2, "WX_SERVICE"),
    CRM_CONTACT(3, "CRM_CONTACT"),
    CRM_LEAD(4, "CRM_LEAD"),

    CRM_WX_USER(5, "CRM_WX_USER"),
    CRM_ACCOUNT(6, "CRM_ACCOUNT"),
    CRM_WX_USER_WITH_WX_APPID_AND_WX_OPENID(7, "CRM_WX_USER_WITH_WX_APPID_AND_WX_OPENID"),
    // 浏览器身份
    BROWSER_USER(8, "BROWSER_USER"),
    CRM_WX_WORK_EXTERNAL_USER(9, "CRM_WX_WORK_EXTERNAL_USER"),
    WX_WORK_EXTERNAL_USER(10, "CRM_WX_WORK_EXTERNAL_USER"),
    WX_WORK_MINIAPP_USER(11, "CRM_WX_WORK_MINIAPP_USER"),
    CRM_MEMBER(12, "CRM_MEMBER"),

    //支持自定义对象
    CUSTOMIZE_OBJECT(20, "CUSTOMIZE_OBJECT")
    ;
    private Integer type;
    private String description;

    public static ChannelEnum getByType(Integer type) {
        return Lists.newArrayList(values()).stream().filter(val -> val.getType().equals(type)).collect(Collectors.toList()).get(0);
    }

    public static ChannelEnum getByApiName(String apiName) {
        if (CrmObjectApiNameEnum.CRM_LEAD.getName().equals(apiName)) {
            return CRM_LEAD;
        }
        if (CrmObjectApiNameEnum.CONTACT.getName().equals(apiName)) {
            return CRM_CONTACT;
        }
        if (CrmObjectApiNameEnum.CUSTOMER.getName().equals(apiName)) {
            return CRM_ACCOUNT;
        }
        if (CrmObjectApiNameEnum.WECHAT.getName().equals(apiName)) {
            return CRM_WX_USER;
        }
        if(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName().equals(apiName)){
            return CRM_WX_WORK_EXTERNAL_USER;
        }
        if (CrmObjectApiNameEnum.MEMBER.getName().equals(apiName)) {
            return CRM_MEMBER;
        }

        return null;
    }

    public static List<String> getAllChannelApiName() {
        return Lists.newArrayList(CrmObjectApiNameEnum.CRM_LEAD.getName(), CrmObjectApiNameEnum.CONTACT.getName(), CrmObjectApiNameEnum.CUSTOMER.getName(), CrmObjectApiNameEnum.WECHAT.getName(), CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), CrmObjectApiNameEnum.MEMBER.getName());
    }

    public String getApiName() {
        if (this.getType().equals(CRM_LEAD.getType())) {
            return CrmObjectApiNameEnum.CRM_LEAD.getName();
        } else if (this.getType().equals(CRM_CONTACT.getType())) {
            return CrmObjectApiNameEnum.CONTACT.getName();
        } else if (this.getType().equals(CRM_ACCOUNT.getType())) {
            return CrmObjectApiNameEnum.CUSTOMER.getName();
        } else if (this.getType().equals(CRM_WX_USER.getType())) {
            return CrmObjectApiNameEnum.WECHAT.getName();
        } else if(this.getType().equals(CRM_WX_WORK_EXTERNAL_USER.getType())){
            return CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName();
        } else if(this.getType().equals(CRM_MEMBER.getType())){
            return CrmObjectApiNameEnum.MEMBER.getName();
        }
        return null;
    }
}
