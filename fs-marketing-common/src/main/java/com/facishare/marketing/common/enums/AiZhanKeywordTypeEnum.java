package com.facishare.marketing.common.enums;

import lombok.Getter;

@Getter
public enum AiZhanKeywordTypeEnum {

    // 0 小程序  1 H5   2 web
    MOBILE_KEYWORD(1, 1,"移动端关键词"),
    MOBILE_RANK_DOWN_KEYWORD(1, 2,"移动端跌出词"),
    MOBILE_RANK_UP_KEYWORD(1, 3,"移动端涨入词"),
    PC_KEYWORD(2, 1,"客户端关键词"),
    PC_RANK_DOWN_KEYWORD(2, 2,"客户端跌出词"),
    PC_RANK_UP_KEYWORD(2, 3,"客户端涨入词"),


    ;
    private int client; //1移动端 2 客户端

    private int type; //1关键词  2跌出词  3涨入词

    private String desc;

    AiZhanKeywordTypeEnum(int client,int type, String desc) {
        this.client = client;
        this.type = type;
        this.desc = desc;
    }


}
