package com.facishare.marketing.common.enums;

import lombok.Getter;

@Getter
public enum ExportAdEncryptionEnum {
    MD5("md5", "md5"),
    SHA256("sha256", "sha256"),
    ;


    ExportAdEncryptionEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private String type;

    private String desc;


    public static ExportAdEncryptionEnum getByType(String type) {
        for (ExportAdEncryptionEnum exportAdEncryptionEnum : ExportAdEncryptionEnum.values()) {
            if (exportAdEncryptionEnum.getType().equals(type)) {
                return exportAdEncryptionEnum;
            }
        }
        return null;
    }
}
