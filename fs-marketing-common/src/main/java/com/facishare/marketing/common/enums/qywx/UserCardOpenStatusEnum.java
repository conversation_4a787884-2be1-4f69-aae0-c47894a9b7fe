package com.facishare.marketing.common.enums.qywx;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created  By zhoux 2020/04/14
 **/
@AllArgsConstructor
@Getter
public enum UserCardOpenStatusEnum {

    ALL(0, "全部"),

    OPEN(1, "已开通"),

    NOT_OPEN(2, "未开通");

    private Integer status;

    private String desc;

    public static UserCardOpenStatusEnum getByName(String name) {
        for (UserCardOpenStatusEnum value : UserCardOpenStatusEnum.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }

}
