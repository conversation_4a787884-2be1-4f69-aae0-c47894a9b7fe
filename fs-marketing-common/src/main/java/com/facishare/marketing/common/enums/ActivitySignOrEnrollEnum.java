package com.facishare.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ActivitySignOrEnrollEnum {
    //签到 1:未报名 2:已签到 3:未签到 4:无需签到 5:未审核通过

    NOT_ENROLL_IN(1, "未报名", null),
    SIGN_IN(2, "已签到", "sighed"),
    NOT_SIGN_IN(3, "未签到", "unsigned"),
    NOT_NEED_SIGN(4, "无需签到", null),
    NOT_REVIEW_SUCCESS(5, "未审核通过", null),
    NOT_REVIEW_FAIL(6, "审核失败-未通过", null),
    NOT_REVIEW_AUDITING(7, "审核中", null),
    ;
    private Integer type;
    private String desc;
    private String CampaignMergeDataApiName;

    public static String getTypeDesc(Integer type) {
        for (ActivitySignOrEnrollEnum value : ActivitySignOrEnrollEnum.values()) {
            if(value.getType().equals(type)){
                return value.getDesc();
            }
        }
        return null;
    }

    public static ActivitySignOrEnrollEnum getByType(Integer type) {
        for (ActivitySignOrEnrollEnum value : ActivitySignOrEnrollEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    public static ActivitySignOrEnrollEnum getByCampaignMergeDataApiName(String apiName){
        for (ActivitySignOrEnrollEnum value : ActivitySignOrEnrollEnum.values()) {
            if (value.getCampaignMergeDataApiName() != null && value.getCampaignMergeDataApiName().equals(apiName)) {
                return value;
            }
        }
        return null;
    }
}

