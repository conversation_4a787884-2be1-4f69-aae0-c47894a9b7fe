package com.facishare.marketing.common.enums.baidu;

import lombok.Getter;

/**
 * Created by ranluch on 2019/11/25.
 * 实时数据类型
 */
@Getter
public enum ReportTypeEnum {
    ACCOUNT(2, "账户"),
    REGION(3, "地域"),
    SUB_REGION(5, "二级地域报告"),
    WORD(9, "关键词(wordid)"),
    CAMPAIGN(10, "计划"),
    UNIT(11, "单元"),
    CREATIVE(12, "创意"),
    KEYWORD(14, "关键词(keywordid)"),
    ROUTE(21, "蹊径报告"),
    history_rank(38, "历史数据排名报告"),
    AD_GROUP_FEED(702, "信息流-单元"),
    ;

    private int type;
    private String desc;

    ReportTypeEnum(int type, String desc){
        this.type = type;
        this.desc = desc;
    }
}
